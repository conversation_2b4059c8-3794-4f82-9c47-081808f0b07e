<template>
  <div class="todoTable-container">
    <!-- 表头，支持插槽 -->
    <header class="headerWrapper flex" :style="headerStyle">
      <div
        v-for="column in columns"
        :key="column.prop"
        class="colWrapper"
        :style="{ ...colWidth(column), textAlign: column?.align || 'center' }"
      >
        <template v-if="column?.slotLabel">
          <slot :name="column.slotLabel"></slot>
        </template>
        <span v-else :style="headerCellStyle">{{ column.label }}</span>
      </div>
    </header>

    <!-- 内容区，滚动到底加载下一页，支持插槽 -->
    <el-scrollbar class="contentWrapper">
      <div
        v-infinite-scroll="loadData"
        :infinite-scroll-disabled="loading || finished"
        class="h-full"
      >
        <template v-if="tableData.length">
          <div
            v-for="(row, rowIndex) in tableData"
            :key="rowIndex"
            :class="[
              'rowWrapper flex',
              { stripeBg: stripe && rowIndex % 2 === 0 },
            ]"
            @click="
              $emit('rowClick', row, Math.ceil(rowIndex / pageSize), pageSize)
            "
          >
            <!-- 鼠标移入打点的列，展示文本提示 -->
            <el-tooltip
              v-for="column in columns"
              :key="column.prop"
              :content="row[column.prop]"
              placement="top"
              :show-after="300"
              :visible="row[`visibleKey_${column.prop}`]"
            >
              <div
                class="colWrapper truncate"
                :style="{
                  ...colWidth(column),
                  textAlign: column?.align || 'center',
                }"
                @mouseenter="
                  row[`visibleKey_${column.prop}`] =
                    $event.target.scrollWidth > $event.target.clientWidth
                "
                @mouseleave="row[`visibleKey_${column.prop}`] = false"
              >
                <template v-if="column.slotName">
                  <slot
                    :name="column.slotName"
                    :row="row"
                    :index="rowIndex"
                  ></slot>
                </template>
                <span v-else>{{ row[column.prop] || "-" }}</span>
              </div>
            </el-tooltip>
          </div>
          <p class="rowWrapper text-center" v-if="loading">加载中...</p>
          <p class="rowWrapper text-center" v-if="finished">没有更多数据</p>
        </template>
        <div v-else class="rowWrapper text-center">暂无数据</div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { pxToVw, stringToNumber } from "@/utils/index.js";

const props = defineProps({
  // 列配置
  columns: {
    type: Array,
    required: true,
  },
  // 接口信息
  interfaceInfo: {
    type: Object,
    default: () => ({}),
  },
  // 表头整体样式
  headerStyle: {
    type: Object,
    default: () => ({}),
  },
  // 表头每个单元格样式
  headerCellStyle: {
    type: Object,
    default: () => ({}),
  },
  // 是否使用斑马纹
  stripe: {
    type: Boolean,
    default: false,
  },
});

defineEmits(["rowClick"]);

const currentPage = ref(1); // 当前页
const pageSize = ref(15); // 页大小
const loading = ref(false); // 加载状态
const tableData = ref([]); // 表格数据
const total = ref(0); // 总条数
const finished = ref(false); // 数据是否加载完

// 列宽
const colWidth = computed(() => (col) => {
  if (col?.width) {
    const widthValue = stringToNumber(col.width);
    const w = isNaN(widthValue) ? pxToVw(col.width) : pxToVw(widthValue);
    return { width: w };
  } else {
    return { flex: 1 };
  }
});

/**
 * 滚动至底部时，加载数据
 */
const loadData = async () => {
  loading.value = true;
  try {
    const param = {
      ...props.interfaceInfo.params,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };
    const res = await props.interfaceInfo.api(param);
    if (res.code === 200) {
      let t = res?.total || res?.data?.total;
      let records =
        res?.rows || res?.records || res?.data?.records || res?.data?.rows;
      records = props.interfaceInfo.getDataMap(records) || records;
      tableData.value = [...tableData.value, ...records];
      total.value = t;

      if (tableData.value.length >= total.value) {
        finished.value = true;
      } else {
        currentPage.value++;
      }
      console.log("是否加载完：", finished.value);
    }
  } catch (error) {
    console.error("加载时出错：", error);
    finished.value = true;
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.interfaceInfo.params,
  (val) => {
    if (val) {
      tableData.value = [];
      currentPage.value = 1;
      finished.value = false;
      loadData();
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;

.todoTable-container {
  width: 100%;
  height: 100%;
  .headerWrapper {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: px2vw(13);
    color: #b5bfc8;
    line-height: px2vw(18);
    padding: px2vw(7) 0;
  }

  .contentWrapper {
    height: calc(100% - px2vw(32));
    overflow: auto;

    .rowWrapper {
      height: px2vw(51);
      line-height: px2vw(51);
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: px2vw(14);
      color: #e3f7ff;
      cursor: pointer;

      &.stripeBg {
        background: url("@/assets/images/home/<USER>") no-repeat
          center/cover;
      }
    }
  }

  .colWrapper {
    padding: 0 px2vw(10);
  }
}
</style>
