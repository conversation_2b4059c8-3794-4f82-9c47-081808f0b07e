export const applicationColumns = [
    {
        label: '序号',
        type: 'index',
        width: '60px',
    },
    {
        prop: 'name',
        label: '系统名称',
        align: 'left',
    },
    {
        prop: 'url',
        label: '系统地址',
        align: 'left',
    },
    {
        prop: 'icon',
        label: '图标',
        align: 'left',
        slotName: 'iconSlot',
    },
    {
        prop: 'sysClientType',
        label: '系统类型',
        align: 'left',
        slotName: 'sysClientTypeSlot',
    },
    {
        prop: 'status',
        label: '是否启用',
        align: 'left',
        slotName: 'isEnableSlot',
    }
]

export const operationFormRule = {
    name: [{
        required: true,
        message: '请输入系统名称',
        trigger: 'blur'
    }],
    url: [{
        required: true,
        message: '请输入系统地址',
        trigger: 'blur'
    }],
    // icon: [{
    //     required: true,
    //     message: '请输入图标',
    //     trigger: 'blur'
    // }],
    status: [{
        required: true,
        message: '请选择是否启用',
        trigger: 'change'
    }],
    sysClientType: [{
        required: true,
        message: '请选择系统类型',
        trigger: 'change'
    }],
}
