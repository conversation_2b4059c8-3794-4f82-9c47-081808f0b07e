<template>
  <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63 (92445) - https://sketch.com -->
    <title>箭头</title>
    <desc>Created with Sketch.</desc>
    <defs>
      <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-1">
        <stop stop-color="#4791F8" offset="0%"></stop>
        <stop stop-color="#4791F8" stop-opacity="0.29611014" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g id="互联网应用服务（外网）" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
      <g id="添加便签" transform="translate(-902.000000, -541.000000)" fill="url(#linearGradient-1)" fill-rule="nonzero">
        <g id="编组-14" transform="translate(488.000000, 224.000000)">
          <g id="箭头" transform="translate(414.000000, 317.000000)">
            <path d="M19.75412,1.98951966e-13 L36,18 L19.75412,36 L19.754,26.775 L-3.97903932e-13,26.775 L-3.97903932e-13,9.225 L19.754,9.225 L19.75412,1.98951966e-13 Z" id="形状结合"></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
</template>

<script setup>

</script>

<style scoped lang="less">

</style>
