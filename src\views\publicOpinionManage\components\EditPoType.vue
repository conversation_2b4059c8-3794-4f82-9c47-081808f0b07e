<template>
  <div>
    <el-select
      v-model="poTypeF"
      :placeholder="placeholder"
      clearable
      filterable
      ref="poTypeSelectRef"
    >
      <!-- 后端确保label唯一 -->
      <el-option
        v-for="item in opinionList"
        :key="item.label"
        :label="item.label"
        :value="item.value"
      />
      <template #footer>
        <el-button type="primary" link icon="Plus" @click="addPoType"
          >添加类型</el-button
        >
      </template>
    </el-select>

    <!-- 舆情平台/类型新增弹窗 -->
    <PlatFormOrPoTypeDialog
      v-model="PlatFormOrPoTypeVisible"
      add-type="publicOpinion"
      @refreshSelectList="getOpinionList"
    />
  </div>
</template>

<script setup>
import { listData } from "@/api/system/dict/data";
import PlatFormOrPoTypeDialog from "./PlatFormOrPoTypeDialog.vue";

const props = defineProps({
  modelValue: {
    type: [String, null],
    required: true,
  },
  placeholder: {
    type: String,
    default: "请选择舆情类型",
  },
});

const emit = defineEmits(["update:modelValue"]);

const poTypeF = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit("update:modelValue", val);
  },
});

const poTypeSelectRef = ref(null);
const opinionList = ref([]);
const PlatFormOrPoTypeVisible = ref(false);

/**
 * 获取舆情类型数据
 */
const getOpinionList = () => {
  listData({ dictType: "opinion_type" }).then((response) => {
    opinionList.value = response.rows.map((ele) => ({
      value: ele.dictValue,
      label: ele.dictLabel,
    }));
  });
};

/**
 * 舆情类型添加按钮
 */
function addPoType() {
  poTypeSelectRef.value.blur();
  PlatFormOrPoTypeVisible.value = true;
}

getOpinionList();
</script>

<style lang="scss" scoped></style>
