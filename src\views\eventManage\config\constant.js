/**
 * 是否首报
 */
export const IS_FIRST_STATUS = {
  YES: "1", // 是
  NO: "0" // 否
};

export const IS_FIRST_STATUS_LIST = [
  { label: "是", value: IS_FIRST_STATUS.YES },
  { label: "否", value: IS_FIRST_STATUS.NO }
];

/**
 * 是否为敏感舆情
 */
export const SENSITIVE_STATUS = {
  YES: "1", // 是
  NO: "0" // 否
};

export const SENSITIVE_STATUS_LIST = [
  { label: "是", value: SENSITIVE_STATUS.YES },
  { label: "否", value: SENSITIVE_STATUS.NO }
];

/**
 * 是否无效
 */
export const INVALID_STATUS = {
  YES: true, // 是
  NO: false // 否
};

/**
 * 是否谣言
 */
export const RUMOR_STATUS = {
  YES: "1", // 是
  NO: "0" // 否
};

/**
 * 贴文状态
 */
export const ARCTICLE_STATUS = {
  NO_DEL: "0", // 未删除
  FINISH_DEL: "1" // 已删除
};

export const ARCTICLE_STATUS_LIST = [
  { label: "未删除", value: ARCTICLE_STATUS.NO_DEL, color: "#333333" },
  { label: "已删除", value: ARCTICLE_STATUS.FINISH_DEL, color: "#D9001B" }
];

/**
 * 处置状态
 */
export const HANDLE_STATUS = {
  WAIT_HANDLE: "0", // 待研判
  FINISH_HANDLE: "1" // 已处置
};

export const HANDLE_STATUS_LIST = [
  { label: "待研判", value: HANDLE_STATUS.WAIT_HANDLE, color: "#0052D9", bgColor: "#E6ECFF" },
  { label: "已处置", value: HANDLE_STATUS.FINISH_HANDLE, color: "#2BBC13", bgColor: "#E1FAF2" }
];

/**
 * 处理状态
 */
export const HANDLE2_STATUS = {
  WAIT_HANDLE: ["TO_CHECK", "TO_REPORT", "TO_RESOLVE"], // 待处理
  ING: ["CHECKING", "REPORTING", "RESOLVING"], // 跟进中
  FINISH_HANDLE: ["CHECKED", "REPORTED", "RESOLVED"], // 已处理
  WAIT_VIEW: ["TO_VIEW"], // 待查看
  FINISH_VIEW: ["VIEWED"] // 已查看
};


export const HANDLE2_STATUS_LIST = [
  { label: "待处理", value: HANDLE2_STATUS.WAIT_HANDLE, color: "#0052D9" },
  { label: "跟进中", value: HANDLE2_STATUS.ING, color: "#F59A23" },
  { label: "已处理", value: HANDLE2_STATUS.FINISH_HANDLE, color: "#2BBC13" },
  { label: "待查看", value: HANDLE2_STATUS.WAIT_VIEW, color: "#0052D9" },
  { label: "已查看", value: HANDLE2_STATUS.FINISH_VIEW, color: "#2BBC13" }
];

/**
 * 是否逾期
 */
export const OVERDUE_STATUS = {
  YES: "2", // 是
  NO: "1" // 否
};

export const OVERDUE_STATUS_LIST = [
  { label: "是", value: OVERDUE_STATUS.YES },
  { label: "否", value: OVERDUE_STATUS.NO }
];

/**
 * 个人任务状态
 */
export const SELF_TASK_STATUS = {
  NO_HANDLE: "0", // 未处理
  FINISH_HANDLE: "1" // 已处理
};

export const SELF_TASK_STATUS_LIST = [
  { label: "未处理", value: SELF_TASK_STATUS.NO_HANDLE },
  { label: "已处理", value: SELF_TASK_STATUS.FINISH_HANDLE }
];
