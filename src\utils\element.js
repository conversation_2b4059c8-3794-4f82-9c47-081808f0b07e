/**
 * 监听目标元素动态加载完毕
 */
export function onElementReady(selector, callback, options) {
  const { container = document.body, observeOnce = true } = options;
  let observer = null;

  // 检查目标元素是否已经存在
  const targetElement = container.querySelector(selector);
  if (targetElement) {
    callback(targetElement);
    if (observeOnce) {
      return () => {}; // 如果元素已经存在且只需观察一次，直接返回空清理函数
    }
  }

  // 创建 MutationObserver 实例
  observer = new MutationObserver(() => {
    const targetElement = container.querySelector(selector);
    if (targetElement) {
      callback(targetElement);

      // 如果只需观察一次，停止观察
      if (observeOnce) {
        observer?.disconnect();
      }
    }
  });

  // 开始观察容器内的变化
  observer.observe(container, { childList: true, subtree: true });

  // 返回一个清理函数
  return () => {
    observer?.disconnect();
  };
}
