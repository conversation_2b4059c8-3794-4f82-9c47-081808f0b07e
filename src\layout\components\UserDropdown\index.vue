<template>
  <div class="userDropdown-container">
    <el-dropdown
      @visible-change="(val) => (showDropdown = val)"
      class="right-menu-item hover-effect"
      :popper-class="popperClass"
    >
      <div class="avatar-wrapper">
        <img
          src="@/assets/images/avatar.svg"
          alt="avatar"
          :style="avatarStyle"
        />
        <span :style="fontStyle" class="ml-[10px]"
          >{{ userStore.nickname }}，{{ getMoment() }}</span
        >
        <el-icon :style="fontStyle" :class="{ rotateIcon: showDropdown }"
          ><caret-bottom
        /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <!-- <el-dropdown-item>
            <span @click="openApp(userCenterUrl)">个人中心</span>
          </el-dropdown-item> -->
          <!-- <router-link to="/mywork/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link> -->
          <el-dropdown-item>
            <span class="dropdownItem w-[100px]" @click="logout">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import useUserStore from "@/store/modules/user";
import { openApp } from "@/utils/windowUtils";
import { getLinks } from "@/api/home/<USER>";
import { pxToVw } from "@/utils/index.js";
import { getMoment } from "@/utils/index.js";

defineProps({
  popperClass: {
    type: String,
    default: "",
  },
  fontStyle: {
    type: String,
    default: () => {
      return {
        color: "#000",
      };
    },
  },
  avatarStyle: {
    type: String,
    default: () => {
      return {
        width: pxToVw(30),
      };
    },
  },
});

const userStore = useUserStore();

const showDropdown = ref(false);

// onMounted(() => {
//   handleGetLinks();
// });

const userCenterUrl = ref("");
// 获取个人中心链接
async function handleGetLinks() {
  try {
    const { data } = await getLinks();
    const userCenterLink = data.find((link) => link.appName === "个人中心");
    if (userCenterLink) {
      userCenterUrl.value = userCenterLink.appUrl;
    } else {
      console.warn("未找到个人中心链接");
    }
  } catch (e) {
    console.error("获取链接失败:", e);
  }
}

/**
 * 退出登录
 */
function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = "/patrol-opinion/index";
      });
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped>
.userDropdown-container {
  position: relative;
  display: flex;
  align-items: center;

  .right-menu-item {
    display: inline-block;
    font-size: 18px;
    color: #ffffff;
    vertical-align: text-bottom;

    &.hover-effect {
      cursor: pointer;
      transition: background 0.3s;

      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }
    }
  }

  .avatar-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 5px;
    padding-right: 35px;

    span {
      font-size: 13px;
    }
    .user-avatar {
      cursor: pointer;
      width: 30px;
      height: 30px;
      border-radius: 20px;
      border: 1px solid #ffffff;
      box-shadow: #5a5e66 1px 2px 5px;
    }

    i {
      cursor: pointer;
      position: absolute;
      right: 0px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;

      &.rotateIcon {
        transform: rotate(180deg) translateY(50%);
      }
    }
  }
}
</style>
