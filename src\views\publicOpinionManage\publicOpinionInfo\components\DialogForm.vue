<template>
  <div class="dialogForm-container">
    <div
      v-if="dialogType === 'batch' && typeText !== TRANSFER_TYPE.VIEW"
      class="mb-[16px]"
    >
      <div class="formTitle">舆情完善</div>
      <el-form
        ref="impoveFormRef"
        :model="improveForm"
        label-position="top"
        label-width="auto"
        :inline="false"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属事件">
              <el-select
                v-model="improveForm.poEvent"
                multiple
                placeholder="请选择所属事件"
                clearable
              >
                <el-option
                  v-for="item in poManageStore.ingPoEventList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="舆情属地">
              <el-cascader
                clearable
                v-model="improveForm.happenLocation"
                :options="pcaTextArr"
                placeholder="请选择舆情属地"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="formTitle">舆情指派</div>
    <el-form
      ref="addFormRef"
      :model="formData"
      :rules="typeText === '研判' ? rules1 : rules2"
      label-position="top"
      label-width="auto"
      :inline="false"
    >
      <el-row v-for="(item, index) in formArr" :key="index">
        <el-col :span="12">
          <el-form-item
            :label="`${formArr.length === 1 ? '负责' : item}单位`"
            :prop="`charges${index + 1}`"
          >
            <el-tree-select
              v-model="formData[`charges${index + 1}`]"
              lazy
              :load="loadTreeData"
              :props="{
                value: 'value',
                label: 'label',
                children: 'children',
                isLeaf: 'isLeaf',
              }"
              node-key="value"
              multiple
              :render-after-expand="false"
              show-checkbox
              searchable
            >
            </el-tree-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="item !== TRANSFER_TYPE.VIEW"
            label="截止时间"
            :prop="`deadlineTime${index + 1}`"
          >
            <el-date-picker
              v-model="formData[`deadlineTime${index + 1}`]"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择截止时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { getDeptUserList } from "@/api/poManage/poInfo";
import { TRANSFER_TYPE } from "../../config/mapRel";
import { PO_TRANSFER_SORT } from "../config/constant";
import { pcaTextArr } from "element-china-area-data";
import { usePoManageStore } from "@/store/modules/poManage.js";

const poManageStore = usePoManageStore();
poManageStore.getAllPoEventList();

const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
  improveForm: {
    type: Object,
    default: () => ({}),
  },
  typeText: {
    type: String,
    required: true,
  },
  dialogType: {
    type: String,
    default: "single",
  },
});

const addFormRef = ref();
const rules1 = reactive({
  deadlineTime1: [{ validator: deadlineTimeV, trigger: "change" }],
  deadlineTime2: [{ validator: deadlineTimeV, trigger: "change" }],
  deadlineTime3: [{ validator: deadlineTimeV, trigger: "change" }],
});
const rules2 = reactive({
  charges1: [{ required: true, trigger: "change", message: "请选择负责单位" }],
  deadlineTime1: [{ validator: deadlineTimeV, trigger: "change" }],
});

const formArr = computed(() => {
  return props.typeText === "研判" ? PO_TRANSFER_SORT : [props.typeText];
});

/**
 * 加载部门人员树结构数据
 */
async function loadTreeData(node, resolve) {
  try {
    const parentId =
      JSON.stringify(node.data) !== "{}" ? node.data.value : null; // Root node has deptId as '0'
    const isDept = JSON.stringify(node.data) !== "{}" ? node.data.isDept : null;

    const response = await getDeptUserList({
      deptId: parentId,
      isDept: isDept,
    });

    const { data } = response;

    const nodes = data.map((user) => ({
      value: user.id,
      label: user.name,
      isDept: user.isDept,
      isLeaf: !user.isDept,
      // disabled: user.isDept,
      deptId: parentId,
      // children: [],
    }));

    resolve(nodes);
  } catch (error) {
    console.error("Failed to load data:", error);
    resolve([]);
  }
}

/**
 * 截止时间校验
 */
function deadlineTimeV(rule, value, callback) {
  if (value && new Date(value).getTime() <= new Date().getTime()) {
    callback(new Error("截止时间必须大于当前时间"));
  } else {
    callback();
  }
}

/**
 * 重置数据
 */
function reset() {
  addFormRef.value.resetFields(); // 重置表单
}

defineExpose({
  addFormRef,
  reset,
});
</script>

<style lang="scss" scoped>
.dialogForm-container {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px 4px 25px 20px;

  .formTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #1f2329;
    line-height: 20px;
    margin-bottom: 16px;
  }

  :deep(.el-form) {
    /*display: flex;*/
    flex-wrap: wrap;

    .el-form-item {
      width: 100%;
      padding-right: 14px;
      margin-bottom: 11px;
    }

    .el-form-item__label {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #1f2329;
      line-height: 32px;
      margin-bottom: 3px;
    }
  }
}
</style>
