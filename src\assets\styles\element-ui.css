@charset "UTF-8";
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type="file"] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.cell .el-tag {
  margin-right: 0px;
}

.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width .el-button--mini {
  padding: 7px 10px;
  width: 60px;
}

.status-col .cell {
  padding: 0 10px;
  text-align: center;
}

.status-col .cell .el-tag {
  margin-right: 0px;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  margin-top: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.el-dialog .el-dialog__header {
  text-align: left;
  padding: 0 20px 20px 10px;
}

.el-dialog .el-dialog__headerbtn {
  top: 8px;
}

.el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
  padding-right: 5px;
}

.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block;
}

.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
> div
> .el-submenu
> .el-submenu__title
.el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

.app-wrapper .el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background-color: #f2f3f6 !important;
}

.el-icon.el-breadcrumb__separator {
  color: grey;
}

.el-table__body-wrapper .el-scrollbar__wrap--hidden-default {
  max-height: calc(100vh - 450px);
}

.el-dialog__body {
  max-height: 75vh;
  overflow: auto;
  padding-top: 10px;
}

.el-button--primary.is-plain:focus {
  background-color: transparent;
  border-color: transparent;
  color: #409eff;
}

.el-message-box__message {
  padding-left: 15px !important;
}

.el-message-box__status {
  position: relative;
  transform: translateY(0px);
}

.el-table {
  color: #000000 !important;
}

.el-menu-item {
  color: #000000 !important;
}

.el-tree {
  color: #000000 !important;
}

.vxe-table .vxe-table--header-wrapper {
  color: #000000 !important;
}

.vxe-table--render-default {
  color: #000000 !important;
  font-family: Helvetica Neue,
 Helvetica,
 Hiragino Sans GB,
 Microsoft YaHei,
 Arial,
 PingFang SC,
 sans-serif,
 sans-serif !important;
}

.vxe-select {
  color: #000000 !important;
}

.vxe-input--inner {
  color: #000000 !important;
}

.vxe-select--panel {
  color: #000000 !important;
}

.el-popper.is-dark {
  --el-text-color-primary: #1f232a;
  box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.14);
  border-radius: 10px;
}

.el-popper.is-dark > span:first-child {
  display: block;
  max-width: 430px;
  /* 设置 Tooltip 的最大宽度 */
  max-height: 100px;
  /* 设置 Tooltip 的最大宽度 */
  overflow: auto;
  white-space: pre-wrap;
  /* 保留文本中的换行符 */
  word-break: break-all;
}

.el-popper.is-dark ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.el-popper.is-dark ::-webkit-scrollbar-track {
  background: transparent;
}

.el-popper[data-popper-placement^="bottom"] .el-popper__arrow:before {
  border-bottom-color: transparent !important;
  border-right-color: transparent !important;
}

.el-popper.is-light {
  border: none;
}

.el-popper.is-light .el-popper__arrow:before {
  background: transparent !important;
  border: 1px solid transparent;
}
