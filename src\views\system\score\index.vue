<template>
  <div class="app-container kq-custom">
    <div class="app-title">{{ pageTitle }}</div>
    <!-- <el-form
      @submit.native.prevent
      :model="queryParams"
      ref="kqQueryRef"
      :inline="true"
      label-width=""
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item>
            <el-select
              v-model="queryParams.businessCategory"
              @change="handleQuery"
              placeholder="请选择业务工作"
              filterable
              style="width: 200px"
            >
              <el-option
                v-for="dict in projectList"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
    <!-- <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item> -->
    <!-- </el-form> -->
    <vxe-table
      class="table-all"
      border="inner"
      align="left"
      :data="tableData"
      :loading="loading"
      :column-config="{ resizable: true, minWidth: '110px' }"
      keep-source
      :show-overflow="true"
      :row-config="{
        useKey: true,
        keyField: 'id',
      }"
      ref="tableRef"
      :edit-config="{
        showUpdateStatus: true,
        trigger: 'click',
        mode: 'cell',
        enabled: canEdit(['system:scoreConfig:save']),
      }"
      :scroll-y="{
        enabled: true,
        mode: 'wheel',
      }"
      header-row-class-name="header-custom"
      header-cell-class-name="row-custom"
      @edit-closed="editClosedEvent"
    >
      <vxe-column
        field="orderNum"
        title="序号"
        fixed="left"
        width="80"
        align="center"
      ></vxe-column>
      <!-- <vxe-column field="itemName" title="业务工作项目名称"> -->
      <!-- <template #default>{{ cName }}</template> -->
      <!-- </vxe-column> -->
      <vxe-column
        field="baseScore"
        title="基础分值"
        align="center"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-input
            type="number"
            @change=""
            v-model="row['baseScore']"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="itemName"
        title="赋分项目名称"
        :edit-render="{}"
        :sort-config="sortConfig"
      >
        <template #edit="{ row }">
          <vxe-input @change="" v-model="row['itemName']"></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="scorePointName" title="赋分点名称" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input @change="" v-model="row['scorePointName']"></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="scorePointValue"
        title="赋分点分值"
        align="center"
        type="number"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-input @change="" v-model="row['scorePointValue']"></vxe-input>
        </template>
      </vxe-column>

      <vxe-column field="" title="操作">
        <template #default="{ row }">
          <el-button
            type="primary"
            color="#4395F9"
            plain
            size="small"
            link
            @click="handleCopy(row)"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            color="#4395F9"
            plain
            size="small"
            link
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </vxe-column>
      <template #empty>
        <el-empty description="暂无数据~" />
      </template>
    </vxe-table>
    <pagination
      class="fix-pagination"
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />
  </div>
</template>

<script setup>
import { getList, save, del, updateSort } from "@/api/score/index";
import useUserStore from "@/store/modules/user";
const { proxy } = getCurrentInstance();
const { public_opinion_report_category } = proxy.useDict(
  "public_opinion_report_category"
);
const route = useRoute();
const pageTitle = route.meta.title;
const kqQueryRef = ref(null);
const tableRef = ref(null);
const loading = ref(false);
const total = ref(0);

const sortConfig = {
  field: "orderNum",
  order: "desc",
};
const queryParams = reactive({
  // pageNum: pageConfig.pageNum,
  // pageSize: pageConfig.pageSize,
  businessCategory: "1",
});
const tableData = ref([]);
const tableHeight = "500px";
// 提取所有不重复的日期
// 保存数据 实时
const editClosedEvent = async ({ row, column }) => {
  const $table = tableRef.value;
  if ($table) {
    const field = column.field;
    const cellValue = row[field];
    // console.log({field,cellValue});
    // 判断单元格值是否被修改
    if ($table.isUpdateByRow(row, field)) {
      // 保存数据
      let params = {
        ...row,
      };
      if (typeof params.id === "string" && params.id.startsWith("row_")) {
        delete params.id;
      }
      params[field] = cellValue;
      await save(params);
      if (params.add) {
        let res = await setSort(params);
      }
      ElMessage.success("保存成功");
      // 局部更新单元格为已保存状态
      await handleQuery();
    }
  }
};

// 请求列表
const handleQuery = async () => {
  let params = {
    // ...queryParams,
  };
  loading.value = true;
  return getList(params)
    .then((res) => {
      tableData.value = res.data;
      if (res.data?.length === 0) {
        tableData.value = [
          {
            // businessCategory: queryParams.businessCategory,
            baseScore: 0,
            orderNum: 1,
            scorePointName: "",
            scorePointValue: 0,
          },
        ];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
const setSort = async (params) => {
  loading.value = true;
  let list = tableData.value;
  let data = [];
  for (let index = params.RowIndex + 2; index < list.length; index++) {
    const element = list[index];
    data.push({
      id: element.id,
      orderNum: index + 1,
    });
  }
  if (data.length === 0) {
    loading.value = false;
    return "noData";
  }
  await updateSort(data).finally(() => {
    loading.value = false;
  });
};
onMounted(async () => {
  await handleQuery();
});

// 编辑权限
const canEdit = (str) => {
  const all_permission = "*:*:*";
  const permissions = useUserStore().permissions;

  if (str && str instanceof Array && str.length > 0) {
    const permissionFlag = str;

    const hasPermissions = permissions.some((permission) => {
      return (
        all_permission === permission || permissionFlag.includes(permission)
      );
    });

    if (!hasPermissions) {
      return false;
    }
  } else {
    throw new Error(`请设置操作权限标签值`);
  }
};
const handleDelete = (row) => {
  if (tableData.value.length === 1) {
    ElMessage.warning("至少保留一条数据");
    return;
  }
  ElMessageBox.confirm("此操作将删除此信息, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    if (typeof row.id === "string" && row.id.startsWith("row_")) {
      // 删除此行 row
      tableData.value.splice(
        tableData.value.findIndex((item) => item.id === row.id),
        1
      );
    } else {
      let data = { ids: [row.id] };
      const res = await del(data);
      await handleQuery();
    }
    ElMessage.success("删除成功");
  });
};
const handleCopy = async (row) => {
  // 在当前行下方加一条空数据
  if (testModeData.value.length >= 1) {
    let it = testModeData.value[0];
    ElMessage.warning(`请先填写完成序号${it.orderNum}的数据`);
    return;
  }
  let params = {};
  let sort = Number(row.orderNum);
  let currentRowIndex = tableData.value.findIndex((item) => item.id === row.id);
  (params.businessCategory = queryParams.businessCategory),
    (params.itemName = "");
  params.scorePointName = "";
  params.scorePointValue = 0;
  params.orderNum = sort + 1;
  params.add = true;
  params.RowIndex = currentRowIndex;
  const $table = tableRef.value;
  // const { row: newRow } = await $table.insertAt(params, nextRow)
  tableData.value.splice(currentRowIndex + 1, 0, params);
  nextTick(async () => {
    let inserItem = tableData.value[currentRowIndex + 1];
    await $table.setEditCell(inserItem, "itemName");
  });
  // ElMessage.success('复制成功');
};
const testModeData = computed(() => {
  return tableData.value.filter((item) => {
    let type = typeof item.id;
    if (type === "string" && item.id.startsWith("row_")) {
      return true;
    } else {
      return false;
    }
  });
});
const confirmClick = async () => {
  ElMessage.success("保存成功");
};
const resetData = () => {
  tableData.value = [{ name: "", score: 1 }];
};
</script>

<style lang="scss" scoped>
.kq-custom {
  :deep(.el-dropdown) {
    font-size: 14px;
  }

  :deep(.header-custom) {
    background-color: #f2f3f6;
  }

  :deep(.row-custom) {
    height: 42px !important;
    padding: 8px 0;
  }

  :deep(.vxe-table--border-line) {
    border: none;
  }
}
.table-all {
  :deep(.vxe-table--body-wrapper) {
    max-height: calc(100vh - v-bind("tableHeight"));
  }
}
$scroll-bar-height: 6px;
:deep(.vxe-table--fixed-left-wrapper) {
  .fixed-left--wrapper {
    padding-bottom: $scroll-bar-height;
  }
}
</style>
