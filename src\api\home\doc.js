import request from '@/utils/request'
// 新增文件夹或文件
export function addDoc(data) {
    return request({
        url: '/system/folders',
        method: 'post',
        data
    })
}
// 根据父类ID获取文件或文件夹
export function getDoc(data) {
    return request({
        url: '/system/folders/list',
        method: 'post',
        data
    })
}
// 获取文档树结构
export function getDocTree(data) {
    return request({
        url: '/system/folders/documentTree',
        method: 'post',
        data
    })
}