<template>
  <div id="echarts" ref="chartRef" :style="echartsStyle" />
</template>

<script setup>
import {ref, onMounted, onBeforeUnmount, watch, computed, markRaw, nextTick} from "vue";
import echarts from "@/common/components/ECharts/echarts";
import lodash from 'lodash'
import 'echarts/lib/component/graphic'

const props = defineProps({
  option: {
    type: Object,
    default: '',
    required: true
  },
  renderer: {
    type: String,
    default: 'canvas',
    required: false
  },
  resize: {
    type: Boolean,
    default: true,
    required: false
  },
  theme: {
    type: String,
    default: '',
    required: false
  },
  width: {
    type: String,
    default: '100%',
    required: false
  },
  height: {
    type: String,
    default: '100%',
    required: false
  },
  onClick: {
    type: Function,
    default: (event) => {},
    required: false
  }
});

/*
* 设置echarts的宽和高
* */
const echartsStyle = computed(() => {
  const getDimension = (dimension, defaultValue) => {
    if (!dimension) return defaultValue
    const dimensionStr = dimension.toString()
    // 如果传入的width和height有单位则直接使用传入的，否则默认加上px
    return dimensionStr.includes('px') || dimensionStr.includes('vw') || dimensionStr.includes('%') || dimensionStr.includes('vh')
      ? dimensionStr
      : `${dimension}px`
  }
  return {
    // 如果没传默认为100%
    width: getDimension(props.width, '100%'),
    height: getDimension(props.height, '100%'),
  }
});

const chartRef = ref();
const chartInstance = ref();

const draw = () => {
  if (chartInstance.value) {
    // 给echarts赋值
    chartInstance.value.setOption(props.option, { notMerge: true });
  }
};

// 监听props来动态更新
watch(props, () => {
  draw();
}, {deep: true});

const handleClick = (event) => props.onClick && props.onClick(event);

const init = () => {
  if (!chartRef.value) return;
  chartInstance.value = echarts.getInstanceByDom(chartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(
      echarts.init(chartRef.value, props.theme, {
        renderer: props.renderer
      })
    );
    chartInstance.value.on("click", handleClick);
    draw();
  }
};

const resize = () => {
  if (chartInstance.value && props.resize) {
    chartInstance.value.resize({ animation: { duration: 300 } });
  }
};

const debouncedResize = lodash.debounce(resize, 300);

onMounted(() => {
  nextTick(() => init());
  window.addEventListener("resize", debouncedResize);
});

onBeforeUnmount(() => {
  chartInstance.value?.dispose();
  window.removeEventListener("resize", debouncedResize);
});

defineExpose({
  getInstance: () => chartInstance.value,
  getChartRef: () => chartRef.value,
  resize,
  draw
});
</script>
