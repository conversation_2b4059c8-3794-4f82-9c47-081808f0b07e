<template>
  <div class="dialog-container">
    <el-dialog
      v-model="addEditVisible"
      @close="resetForm"
      :title="dialogTitle"
      width="600"
    >
      <el-form
        ref="folderFormRef"
        :model="folderForm"
        :rules="FOLDER_FORM_RULES"
        label-position="top"
        class="custom-form !block"
      >
        <el-form-item label="文件夹名称" prop="folderName">
          <el-input
            clearable
            v-model="folderForm.folderName"
            placeholder="请输入文件夹名称"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="上级文件夹">
          <!-- <el-tree-select
            v-model="folderForm.parentFolderId"
            :data="folderOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择上级文件夹"
            check-strictly
          /> -->
          <el-select
            clearable
            v-model="folderForm.parentFolderId"
            placeholder="请选择上级文件夹"
          >
            <el-option
              v-for="item in folderOptions"
              :key="item.id"
              :label="item.folderName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button class="cancelDialogBtn" @click="onCancel">取消</el-button>
        <el-button
          class="addDialogBtn"
          :loading="submitLoading"
          @click="onSubmit"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { FOLDER_FORM_RULES } from "@/views/smartPatrol/config/formConfig.js";
import { AllFolderList, editFolder, addFolder } from "@/api/plan/plan.js";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["refreshData"]);

const folderForm = ref({
  id: "",
  folderName: "",
  parentFolderId: "",
});

const dialogType = ref("");
const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新建文件夹" : "编辑文件夹";
});

const addEditVisible = ref(false);
const folderFormRef = ref(null); // 表单组件

const folderOptions = ref([]);

/**
 * 打开弹框
 */
const openDialog = async (type, row) => {
  // 编辑
  if (row?.value) {
    const { name, parentFolderId } = row;
    folderForm.value.id = row?.value;
    folderForm.value.folderName = name;
    folderForm.value.parentFolderId =
      parentFolderId === 0 ? "" : parentFolderId;
  } else {
    // 新增
    resetForm();
  }
  dialogType.value = type;
  addEditVisible.value = true;
};

/**
 * 重置表单操作
 */
const resetForm = () => {
  folderForm.value = {
    folderName: "",
    parentFolderId: "",
  };
};

/**
 * 取消操作
 */
const onCancel = () => {
  addEditVisible.value = false;
  resetForm();
};

/**
 * 提交表单操作
 */
const onSubmit = () => {
  folderFormRef.value.validate(async (val) => {
    if (val) {
      if (dialogType.value === "add") {
        await addFolder(folderForm.value);
      }
      if (dialogType.value === "edit") {
        await editFolder(folderForm.value);
      }
      proxy.$message.success("操作成功");
      addEditVisible.value = false;
      emit("refreshData");
    }
  });
};

/**
 * 查询上级文件夹树结构
 */
async function getSuperiorFolderTree() {
  const res = await AllFolderList();
  if (res.code === 200) {
    folderOptions.value = res.data;
  }
}

getSuperiorFolderTree();

defineExpose({ openDialog, folderFormRef });
</script>

<style lang="scss" scoped>
:deep(.custom-form) {
  .el-form-item--default {
    margin-bottom: 12px;
  }

  .el-form-item__label,
  .el-radio__label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
  }
}

:deep(.el-dialog) {
  padding: 24px;
  .el-dialog__header {
    padding-left: 0;
  }
  .el-dialog__title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
  }
}

.el-dialog__footer {
  height: 100px;
  line-height: 100px;
  padding: 0 22px 0 0;

  .cancelDialogBtn,
  .addDialogBtn {
    width: 80px;
    height: 32px;
    line-height: 32px;
    border-radius: 5px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
  }

  .cancelDialogBtn {
    border: 1px solid #cfd2d6;
    color: #1f2329;
  }
  .addDialogBtn {
    background: #0070ff;
    color: #ffffff;
  }
}
</style>
