import * as echarts from 'echarts/core';
import 'zrender/lib/svg/svg';
import 'echarts/lib/component/graphic'
import {
  <PERSON><PERSON>hart,
  LineChart,
  LinesChart,
  Pie<PERSON>hart,
  Scatter<PERSON>hart,
  Radar<PERSON>hart,
  Gauge<PERSON>hart,
  PictorialBarChart,
  Custom<PERSON>hart
} from 'echarts/charts';
import {
  LegendComponent,
  PolarComponent,
  GeoComponent,
  ToolboxComponent,
  DataZoomComponent,
} from 'echarts/components';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  // 数据集组件
  DatasetComponent,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent
} from 'echarts/components';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import 'echarts-wordcloud';

// 注册必须的组件
echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  Bar<PERSON>hart,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  PolarComponent,
  GeoComponent,
  ToolboxComponent,
  DataZoomComponent,
  BarChart,
  LineChart,
  LinesChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  PictorialBarChart,
  CustomChart
]);
export default echarts
