<template>
  <div class="app-container" style="height: inherit; padding: 0">

    <div class="un-search" v-if="!isSearch">
      <div class="center-container">
        <img src="@/assets/images/search-title.png" class="search-title" alt="">
        <div class="tips">说明：请输入完整的姓名、身份证号或手机号码，您的所有操作将被后台审计。</div>
        <div class="search-container">
          <el-input v-model="queryParams.keyword"
                    class="search-input"
                    size="large"
                    placeholder="请输入姓名、身份证号、虚拟账号、帖文标题等进行搜索"
                    clearable
                    @clear="handleQuery"
                    @keyup.enter="handleQuery"
          >
          </el-input>
          <el-button class="search-btn" @click="handleQuery" size="large" type="primary">搜索</el-button>
        </div>
        <div class="history-container">
          <div class="more-font">查询历史
            <el-icon class="el-icon--right" style="color: #999; font-weight: 700">
            <ArrowRight />
          </el-icon>
            <div @click="delAll" style="cursor: pointer; position: absolute; right: 0; font-size: 12px; color: #409EFF">删除全部</div>
          </div>
          <div class="history-list">
            <div class="history-item" v-for="(item, i) in historyList" :key="i" @click="selectItem(item)">
              {{ item }}
              <el-icon style="margin-left: 10px"><CircleClose  @click.stop="delSinglePerson(item)"/></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="al-search" v-else v-loading="isLoading" element-loading-text="加载中..."
         element-loading-svg-view-box="-10, -10, 50, 50" element-loading-background="rgba(122, 122, 122, 0.8)">
      <div class="center-container">
        <img src="@/assets/images/search-title.png" class="search-title" alt="">
        <div class="search-container">
          <el-input v-model="queryParams.keyword"
                    class="search-input"
                    size="large"
                    placeholder="请输入姓名、身份证号、虚拟账号、帖文标题等进行搜索"
                    clearable
                    @clear="handleQuery"
                    @keyup.enter="handleQuery"
          >
          </el-input>
          <el-button class="search-btn" @click="handleQuery" size="large" type="primary">搜索</el-button>
        </div>
        <div class="tips">说明：请输入完整的姓名、身份证号或手机号码，您的所有操作将被后台审计。</div>
        <div class="search-model" style="flex-grow: 1">
          <el-table v-if="list && list.length" :data="list" height="calc(100% - 100px)">
            <el-table-column label="姓名" prop="name" />
            <el-table-column label="身份证" prop="idCard" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-button v-if="scope.row.dataCount !== 0" link type="primary" @click="viewDetail(scope.row)">查询
                </el-button>
                <span v-else>无相关数据</span>
              </template>
            </el-table-column>
          </el-table>

          <div class="search-empty" v-else>
            <img src="@/assets/images/search-empty.png" alt="" style="width: 200px; height: 200px">
            <div class="empty-font">
              您的查询内容暂无结果
<!--              <el-button text type="primary" class="type-btn" @click="handleApply">深度赋能>></el-button>-->
            </div>
          </div>
          <pagination
              v-show="total > 0"
              :total="total"
              :page-sizes="[15, 20, 30, 50]"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getHistoryData, getSearchData, delHisPerson } from "@/api/networkuser/user.js"
  import {getToken} from "@/utils/auth";
  import {ElMessage} from "element-plus";
  const router = useRouter();
  const route = useRoute();
  const { proxy } = getCurrentInstance();
  const searchType = ref(null)

  const open = ref(false)
  const isLoading = ref(false)
  const historyList = ref([])
  const list = ref([])
  const fileList = ref([]);

  // 禁用今天之前的日期
  const disabledDate = (time) => {
    return time.getTime() < Date.now() - 86400000
  }
  const data = reactive({
    queryParams: {
      keyword: "",
      type: "1",
      pageNum: 1,
      pageSize: 15
    },
    isSearch: false,
    total: 0,
    form: {
      caseName: '',
      caseNum: '',
      caseType: '',
      urgencyLevel: '',
      status: null,
      searchType: '',
      searchDeadline: new Date(new Date().getTime() + 604800000).toISOString(),
      searchContent: '',
      caseSummary: '',
      currentReviewer: null,
      fileInfos: [],
    },
    rules: {
      caseName: [{ required: true, message: "案件名称不能为空", trigger: "submit" }],
      caseNum: [{ required: true, message: "案件编号不能为空", trigger: "submit" }],
      caseType: [{ required: true, message: "案件类型不能为空", trigger: "submit" }],
      urgencyLevel: [{ required: true, message: "紧急程度不能为空", trigger: "submit" }],
      // searchType: [{ required: true, message: "查询类别不能为空", trigger: "submit" }],
      searchDeadline: [{ type: 'date', required: true, message: "截止时间不能为空", trigger: "submit" }],
      searchContent: [{ required: true, message: "查询内容不能为空", trigger: "submit" }],
      caseSummary: [{ required: true, message: "案件摘要不能为空", trigger: "submit" }],
      currentReviewer: [{ required: true, message: "审批人不能为空", trigger: "submit" }],
      fileInfos: [{ required: true, message: "佐证材料不能为空", trigger: "submit" }]
    },
  });
  const { queryParams, total, form, rules, isSearch } = toRefs(data);

  const shortcuts = [
    {
      text: '一周后',
      value: () => {
        const date = new Date()
        date.setDate(date.getDate() + 7)
        return date
      },
    },
    {
      text: '两周后',
      value: () => {
        const date = new Date()
        date.setDate(date.getDate() + 14)
        return date
      },
    },
    {
      text: '三周后',
      value: () => {
        const date = new Date()
        date.setDate(date.getDate() + 21)
        return date
      },
    },
    {
      text: '四周后',
      value: () => {
        const date = new Date()
        date.setDate(date.getDate() + 28)
        return date
      },
    },
  ]

  function selectItem(item) {
    queryParams.value.keyword = item
    handleQuery()
  }

  function handleQuery() {
    if(queryParams.value.keyword) {
      getSearchData(queryParams.value.keyword).then(res => {
        if(res.code === 200) {
          isSearch.value = true
          list.value = res.rows
          total.value = res.total
        }
      })
    }
/*    if (queryParams.value.keyword && queryParams.value.keyword.trim()) {
      isLoading.value = true
      // identifyType()
      queryParams.value.pageNum = 1
      getSearchData(queryParams.value).then(res => {
        if (res.code === 200) {
          let payload = {
            name: queryParams.value.keyword,
            searchType: 0,
            searchContentType: queryParams.value.type
          }
          handleAdd(payload)
          if (res.rows && res.rows.length === 0) {
            router.push("/business/detailAll/allDetail/0/" + queryParams.value.keyword)
          }
          if (res.rows && res.rows.length > 0) {
            let searchArr = res.rows.filter(item => item.dataCount !== 0)
            if (searchArr.length === 1) {
              const id = searchArr[0].userId
              const name = searchArr[0].xm ? searchArr[0].xm : searchArr[0].name
              router.push("/business/detail/searchDetail/" + id + '/' + name + '/0/' + queryParams.value.keyword)
            } else {
              isSearch.value = true
              list.value = res.rows
              total.value = res.total
            }
          } else {
            isSearch.value = true
            list.value = res.rows
            total.value = res.total
          }
          isLoading.value = false
        } else {
          isLoading.value = false
        }
      }).catch(err => {
        isLoading.value = false
      })
    }*/
  }

  function identifyType() {
    // 根据输入框的值鉴别分类
    if (!queryParams.value.keyword) return
    let keyword = queryParams.value.keyword
    if (isName(keyword)) {
      queryParams.value.type = '1'
    } else if(isIDCard(keyword)) {
      queryParams.value.type = '2'
    } else if(isPhone(keyword)) {
      queryParams.value.type = '3'
    } else if(isEmail(keyword)) {
      queryParams.value.type = '4'
    } else if(isQQ(keyword)) {
      queryParams.value.type = '5'
    } else if(isWeChat(keyword)) {
      queryParams.value.type = '6'
    }
  }

  function isName(value) {
    // 姓名 全是中文
    const chineseRegex = /^[\u4e00-\u9fa5]+$/;
    return chineseRegex.test(value)
  }
  function isIDCard(value) {
    // 身份证 长度18或15并且最多只有一个x
    const regIDCard = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[X])$)$/
    return regIDCard.test(value)
  }
  function isPhone(value) {
    // 手机号 长度11 开头是1
    const regPhone = /^1\d{10}$/
    return regPhone.test(value)
  }
  function isEmail(value) {
    // 邮箱
    const regEmail = /^([A-Za-z0-9_\-.])+@([A-Za-z0-9_\-.])+\.([A-Za-z]{2,4})$/;
    return regEmail.test(value)
  }
  function isQQ(value) {
    // qq号 长度5-10 2024年是十位
    const regQQ = /^\d{5,10}$/
    return regQQ.test(value)
  }
  function isWeChat(value) {
    // 微信号 长度6-20 字母加数字
    const regWechat = /^.{6,20}$/
    return regWechat.test(value)
  }

  function getList() {
/*    if (queryParams.value.keyword && queryParams.value.keyword.trim()) {
      isSearch.value = true
      isLoading.value = true
      getSearchData(queryParams.value).then(res => {
        if (res.code === 200) {
          list.value = res.rows
          total.value = res.total

          let payload = {
            name: queryParams.value.keyword,
            searchType: 0
          }
          handleAdd(payload)
          isLoading.value = false
        } else {
          isLoading.value = false
        }
      }).catch(err => {
        isLoading.value = false
      })
    }*/
  }

  function searchMore() {

  }

  function setColor(i) {
    let color = ''
    switch (i) {
      case 1:
        color = '#FF2D46';
        break;
      case 2:
        color = '#FF6502';
        break;
      case 3:
        color = '#FBA90F';
        break;
      default:
        color = '#999';
        break;
    }

    return color;
  }

  function viewDetail(item) {
    // const id = item.rybh ? item.rybh : item.zjhm
    const id = item.id
    // const name = item.xm ? item.xm : item.name
    router.push(`/keyObj/add/addNetPerson/${id}/${true}`)
    // router.push({name: "addNetPerson", params: {id: id, type: true},})
   /* if (id) {
      router.push("/business/detail/searchDetail/" + id + '/' + name + '/0/' + queryParams.value.keyword)
    }*/
  }

  function handleApply() {
    // open.value = true
    // proxy.$refs.ApplyDialogRef.openDialog()
  }

  function getHistoryList() {
    getHistoryData().then(res => {
      if (res.code === 200) {
        historyList.value = res.data
        // historyList.value = [{name: 333344}, {name: 333344}, {name: 333344}, {name: 333344}, {name: 333344}]
      }
    }).catch(err => {
    })
  }

  function handleSuccess(res, e, fileList) {
    e.fileUrl = res.msg
    e.fileName = e.name
  }

  function handlePhoto() {
    // proxy.$refs.photoDialogRef.open()
  }
  function handlePhotoSuccess(data) {
    form.value.fileInfos = form.value.fileInfos.concat(data)
  }

  function cancel() {
    reset()
    open.value = false
  }

  //重置
  function reset() {
    form.value = {
      id: null,
      caseName: '',
      caseNum: '',
      caseType: '',
      urgencyLevel: '',
      status: null,
      // searchType: '',
      searchDeadline: new Date(new Date().getTime() + 604800000).toISOString(),
      searchContent: '',
      caseSummary: '',
      currentReviewer: null,
      fileInfos: [],
    };
  }

  function submitForm(status) {
/*    form.value.status = status
    proxy.$refs["applyRef"].validate(valid => {
      if (valid) {
        addApply(form.value).then(res => {
          if (res.code === 200 || res.msg === "操作成功") {
            ElMessage.success(res.msg);
            cancel()
          } else {
            // ElMessage.error(res.msg);
          }
        }).catch(err => {
          ElMessage.error(err);
        })
      }
    });*/
  }

  function handleAdd(data) {
  /*  // console.log(444)
    addSearchRecord(data).then(res => {
      // console.log(222333)
    }).catch(err => {
    })*/
  }

  function delAll() {
    delHisPerson(historyList.value).then(res => {
      if(res.code === 200 && res.data) {
        ElMessage({ message: '删除成功', type: 'success' })
        getHistoryList()
      }
    }).catch(err => {

    })
  }

  function delSinglePerson(name) {
    delHisPerson([name]).then(res => {
      if(res.code === 200 && res.data) {
        ElMessage({ message: '删除成功', type: 'success' })
        getHistoryList()
      }
    }).catch(err => {

    })
  }

  getHistoryList()
</script>

<style lang="scss" scoped>

  :deep(.pagination-container) {
    /*display: flex;*/
    /*align-items: center;*/
    width: inherit;
  }

  :deep(.el-select__wrapper) {
    height: inherit;
    background-color: #FFFFFF !important;
  }
  .un-search {
    width: inherit;
    height: inherit;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url("../../../assets/images/search-bg.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;

    .center-container {
      display: flex;
      flex-direction: column;
      align-items: center;

      .search-title {
        width: 240px;
      }

      .tips {
        margin: 20px 0;
        color: #888;
      }

      .search-container {
        display: flex;
        align-items: center;
        justify-content: center;

        .search-input {
          width: 700px;
          height: 60px;
          margin-right: 10px;
        }

        .search-btn {
          width: 100px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .history-container {
        width: 810px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-top: 30px;
        position: relative;

        .more-font {
          color: #333333;
          font-size: 20px;
          font-weight: 700;
          display: flex;
          align-items: center;
        }

        .history-list {
          .history-item {
            margin-top: 20px;
            margin-right: 20px;
            padding: 10px 20px;
            border-radius: 25px;
            background-color: #FFFFFF;
            color: #000;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
          }
        }
      }
    }
  }

  .al-search {
    width: inherit;
    height: inherit;
    position: relative;
    background-image: url("../../../assets/images/search-bg2.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;

    .center-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100%;

      .search-title {
        width: 150px;
        margin: 50px 0 30px;
      }

      .search-container {
        display: flex;
        align-items: center;
        justify-content: center;

        .search-input {
          width: 500px;
          height: 50px;
        }

        .search-btn {
          width: 100px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .tips {
        width: 600px;
        margin: 10px 0 0;
        color: #888;
      }

      .search-model {
        width: 800px;
        //display: flex;
        //flex-direction: column;
        //align-items: center;
        margin-top: 50px;

        .search-record {
          width: inherit;

          .more-font {
            color: #333333;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 20px;
          }

          .search-list {
            width: inherit;
            display: flex;
            flex-direction: column;
            align-items: center;

            .search-header {
              font-size: 13px;
              width: inherit;
              height: 56px;
              background: #F0F2F5;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 0 32px;

              .search-index {
                width: 36px;
                height: 36px;
                border-radius: 50%;
                background-color: #0093D4;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #FFF;
                border: 2px solid #FFF;
                box-shadow: 0 0 10px rgba(51, 51, 51, .6);
              }
            }

            .type-list {
              width: inherit;
              /*width: 25vw;*/
              margin: 8px 0;

              .type-item {
                display: flex;
                align-items: center;
                justify-content: center;
                border-bottom: 1px solid #F0F2F5;

                .type-btn {
                  font-size: 16px;
                  padding: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  line-height: normal;
                }
              }


            }
          }

        }

        .search-empty {
          margin-top: 60px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .empty-font {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
        }
      }
    }
  }



  .upload-form-item {
    :deep(.el-form-item__content) {
      align-items: flex-start;
      position: relative;
      .upload-demo {
        width: 40%;
      }
    }
    .photo-btn {
      position: absolute;
      left: 80px;
    }
  }
</style>