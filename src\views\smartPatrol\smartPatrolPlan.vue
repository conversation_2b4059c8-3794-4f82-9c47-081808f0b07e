<template>
  <div class="app-container" style="height: 100%">
    <div class="left-container gap-y-[20px]">
      <div class="flex justify-between items-center">
        <span class="page-title">智巡方案</span>
        <svg-icon
          icon-class="folder"
          class="cursor-pointer"
          @click="addFolder"
        />
      </div>
      <el-input v-model="queryName" placeholder="请输入方案名称" clearable />

      <el-tree
        ref="treeRef"
        class="filter-tree"
        :data="treeData"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        :expand-on-click-node="true"
        @node-click="handleDetails"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <el-tooltip :content="node.label" placement="top">
              <div class="tree-label">{{ node.label }}</div></el-tooltip
            >
            <!-- 根据type字段区分是文件夹还是方案控制各自的操作显示,type:1文件夹,type:2方案 -->
            <section v-if="data.type === 2">
              <el-button
                type="primary"
                :icon="Edit"
                link
                @click.stop="handleSchemeEdit(data)"
              />
              <el-button
                type="danger"
                :icon="Delete"
                link
                @click.stop="handleSchemeRemove(node, data)"
              />
            </section>

            <section v-if="data.type === 1" @click.stop>
              <el-button
                :icon="Plus"
                link
                @click.stop="handleSchemeAdd(data)"
                class="mr-[12px]"
              />
              <el-dropdown trigger="click">
                <el-button :icon="MoreFilled" link />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>
                      <span @click="handleFolderEdit(data)">编辑</span>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <span @click="handleFolderRemove(data)">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </section>
          </span>
        </template>
      </el-tree>
      <!-- <div class="model-footer">
        <el-button type="primary" size="large" class="btn" @click="handleSchemeAdd"
          >创建检测方案</el-button
        >
      </div> -->
    </div>
    <div class="right-container" v-if="plan">
      <!-- 标题 -->
      <div class="page-title mb-[20px] w-[108px] flex justify-end">
        <span>配置方案</span>
      </div>
      <el-form
        :model="plan"
        :rules="planRules"
        ref="planRef"
        label-width="120px"
        class="form-container"
      >
        <el-form-item label="方案名称" prop="name">
          <el-input
            v-model="plan.name"
            placeholder="请输入方案名称"
            :disabled="!isShow"
          />
        </el-form-item>

        <el-form-item label="精准地域设置"> </el-form-item>
        <el-form-item label="精准地域">
          <el-cascader
            filterable
            clearable
            v-model="plan.preciseLocation"
            :options="pcaTextArr"
            placeholder="请输入所在地区"
            style="width: 100%"
            :props="cascaderProps"
            :disabled="!isShow"
          />
        </el-form-item>
        <el-form-item>
          <span class="tips"
            >提示：输入并选择您需要的所在地区，最多支持 10
            个地名，系统将通过您输入的地域进行信息汇总采集。</span
          >
        </el-form-item>
        <el-form-item label="方案关键词设置">
          <!-- <span class="tips"
            >提示：关键词之间为并且关系，满足所有关键词的舆情信息才可被收集，填写一个关键词后，需要用点下方的"且"按钮对关键词进行分隔，关键词方可生效</span
          > -->
        </el-form-item>
        <el-form-item>
          <template #label>
            <span>
              <el-tooltip
                content="什么情况下同时用到“&”、“|”：如想关注上海世博会的新闻，由于“世博会”又可能被称为“世界博览会”，表达式为 “上海&(世博会|世界博览会)”，表示文章中出现“上海”，同时出现“世博会”或者 “世界博览会”中任意一个词，就能监测到；"
                placement="top"
              >
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
              监控关键词
            </span>
          </template>
          <el-input
            v-model="plan.keyword"
            placeholder="请输入监控关键词"
            :disabled="!isShow"
            type="textarea"
            :rows="4"
            :autosize="{ minRows: 4, maxRows: 20 }"
            show-word-limit
            maxlength="1000"
          />
        </el-form-item>
        <el-form-item>
          <div style="display: flex; flex-direction: column">
            <section class="flex gap-x-[20px]">
              <el-button
                type="primary"
                @click="handleSplice('keyword', '&')"
                style="width: 60px"
                v-if="isShow"
                >&</el-button
              >
              <el-button
                type="primary"
                @click="handleSplice('keyword', '|')"
                style="width: 60px"
                v-if="isShow"
                >|</el-button
              >
              <el-button
                type="primary"
                @click="handleSplice('keyword', '(')"
                style="width: 60px"
                v-if="isShow"
                >(</el-button
              >
              <el-button
                type="primary"
                @click="handleSplice('keyword', ')')"
                style="width: 60px"
                v-if="isShow"
                >)</el-button
              >
            </section>
          </div>
        </el-form-item>
        <el-form-item>
          <template #label>
            <span>
              <el-tooltip
                content="什么情况下用到排除关键词：如想关注上海、北京、广州的新闻，但又不想看到内容中有“三室一厅”、“二室 一厅”这种关键词的广告，可以使用排除关键词的方式。 匹配关键词表达式“北京|上海|广州” 排除关键词表达式“三室一厅|二室一厅"
                placement="top"
              >
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
              排除关键词
            </span>
          </template>
          <el-input
            v-model="plan.excludeKeywords"
            placeholder="请输入排除关键词"
            :disabled="!isShow"
            type="textarea"
            :rows="4"
            :autosize="{ minRows: 4, maxRows: 20 }"
            show-word-limit
            maxlength="1000"
          />
        </el-form-item>
        <el-form-item>
          <div style="display: flex; flex-direction: column">
            <section class="flex gap-x-[20px]">
              <el-button
                type="primary"
                @click="handleSplice('excludeKeywords', '&')"
                style="width: 60px"
                v-if="isShow"
                >&</el-button
              >
              <el-button
                type="primary"
                @click="handleSplice('excludeKeywords', '|')"
                style="width: 60px"
                v-if="isShow"
                >|</el-button
              >
              <el-button
                type="primary"
                @click="handleSplice('excludeKeywords', '(')"
                style="width: 60px"
                v-if="isShow"
                >(</el-button
              >
              <el-button
                type="primary"
                @click="handleSplice('excludeKeywords', ')')"
                style="width: 60px"
                v-if="isShow"
                >)</el-button
              >
            </section>
          </div>
        </el-form-item>
      </el-form>
      <div class="model-footer" style="margin-left: 30px" v-if="isShow">
        <el-button type="primary" size="large" class="btn" @click="handleSubmit"
          >提交</el-button
        >
        <el-button size="large" class="btn" @click="cancel">取消</el-button>
      </div>
    </div>
    <!-- 新增编辑文件夹弹框 -->
    <AddEditFolderDialog
      v-model="folderDialogVisible"
      ref="folderDialogRef"
      @refresh-data="getList"
    />
  </div>
</template>

<script setup>
import { Edit, Delete, Plus, MoreFilled } from "@element-plus/icons-vue";
import AddEditFolderDialog from "./components/AddEditFolderDialog.vue";
import {
  getPatrolPlanList,
  addPlan,
  editPlan,
  delPlan,
  delFolder,
} from "@/api/plan/plan.js";
import { deepClone } from "../../utils";
const { proxy } = getCurrentInstance();
import { pcaTextArr } from "element-china-area-data";

const defaultProps = {
  children: "children",
  label: "name",
};
const checkKeyword = (rule, value, callback) => {
  let list = [" ", "，"];
  if (value == "" || value == null) {
    callback(new Error("关键词不得为空"));
  } else {
    console.log(value);
    list.forEach((e) => {
      if (value.toString().indexOf(e) > -1) {
        callback(new Error("关键词无法解析"));
      }
    });
    callback();
  }
};

const currentSelectedPlan = ref(null);

const cascaderProps = { multiple: true, checkStrictly: true, limit: 10 };
const data = reactive({
  queryName: null,
  treeData: {},
  plan: null,
  planRules: {
    name: [{ required: true, message: "方案名称不可为空", trigger: "submit" }],
    keyword: [{ required: true, validator: checkKeyword, trigger: "submit" }],
  },
  isShow: false,
});
const { queryName, treeData, plan, planRules, isShow } = toRefs(data);

watch(queryName, (val) => {
  proxy.$refs.treeRef.filter(val);
});

watch(
  () => plan.value?.preciseLocation,
  (newVal, oldVal) => {
    if (newVal?.length > 10) {
      ElMessage({
        type: "warning",
        message: "最多选择10个地区",
      });
      nextTick(() => {
        plan.value.preciseLocation = oldVal;
      });
    }
  },
  { deep: true }
);

const folderDialogVisible = ref(false);
const folderDialogRef = ref(null);

//过滤节点
function filterNode(value, data) {
  if (!value) return true;
  return data.name.includes(value);
}

// 新增文件夹
function addFolder() {
  folderDialogRef.value.openDialog("add");
}
// 修改文件夹
function handleFolderEdit(data) {
  folderDialogRef.value.openDialog("edit", data);
}

//删除文件夹
function handleFolderRemove(data) {
  ElMessageBox.confirm("确定要删除该文件夹吗", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delFolder(data.value).then((res) => {
        if (res.code === 200) {
          getList();
          cancel();
          ElMessage({
            type: "success",
            message: res.msg,
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "danger",
        message: "删除失败",
      });
    });
}

//新增方案
function handleSchemeAdd(data) {
  let obj = {
    name: "新方案",
    folderId: data?.value,
  };
  isShow.value = true;
  // treeData.value[0].children.push(obj)
  plan.value = deepClone(obj);
}

// function handleInput(type) {
//   // 正则表达式，匹配 &、|、(、) 字符
//   const regex = /[&|()]/g;
//   if (type === "excludeKeywords") {
//     plan.value.excludeKeywords = plan.value.excludeKeywords.replace(regex, "");
//   } else {
//     plan.value.keyword = plan.value.keyword.replace(regex, "");
//   }
// }

//修改方案
function handleSchemeEdit(data) {
  isShow.value = true;
  const tempData = deepClone(data);
  plan.value = {
    id: tempData.value,
    name: tempData.name,
    preciseLocation: tempData?.eventSolution?.preciseLocation,
    keyword: tempData?.eventSolution?.keyword,
    excludeKeywords: tempData?.eventSolution?.excludeKeywords,
  };
}

//查看方案详情
function handleDetails(data, node) {
  if (node.label !== "实时监控方案") {
    isShow.value = false;
    const tempData = deepClone(data);
    plan.value = {
      name: tempData.name,
      preciseLocation: tempData?.eventSolution?.preciseLocation,
      keyword: tempData?.eventSolution?.keyword,
      excludeKeywords: tempData?.eventSolution?.excludeKeywords,
    };
  }
}

//删除方案
function handleSchemeRemove(node, data) {
  ElMessageBox.confirm("确定要删除该方案吗", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delPlan(data.value).then((res) => {
        if (res.code === 200) {
          getList();
          cancel();
          ElMessage({
            type: "success",
            message: res.msg,
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "danger",
        message: "删除失败",
      });
    });
}

//插入
function handleSplice(keywordType, str) {
  switch (str) {
    case "&":
      if (keywordType === "keyword") {
        if (plan.value.keyword.length > 0) {
          let str = plan.value.keyword.split("").reverse().join();
          if (str[0] !== "&") {
            plan.value.keyword += "&";
          }
        }
      }
      if (keywordType === "excludeKeywords") {
        if (plan.value.excludeKeywords.length > 0) {
          let str = plan.value.excludeKeywords.split("").reverse().join();
          if (str[0] !== "&") {
            plan.value.excludeKeywords += "&";
          }
        }
      }
      break;
    case "|":
      if (keywordType === "keyword") {
        if (plan.value.keyword.length > 0) {
          let str = plan.value.keyword.split("").reverse().join();
          if (str[0] !== "|") {
            plan.value.keyword += "|";
          }
        }
      }
      if (keywordType === "excludeKeywords") {
        if (plan.value.excludeKeywords.length > 0) {
          let str = plan.value.excludeKeywords.split("").reverse().join();
          if (str[0] !== "|") {
            plan.value.excludeKeywords += "|";
          }
        }
      }
      break;
    case "(":
      if (keywordType === "keyword") {
        if (plan.value.keyword.length > 0) {
          let str = plan.value.keyword.split("").reverse().join();
          if (str[0] !== "(") {
            plan.value.keyword += "(";
          }
        }
      }
      if (keywordType === "excludeKeywords") {
        if (plan.value.excludeKeywords.length > 0) {
          let str = plan.value.excludeKeywords.split("").reverse().join();
          if (str[0] !== "(") {
            plan.value.excludeKeywords += "(";
          }
        }
      }
      break;
    case ")":
      if (keywordType === "keyword") {
        if (plan.value.keyword.length > 0) {
          let str = plan.value.keyword.split("").reverse().join();
          if (str[0] !== ")") {
            plan.value.keyword += ")";
          }
        }
      }
      if (keywordType === "excludeKeywords") {
        if (plan.value.excludeKeywords.length > 0) {
          let str = plan.value.excludeKeywords.split("").reverse().join();
          if (str[0] !== ")") {
            plan.value.excludeKeywords += ")";
          }
        }
      }
      break;
  }
}

//提交
function handleSubmit() {
  proxy.$refs["planRef"].validate((valid) => {
    if (valid) {
      if (!plan.value.id) {
        addPlan(plan.value).then((res) => {
          if (res.code === 200) {
            ElMessage({
              type: "success",
              message: res.msg,
            });
            getList();
            isShow.value = false;
          } else {
            ElMessage({
              type: "danger",
              message: res.msg,
            });
          }
        });
      } else {
        editPlan(plan.value).then((res) => {
          if (res.code === 200) {
            ElMessage({
              type: "success",
              message: res.msg,
            });
            getList();
            isShow.value = false;
          } else {
            ElMessage({
              type: "danger",
              message: res.msg,
            });
          }
        });
      }

      currentSelectedPlan.value = deepClone(plan.value);
    }
  });
}

//取消
function cancel() {
  // plan.value = null;
  isShow.value = false;
}

async function getList() {
  // cancel()
  // getTreeList().then((res) => {
  //   let obj = {
  //     name: "实时监控方案",
  //     children: res.data,
  //   };
  //   treeData.value = [obj];
  //   if (res.data.length) {
  //     isShow.value = false;
  //     plan.value = deepClone(res.data[0]);
  //   }
  // });
  const res = await getPatrolPlanList();
  if (res.code === 200) {
    treeData.value = res.data;
    if (res.data) {
      isShow.value = false;
      plan.value = currentSelectedPlan.value
        ? deepClone(currentSelectedPlan.value)
        : deepClone(treeData.value[0]);
    }
  }
}

getList();
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;

  /*align-items: center;*/
  /*justify-content: center;*/

  .page-title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }

  .left-container {
    width: 300px;
    height: inherit;
    box-shadow: 1px 0px 0.5px #bbbbbb;
    padding-right: 20px;
    position: relative;
    display: flex;
    flex-direction: column;

    .filter-tree {
      flex: 1;
    }

    .model-footer {
      width: inherit;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn {
        width: 80%;
      }
    }
  }

  .right-container {
    flex: 1;
    height: inherit;
    position: relative;
    display: flex;
    flex-direction: column;
    // padding-left: 23px;
    // padding-top: 22px;
    // padding-bottom: 22px;

    .form-container {
      flex: 1;
    }
  }
}

.tips {
  color: #999999;
}

.tree-label {
  width: 155px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.custom-tree-node) {
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
