#app {

  .main-container {
    height: 100%;
    // transition: margin-left .28s;
    margin-left: $base-sidebar-width;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    -webkit-transition: width .28s;
    transition: width 0.28s;
    width: calc($base-sidebar-width - 14px) !important;
    background-color: $base-menu-background;
    border-radius: 4px;
    height: calc(100% - 64px - 20px);
    position: absolute;
    font-size: 0px;
    top: 64px;
    bottom: 0;
    left: 20px;
    z-index: 9;
    overflow: hidden;
    margin-right: 14px;
    padding-top: 10px;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-menu-item, .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      // color: $base-menu-color-active !important;
      color: $base-menu-first-title-color;
      font-weight: 600;
    }

    & .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      // min-width: $base-sidebar-width !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    // 左侧子菜单选中样式
    & .theme-dark .el-menu--vertical .el-menu-item {
      height: 46px;
      &:hover {
        background-color: unset !important;
        background: url("@/assets/images/activeMenu.svg") no-repeat;
        background-position: center center;
        .menu-title {
          color: #1356F0;
        }
      }
      &.is-active {
        background: url("@/assets/images/activeMenu.svg") no-repeat;
        background-position: center center;
        .menu-title {
          color: #1356F0;
        }
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      &>.el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
          &>i {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  // .mobile {
  //   .main-container {
  //     margin-left: 0px;
  //   }

  //   .sidebar-container {
  //     transition: transform .28s;
  //     width: $base-sidebar-width !important;
  //   }

  //   &.hideSidebar {
  //     .sidebar-container {
  //       pointer-events: none;
  //       transition-duration: 0.3s;
  //       transform: translate3d(-$base-sidebar-width, 0, 0);
  //     }
  //   }
  // }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-sub-menu>.el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // you can use $sub-menuHover
      background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  // the scroll bar appears when the sub-menu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
