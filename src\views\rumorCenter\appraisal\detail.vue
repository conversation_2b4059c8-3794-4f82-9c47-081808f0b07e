<template>
  <div class="app-container">
    <!-- 菜单名 -->
    <div class="page-title"><span>谣言鉴定</span></div>
    <!-- 详情内容 -->
    <div>
      <span class="page-title">舆情正文</span>
      <div class="p-[50px] flex flex-col gap-y-[20px]">
        <!-- 内容 -->
        <div class="flex justify-center page-title">
          <span>{{ appraisalInfo.title }}</span>
        </div>
        <!-- 正文 -->
        <section>
          <ImagePreview
            v-if="contentIsImg(appraisalInfo.content)"
            :src="appraisalInfo.content"
            :width="100"
            :height="100"
          />
          <p v-else class="relative whitespace-pre-wrap!">
            {{ poContentF(appraisalInfo.content) }}
          </p>
          <img
            v-if="appraisalInfo.rumorFlag === IS_RUMOR.YES"
            class="w-[100px] absolute bottom-[-60px] right-[30px]"
            src="@/assets/images/detail-rumor.png"
          />
        </section>

        <!-- 内容分析-暂时不做 -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAppraisalDetailInfo } from "@/api/rumorCenter/appraisal.js";
import { IS_RUMOR } from "../config/constant.js";

const appraisalInfo = ref({
  title: "",
  content: "",
  rumorFlag: "",
});

const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 内容是否为图片
const poContentF = computed(() => val => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter(i => !contentIsImg.value(i));
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});

const getAppraisalDetail = async () => {
  const res = await getAppraisalDetailInfo(history.state.id);
  if (res.code === 200) {
    Object.assign(
      appraisalInfo.value,
      res.data,
      res?.data?.publicOpinionReport
    );
  }
};

onMounted(async () => {
  await getAppraisalDetail();
});
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: absolute; // 撑开高度
  padding: 22px 0 22px 23px;
  .page-title {
    // margin-left: 23px;
    // margin-top: 22px;
    margin-bottom: 22px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
}
</style>
