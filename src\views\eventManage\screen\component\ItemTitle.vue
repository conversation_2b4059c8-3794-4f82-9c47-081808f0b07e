<template>
  <div class="w-full flex items-center">
    <div class="strips-left flex-1 flex items-center justify-end">
      <div class="color-blocks bg-[#1991E8]"></div>
    </div>
    <div class="title-text flex-center font-PangMenZhengDao text-[#FEFFFF]">
      {{ title }}
    </div>
    <div class="strips-right flex-1 flex items-center justify-end">
      <div class="color-blocks bg-[#1991E8]"></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;
.strips-left {
  padding-right: px2vw(10);
  height: px2vw(18);
  background: url(@/assets/screen/title-border.png) no-repeat;
  background-size: 100% 100%;
}
.title-text {
  //width: px2vw(158);
  padding: 0 px2vw(26);
  font-size: px2vw(19);
}
.strips-right {
  padding-right: px2vw(10);
  height: px2vw(18);
  background: url(@/assets/screen/title-border.png) no-repeat;
  background-size: 100% 100%;
  transform: rotate(180deg);
}
.color-blocks {
  width: px2vw(12);
  height: px2vw(5);
  animation: blink 2s infinite;
}

.screen-title {
  width: 100%;
  line-height: 18px;
  color: #feffff;
  text-align: center;
  font-size: 19px;
  font-family: PangMenZhengDao sans-serif;
  position: relative;
  &:before {
    content: "";
    width: 160px;
    height: 18px;
    background: url(@/assets/screen/title-border-active.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    transition: all 1s ease;
  }
  &:after {
    content: "";
    width: 160px;
    height: 18px;
    background: url(@/assets/screen/title-border-active.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    transform: rotate(180deg);
  }
  &.large {
    &:before {
      width: 320px;
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
    }
    &:after {
      width: 320px;
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
    }
  }
  &.title-opacity {
    &:before {
      background: url(@/assets/screen/title-border.png) no-repeat;
      background-size: 100% 100%;
    }
    &:after {
      background: url(@/assets/screen/title-border.png) no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
