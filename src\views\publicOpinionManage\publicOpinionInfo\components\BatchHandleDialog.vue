<template>
  <el-dialog
    class="dialog-container"
    :model-value="modelValue"
    width="1100px"
    @close="onCancle"
  >
    <template #header>
      <span class="addTitle">批量{{ typeText }}</span>
    </template>

    <CommonTable
      :columns="PUBLIC_OPINION_INFO_HANDLE_COLUMNS"
      :data="tableData"
      :show-default-btn="false"
      :show-operation-column="true"
      :operation-column="90"
      no-padding
      :max-height="400"
      :highlight-current-row="false"
    >
      <template #poId="{ row }">
        <div class="whitespace-normal break-all">{{ row.poId }}</div>
      </template>

      <!-- 内容可能为文本/图片 -->
      <template #poContent="{ row }">
        <ImagePreview
          v-if="contentIsImg(row.poContent)"
          :src="row.poContent"
          :width="100"
          :height="100"
          @click.stop
        />
        <div v-else class="poContentWrapper whitespace-pre-wrap!">
          {{ poContentF(row.poContent) }}
        </div>
      </template>

      <template #netizenNickname="{ row }">
        <NetizenColumn
          :nickname="row.netizenNickname"
          :account-id="row.netizenAccountId"
        />
      </template>

      <template #publicTime="{ row }">
        <div class="whitespace-normal break-all">{{ row.publicTime }}</div>
      </template>

      <template #operation="{ row }">
        <el-button style="color: #d9001b" link @click="cancleSelect(row)"
          >取消选择</el-button
        >
      </template>
    </CommonTable>

    <div class="line"></div>

    <DialogForm
      ref="dialogFormRef"
      :form-data="formData"
      :improve-form="improveForm"
      :type-text="typeText"
      :dialog-type="'batch'"
    />

    <template #footer>
      <span class="dialog-footer">
        <el-button class="cancelDialogBtn" @click="onCancle">取消</el-button>
        <el-button class="addDialogBtn" :loading="submitLoading" @click="onOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
import NetizenColumn from "../../components/NetizenColumn.vue";
import DialogForm from "./DialogForm.vue";
import { PUBLIC_OPINION_INFO_HANDLE_COLUMNS } from "../../config/tableColumns";
import {
  createProcess,
  batchEditEventAndLocation,
} from "@/api/poManage/poInfo";
import { PO_TRANSFER_LIST, TRANSFER_TYPE } from "../../config/mapRel";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  typeText: {
    type: String,
    required: true,
  },
  poInfoList: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(["update:modelValue", "refreshData"]);

const { proxy } = getCurrentInstance();

const submitLoading = ref(false);
const dialogFormRef = ref();
// 表单数据
const formData = ref({
  deadlineTime1: "",
  charges1: [],
});

const improveForm = ref({
  poEvent: [],
  happenLocation: [], // 舆情属地
});

const delTableItemId = ref([]); // 取消选择的表格id
const tableData = computed(() =>
  props.poInfoList.filter((ele) => !delTableItemId.value.includes(ele.poId))
);
const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 内容是否为图片
const poContentF = computed(() => (val) => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter((i) => !contentIsImg.value(i));
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});

/**
 * 取消选择
 */
function cancleSelect(row) {
  if (tableData.value.length === 1) {
    proxy.$message.warning("至少需要选择一项");
    return;
  }
  delTableItemId.value.push(row.poId);
}

/**
 * 取消
 */
function onCancle() {
  reset();
  emit("update:modelValue", false);
}

/**
 * 舆情完善
 */
async function poImprove() {
  const params = {
    ids: tableData.value.map((i) => i.id),
    eventIds: improveForm.value.poEvent,
    involvedArea: improveForm.value.happenLocation?.join(""),
  };
  const res = await batchEditEventAndLocation(params);
  return res;
}

/**
 * 确认
 */
function onOk() {
  dialogFormRef.value.addFormRef.validate(async (val) => {
    if (val) {
      submitLoading.value = true;
      const processObj = PO_TRANSFER_LIST.find(
        (i) => i.fontLabel === props.typeText
      );
      const params = {
        reportIds: tableData.value.map((i) => i.id),
        commandCategory: processObj.categoriesCode,
        command: processObj.commandsCode,
        processStep: processObj.processStepCode,
        charges: formData.value.charges1?.map((id) => {
          return {
            userInCharge: id,
            deptInCharge: "100",
          };
        }),
      };

      // 添加截止时间参数
      if (props.typeText !== TRANSFER_TYPE.VIEW) {
        params.deadlineTime = formData.value.deadlineTime1;
      }

      let improveRes = null;
      if (props.typeText !== TRANSFER_TYPE.VIEW) {
        improveRes = await poImprove();
      }

      const createRes = await createProcess(params);
      const isSuccessfully = improveRes
        ? improveRes.code === 200 && createRes.code === 200
        : createRes.code === 200;
      if (isSuccessfully) {
        proxy.$message.success("处理成功");
        submitLoading.value = false;
        emit("refreshData");
        onCancle();
      }
    }
  });
}

/**
 * 重置数据
 */
function reset() {
  dialogFormRef.value.reset();
  delTableItemId.value = [];
}
</script>

<style lang="scss" scoped>
.dialog-container {
  .el-dialog__body {
    padding-right: 20px;
  }

  .addTitle {
    font-size: 16px;
    color: #333333;
  }

  .poContentWrapper {
    white-space: normal;
    word-break: break-all;

    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4; /* 控制几行打点 */
    line-clamp: 4;
    overflow: hidden;
  }

  .line {
    width: 100%;
    height: 1px;
    background: #333333;
    margin: 20px 0;
  }

  .cancelDialogBtn {
    width: 80px;
    height: 32px;
    line-height: 32px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #cfd2d6;
    font-size: 14px;
    color: #1f2329;
  }

  .addDialogBtn {
    width: 80px;
    height: 32px;
    line-height: 32px;
    background: #0052d9;
    border-radius: 5px;
    font-size: 13px;
    color: #ffffff;
  }
}
</style>
