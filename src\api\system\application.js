import portalRequest from "@/utils/portalRequest.js";

/**
 * @description 获取系统列表
 * @method get url: http://localhost:8080/api/client-sys/list
 * @returns {Promise}
 * */
export function getSysList(params) {
  return portalRequest({
    url: '/api/client-sys/list',
    method: 'get',
    params
  })
}

/**
 * @description 新增系统
 * @method post url: http://localhost:8080/api/client-sys/add
 * @param {*} data
 * @returns {Promise}
 * */
export function addSys(data) {
  return portalRequest({
    url: '/api/client-sys/add',
    method: 'post',
    data
  })
}

/**
 * @description 修改系统
 * @method post url: http://localhost:8080/api/client-sys/update
 * @param {*} data
 * @returns {Promise}
 * */
export function updateSys(data) {
  return portalRequest({
    url: '/api/client-sys/update',
    method: 'post',
    data
  })
}

/**
 * @description 删除系统
 * @method post url: http://localhost:8080/api/client-sys/delete
 * @param {*} data
 * @returns {Promise}
 * */
export function deleteSys(data) {
  return portalRequest({
    url: '/api/client-sys/delete',
    method: 'post',
    data
  })
}

