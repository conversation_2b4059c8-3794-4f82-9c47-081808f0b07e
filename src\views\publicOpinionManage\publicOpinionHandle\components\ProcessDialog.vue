<template>
  <el-dialog class="dialog-container" :model-value="modelValue" width="940px" @close="onCancle">
    <CommonTable
      :columns="PUBLIC_OPINION_PROCESS_COLUMNS"
      :data="tableData"
      :loading="tableLoading"
      :show-default-btn="false"
      :show-operation-column="false"
      no-padding
      :max-height="550"
      :highlight-current-row="false"
    >
      <template #curStatus="{ row }">
        <div class="flex-center">
          <div class="truncate statusClass" :style="curStatusF(row.curStatus).style">{{ curStatusF(row.curStatus).text }}</div>
        </div>
      </template>

      <template #isOverdue="{ row }">
        {{ isOverdueF(row.isOverdue) }}
      </template>
    </CommonTable>
  </el-dialog>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
import { PUBLIC_OPINION_PROCESS_COLUMNS } from "@/views/publicOpinionManage/config/tableColumns";
import { HANDLE2_STATUS_LIST, OVERDUE_STATUS_LIST } from "@/views/publicOpinionManage/config/constant";
import { getProcessList } from "@/api/poManage/poInfo";
import { PO_PROCESS_LIST, VIEW_PROCESS_CODE } from "../../config/mapRel";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  // 舆情id
  poId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(["update:modelValue"]);

const tableData = ref([]); // 表格数据
const tableLoading = ref(false); // 表格加载状态

const curStatusF = computed(() => val => {
  const obj = HANDLE2_STATUS_LIST.find(i => i.value.includes(val));
  return { text: obj?.label || "-", style: { color: obj?.color || "#333333", background: obj?.bgColor } };
});
const isOverdueF = computed(() => val => OVERDUE_STATUS_LIST.find(i => i.value === val)?.label || "-");

// 监听弹窗打开，获取数据
watch(
  () => props.modelValue,
  newVal => {
    if (newVal) {
      getTableData();
    }
  }
);

/**
 * 获取表格数据
 */
async function getTableData() {
  tableLoading.value = true;
  const res = await getProcessList({ reportId: props.poId });
  if (res.code === 200) {
    const processData = res.data
      .filter(ele => VIEW_PROCESS_CODE.filter(ele => ele !== "NEW").includes(ele.command))
      .map(ele => ({
        createTime: ele.optTime,
        eventName: PO_PROCESS_LIST.find(i => i.categoriesCode === ele.commandCategory)?.fontLabel || "",
        inChargeList: ele.inChargeList, // 处理单位、处理人
        curStatus: ele.processStep,
        deadlineTime: ele.deadlineTime,
        isOverdue: ele.optStatusTag
      }));
    processData.forEach(ele => {
      ele.inChargeList.forEach(i => {
        tableData.value.push({
          ...ele,
          handleUnit: i.deptInChargeName,
          handler: i.userInChargeName
        });
      });
    });
  }

  tableLoading.value = false;
}

/**
 * 关闭弹窗
 */
function onCancle() {
  emit("update:modelValue", false);
}
</script>

<style lang="scss" scoped>
.table-container {
  padding-top: 5px;
}

.statusClass {
  width: 50px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  border-radius: 3px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
}
</style>
