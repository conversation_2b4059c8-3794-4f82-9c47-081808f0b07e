<template>
  <div class="w-full h-full screen-container">
    <div class="top-container w-full flex justify-center" style="z-index: 30">
      <el-tooltip
        :content="eventDetail.name"
        :disabled="eventDetail.name.length < 14"
        placement="bottom"
        style="z-index: 20"
      >
        <p class="ellipsis w-[25%] h-[50px]">
          {{ eventDetail.name }}
        </p>
      </el-tooltip>
    </div>
    <div class="left-border border-container">
      <div
        class="border-active"
        :class="{ 'border-opacity': borderOpacity }"
        style="left: 4px"
      ></div>
    </div>
    <div class="right-border border-container">
      <div
        class="border-active"
        :class="{ 'border-opacity': borderOpacity }"
        style="right: 4px"
      ></div>
    </div>

    <div class="screen-main-container w-full h-full">
      <div class="w-[478px] h-full ml-[50px] mr-[45px] screen-side-container">
        <div
          class="screen-title mt-[88px] mb-[20px]"
          :class="{ 'title-opacity': titleOpacity }"
        >
          实时舆情展示
        </div>
        <div
          class="info-container"
          style="height: calc(100% - 646px); overflow: hidden"
        >
          <vue3-seamless-scroll
            :list="infoList"
            hover
            style="height: 100%"
            :step="0.2"
            :wheel="true"
          >
            <div
              v-for="(item, index) in infoList"
              :key="item.id"
              class="info-item h-[94px]"
            >
              <div class="info-item-title">
                {{ parseTime(item.publishTime) }}
              </div>
              <div class="line-clamp-2">{{ item.content }}</div>
              <div v-if="item.sensitiveTag" class="info-item-tag">敏感</div>
            </div>
          </vue3-seamless-scroll>
        </div>
        <div
          class="screen-title mt-[20px] mb-[20px]"
          :class="{ 'title-opacity': titleOpacity }"
        >
          舆情视频展示
        </div>
        <div v-if="videoList.length" class="video-list">
          <div
            class="video-item"
            v-for="(item, i) in videoList"
            :key="i"
            @click="viewVideo(item.linkUrl)"
          >
            <ImagePreview :src="item.photoUrl" class="video-img" />
            <div class="video-info">
              <div class="info-content">{{ item.title }}</div>
              <div>
                {{ dayjs(item.publishTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
              <div class="video-platform">来源：{{ item.platformTag }}</div>
            </div>
          </div>
        </div>
        <div
          class="h-[150px]"
          style="
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
          "
          v-else
        >
          暂无数据
        </div>
        <div
          class="screen-title mt-[45px] mb-[20px]"
          :class="{ 'title-opacity': titleOpacity }"
        >
          平台贴文分布
        </div>
        <div
          class="platform-container h-[170px] mb-[60px]"
          style="flex-shrink: 0"
        >
          <div
            class="w-[360px] h-full"
            style="
              display: flex;
              justify-content: space-around;
              flex-wrap: wrap;
            "
          >
            <div
              v-for="(item, index) in platformList"
              :key="index"
              class="platform-item"
              :class="{ active: activePlatformIndex === index }"
            >
              <div>{{ item.label }}</div>
              <div class="platform-item-count">{{ item.value }}</div>
            </div>
          </div>
          <div class="platform-logo w-[105px]">
            <img
              :src="platformList[activePlatformIndex].logo"
              class="mb-[50px]"
              alt=""
            />
          </div>
        </div>
      </div>
      <div class="h-full screen-side-container" style="flex-grow: 1">
        <div
          class="screen-title large mt-[88px] mb-[20px]"
          :class="{ 'title-opacity': titleOpacity }"
        >
          事件情况汇总
        </div>
        <div
          class="h-[300px]"
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
          "
        >
          <div class="sum-card">
            <div class="sum-card-title">发现数</div>
            <div class="sum-card-number">{{ sumInfo.discoveryNum }}</div>
          </div>
          <div class="sum-card">
            <div class="sum-card-title">敏感数</div>
            <div class="sum-card-number" style="color: #ffb769">
              {{ sumInfo.sensitiveNum }}
            </div>
          </div>
          <div class="sum-card">
            <div class="sum-card-title">处置数</div>
            <div class="sum-card-number">{{ sumInfo.dealNum }}</div>
          </div>
          <div class="sum-card">
            <div class="sum-card-title">谣言数</div>
            <div class="sum-card-number">{{ sumInfo.rumorNum }}</div>
          </div>
          <div class="sum-card">
            <div class="sum-card-title">自媒体贴文数</div>
            <div class="sum-card-number">{{ sumInfo.selfMediaNum }}</div>
          </div>
          <div class="sum-card">
            <div class="sum-card-title">媒体报导展示</div>
            <div class="sum-card-number">{{ sumInfo.newsNum }}</div>
          </div>
        </div>
        <div
          class="screen-title large mt-[12px] mb-[12px]"
          :class="{ 'title-opacity': titleOpacity }"
        >
          舆情热度趋势
        </div>
        <div style="font-size: 12px; text-align: right">
          <span style="color: #cce3f9"
            >波峰值： <span style="color: #21e1e7">{{ maxNum }}条</span></span
          >
        </div>
        <div class="w-full h-[230px]" ref="timeRangeChartRef"></div>
        <div
          class="screen-title large mt-[60px] mb-[12px]"
          :class="{ 'title-opacity': titleOpacity }"
        >
          舆情战绩展示
        </div>
        <div class="w-full h-[270px]" ref="showChartRef"></div>
      </div>
      <div class="w-[478px] h-full ml-[45px] mr-[50px] screen-side-container">
        <div
          class="screen-title mt-[88px] mb-[18px]"
          :class="{ 'title-opacity': titleOpacity }"
        >
          事件说明
        </div>
        <div class="h-[400px]">
          <div class="simple-info w-full h-[80px] pl-[19px] pt-[16px]">
            <p>事发时间: {{ parseTime(simpleInfo.beginTime) }}</p>
            <!--            <p>责任区县: {{ simpleInfo.deptName }}</p>-->
          </div>
          <div class="p-[15px] h-[270px]" style="font-size: 14px">
            <p style="color: #8ba9bb">事件简介:</p>
            <div class="scroll-container" ref="evtDescScrollContainer">
              <div
                class="scroll-content"
                :class="{ running: isScroll }"
                ref="evtDescScrollContent"
              >
                {{ simpleInfo.description }}
              </div>
            </div>
            <!-- <div class="h-[230px] text-white overflow-y-auto" style="line-height: 27px;">{{ simpleInfo.description }}</div> -->
          </div>
        </div>
        <div
          class="screen-title mt-[45px] mb-[18px]"
          :class="{ 'title-opacity': titleOpacity }"
        >
          敏感贴文处置情况
        </div>
        <el-carousel
          trigger="click"
          height="94px"
          arrow="never"
          style="flex-shrink: 0"
          @change="handleCarouselChange"
        >
          <el-carousel-item v-for="item in sensitiveList" :key="item">
            <div class="info-item h-[94px]">
              <div class="info-item-title">
                {{ parseTime(item.publishTime) }}
              </div>
              <el-tooltip :content="item.content" placement="top">
                <div class="ellipsis h-[36px]">{{ item.content }}</div>
              </el-tooltip>
            </div>
          </el-carousel-item>
        </el-carousel>
        <!--        <div class="h-[300px]">-->
        <el-table
          :data="activeList"
          height="100%"
          class="mb-[40px]"
          style="flex-grow: 1; background-color: transparent"
          :row-style="handleRowStyle"
          :cell-style="{
            border: 'none !important',
            backgroundColor: 'transparent !important',
          }"
          :header-row-style="{ backgroundColor: 'transparent !important' }"
          :header-cell-style="{
            backgroundColor: 'transparent !important',
            color: '#759ABF !important',
            border: 'none !important',
          }"
        >
          <el-table-column label="事项名称" prop="commandCategory" />
          <el-table-column label="处理单位" prop="dealDept" />
          <el-table-column label="处理人" prop="dealUser" />
          <el-table-column label="当前状态" prop="dealStatus">
            <template #default="{ row }">
              <!-- <span v-if="row.dealStatus === '待处理'" style="color: #0000FF">{{ row.dealStatus }}</span>
                <span v-else-if="row.dealStatus === '跟进中'" style="color: #FFB769">{{ row.dealStatus }}</span>
                <span v-else-if="row.dealStatus === '已处理'" style="color: #00FF00">{{ row.dealStatus }}</span> -->
              <span style="color: #d2e4ed">{{ row.dealStatus }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="截止时间"
            prop="deadlineTime"
            width="180px"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ parseTime(row.deadlineTime) }}
            </template>
          </el-table-column>
        </el-table>
        <!--        </div>-->
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  singleOpinionInfo,
  singleOpinionPlatformNum,
  singleOpinionSimple,
  singleOpinionSensitive,
  singleOpinionSum,
  singleOpinionTimeRange,
  singleOpinionShow,
  singleOpinionVideo,
} from "@/api/eventManagement/index";
import weibo from "@/assets/screen/weibo.svg";
import zhihu from "@/assets/screen/zhihu.svg";
import weixin from "@/assets/screen/weixin.svg";
import douyin from "@/assets/screen/douyin.svg";
import qita from "@/assets/screen/qita.png";
import * as echarts from "echarts";
import dayjs from "dayjs";
import { parseTime } from "../../../utils/ruoyi.js";

const { proxy } = getCurrentInstance();

const infoList = ref([]);
const activePlatformIndex = ref(0);
const platformTimer = ref(null);
const titleTimer = ref(null);
const titleOpacity = ref(true);
const borderTimer = ref(null);
const borderOpacity = ref(true);
const maxNum = ref(0);
const timeRangeChart = ref(null);
const showChart = ref(null);
const sensitiveList = ref([]);
const activeList = ref([]);
const evtDescScrollContainer = ref(null);
const evtDescScrollContent = ref(null);
const isScroll = ref(false);
const videoList = ref([]);
const data = reactive({
  eventDetail: {
    name: "XXX事件",
  },
  platformList: [
    { label: "微博", value: 0, logo: weibo, key: "weiBoNum" },
    { label: "知乎", value: 0, logo: zhihu, key: "zhiHuNum" },
    { label: "微信", value: 0, logo: weixin, key: "weiXinNum" },
    { label: "抖音", value: 0, logo: douyin, key: "douYinNum" },
    { label: "其他网站", value: 0, logo: qita, key: "otherNum" },
  ],
  simpleInfo: {
    beginTime: "",
    deptName: "",
  },
  sumInfo: {
    dealNum: 0,
    discoveryNum: 0,
    newsNum: 0,
    rumorNum: 0,
    selfMediaNum: 0,
    sensitiveNum: 0,
  },
});
const { eventDetail, platformList, simpleInfo, sumInfo } = toRefs(data);

function getInfo() {
  singleOpinionInfo(proxy.$route.query.eventId).then((res) => {
    infoList.value = res.data;
  });
}
function getPlatformNum() {
  singleOpinionPlatformNum(proxy.$route.query.eventId).then((res) => {
    platformList.value.forEach((item) => {
      item.value = res.data[item.key];
    });
  });
}
function getSimpleInfo() {
  singleOpinionSimple(proxy.$route.query.eventId).then((res) => {
    simpleInfo.value = res.data;
    nextTick(() => {
      if (
        evtDescScrollContent.value.clientHeight >
        evtDescScrollContainer.value.clientHeight
      ) {
        // 外框小于内容 绑定事件
        isScroll.value = true;
        evtDescScrollContainer.value.addEventListener(
          "mouseenter",
          handleMouseEnter,
        );
        evtDescScrollContainer.value.addEventListener(
          "mouseleave",
          handleMouseLeave,
        );
      }
    });
  });
}
function getSensitive() {
  singleOpinionSensitive(proxy.$route.query.eventId).then((res) => {
    sensitiveList.value = res.data;
    if (sensitiveList.value.length) {
      activeList.value = sensitiveList.value[0].stepInfos;
    }
  });
}
function getVideo() {
  singleOpinionVideo(proxy.$route.query.eventId).then((res) => {
    videoList.value = res.data;
  });
}
function getSum() {
  singleOpinionSum(proxy.$route.query.eventId).then((res) => {
    sumInfo.value = res.data;
  });
}
function getTimeRange() {
  singleOpinionTimeRange(proxy.$route.query.eventId).then((res) => {
    maxNum.value = res.data.maxNum;
    let option = {
      grid: {
        right: 20,
        top: 10,
        bottom: 30,
      },
      xAxis: {
        type: "category",
        axisTick: {
          show: false,
        },
        axisLabel: {
          margin: 12,
        },
        data: res.data.dataInfos.map((item) => {
          return item.opinionDate;
        }),
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "rgba(47,56,67,0.3)",
          },
        },
      },
      yAxis: {
        type: "value",
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(47,56,67,0.3)",
          },
        },
      },
      series: [
        {
          data: res.data.dataInfos.map((item) => {
            return item.opinionNum;
          }),
          type: "line",
          lineStyle: {
            color: "#21E1E7",
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#21E1E7", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#012A32", // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },
          symbol:
            "image://data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDYzICg5MjQ0NSkgLSBodHRwczovL3NrZXRjaC5jb20gLS0+CiAgICA8dGl0bGU+57yW57uEIDc8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0i5aSn5bGPIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0i5p+Q5LqL5Lu25aSn5bGPIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtODg1LjAwMDAwMCwgLTUwMS4wMDAwMDApIiBmaWxsPSIjMjFFMUU3Ij4KICAgICAgICAgICAgPGcgaWQ9Iue8lue7hC03IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg4ODUuMDAwMDAwLCA1MDEuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICA8Y2lyY2xlIGlkPSLmpK3lnIblvaIiIG9wYWNpdHk9IjAuMjA5MDMwODc4IiBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiPjwvY2lyY2xlPgogICAgICAgICAgICAgICAgPGNpcmNsZSBpZD0i5qSt5ZyG5b2iIiBjeD0iMTAiIGN5PSIxMCIgcj0iNCI+PC9jaXJjbGU+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==",
          symbolSize: 20,
        },
      ],
    };
    timeRangeChart.value = markRaw(echarts.init(proxy.$refs.timeRangeChartRef));
    timeRangeChart.value.setOption(option);
  });
}

function getShow() {
  singleOpinionShow(proxy.$route.query.eventId).then((res) => {
    const data = res.data
      .map((item) => {
        return item.opinionNum;
      })
      .filter((item) => {
        return item !== 0;
      });
    let maxValue = Math.max(...data);

    let yAxisMax = Math.ceil(maxValue * 1.01);

    let option = {
      grid: {
        right: 20,
        top: 30,
        bottom: 30,
      },
      legend: {
        show: true,
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 40,
        textStyle: {
          color: "#CCE3F9",
          fontSize: 12,
          lineHeight: 15,
        },
        top: 0,
        right: 0,
        icon: "rect",
        data: [
          {
            name: "舆情数量",
            itemStyle: {
              color: "#20E1E7",
            },
          },
          {
            name: "已处置舆情数量",
            itemStyle: {
              color: "#0095FF",
            },
          },
        ],
      },
      xAxis: {
        type: "category",
        axisTick: {
          show: false,
        },
        axisLabel: {
          margin: 12,
        },
        data: res.data.map((item) => {
          return item.deptName;
        }),
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "rgba(47,56,67,0.4)",
          },
        },
      },
      yAxis: {
        type: "value",
        minInterval: 1, // 设置最小刻度间隔为 1
        max: yAxisMax, // 设置 y 轴最大值为动态计算的值
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(47,56,67,0.4)",
          },
        },
      },
      series: [
        {
          data: res.data.map((item) => {
            return item.opinionNum;
          }),
          type: "bar",
          stack: "a",
          showBackground: false,
          // backgroundStyle: {
          //   color: {
          //     type: 'linear',
          //     x: 0,
          //     y: 0,
          //     x2: 0,
          //     y2: 1,
          //     colorStops: [{
          //       offset: 0, color: 'rgba(32,193,254,0.00)' // 0% 处的颜色
          //     }, {
          //       offset: 1, color: 'rgba(32,193,254,0.55)' // 100% 处的颜色
          //     }],
          //     global: false // 缺省为 false
          //   }
          // },
          itemStyle: {
            // color: 'transparent',
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(32,193,254,0.00)" },
              { offset: 1, color: "rgba(32, 193, 254, 0.55" },
            ]),
          },
          barWidth: 20,
          label: { show: true, position: "top", color: "#20E1E7" },
        },
        {
          name: "舆情数量",
          data: res.data.map((item) => {
            return 0;
          }),
          type: "bar",
          stack: "a",
          itemStyle: {
            borderColor: "#20E1E7",
            borderWidth: 3,
          },
          barWidth: 20,
        },
        {
          data: res.data.map((item) => {
            return item.dealNum;
          }),
          type: "bar",
          stack: "b",
          itemStyle: {
            color: "transparent",
          },
          barGap: "-100%",
          barWidth: 20,
        },
        {
          name: "已处置舆情数量",
          data: res.data.map((item) => {
            return 0;
          }),
          type: "bar",
          stack: "b",
          itemStyle: {
            borderColor: "#0095FF",
            borderWidth: 3,
          },
          barWidth: 20,
        },
      ],
    };
    showChart.value = markRaw(echarts.init(proxy.$refs.showChartRef));
    showChart.value.setOption(option);
  });
}

function handleCarouselChange(cur, prev) {
  activeList.value = sensitiveList.value[cur].stepInfos;
}
function handleRowStyle(data) {
  let res = {
    backgroundColor: "transparent !important",
    color: "#D2E4ED !important",
  };
  if (data.rowIndex % 2 === 0) {
    res.backgroundImage =
      "linear-gradient(270deg, rgba(40,194,255,0.00) 1%, rgba(40,194,255,0.22) 100%";
  }
  return res;
}
function viewVideo(url) {
  window.location.href = url;
}

getInfo();
getPlatformNum();
getSimpleInfo();
getSensitive();
getSum();
getTimeRange();
getShow();
getVideo();

function onresize() {
  if (showChart.value) {
    showChart.value.resize();
  }
  if (timeRangeChart.value) {
    timeRangeChart.value.resize();
  }
}

onMounted(() => {
  platformTimer.value = setInterval(() => {
    if (activePlatformIndex.value === 4) {
      activePlatformIndex.value = 0;
    } else {
      activePlatformIndex.value += 1;
    }
  }, 3000);

  titleTimer.value = setInterval(() => {
    titleOpacity.value = !titleOpacity.value;
  }, 1000);

  borderTimer.value = setInterval(() => {
    borderOpacity.value = !borderOpacity.value;
  }, 1000);

  eventDetail.value.name = proxy.$route.query.eventName;
  window.addEventListener("resize", onresize);
});

const handleMouseEnter = () => {
  isScroll.value = false;
  evtDescScrollContainer.value.scrollTop = 0;
};

const handleMouseLeave = () => {
  isScroll.value = true;
};

onBeforeUnmount(() => {
  window.removeEventListener("resize", onresize);
  evtDescScrollContainer.value.removeEventListener(
    "mouseenter",
    handleMouseEnter,
  );
  evtDescScrollContainer.value.removeEventListener(
    "mouseleave",
    handleMouseLeave,
  );
});
</script>

<style scoped lang="scss">
.screen-container {
  position: relative;
  overflow: hidden;
  background-color: #010f24;
}
.border-container {
  width: 33%;
  height: calc(100% - 24px);
  margin: 12px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  z-index: 1;
  &.left-border {
    background-image: url("@/assets/screen/border-left.png");
    left: 0;
  }
  &.right-border {
    background-image: url("@/assets/screen/border-right.png");
    right: 0;
  }
  .border-active {
    width: 5px;
    height: 17px;
    background-image: url("@/assets/screen/border-active.png");
    position: absolute;
    top: 0;
    bottom: 0;
    margin-top: auto;
    margin-bottom: auto;
    z-index: 20;
    opacity: 1;
    transition: opacity 1s ease;
    &.border-opacity {
      opacity: 0;
    }
  }
}

.top-container {
  height: 148px;
  background: url("@/assets/screen/back-top.png") no-repeat;
  background-size: 100% 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 5;
  p {
    margin-top: -70px;
    line-height: 37px;
    font-size: 37px;
    font-family: PangMenZhengDao;
    letter-spacing: 3px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 50%, #86cbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    //text-shadow: 0 2px 21px rgba(70,174,215,0.82);
    text-align: center;
  }
}
.screen-main-container {
  display: flex;
  justify-content: space-between;
  z-index: 10;
}
.screen-title {
  width: 100%;
  line-height: 18px;
  color: #feffff;
  text-align: center;
  font-size: 19px;
  font-family: PangMenZhengDao;
  position: relative;
  &:before {
    content: "";
    width: 160px;
    height: 18px;
    background: url(@/assets/screen/title-border-active.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    transition: all 1s ease;
  }
  &:after {
    content: "";
    width: 160px;
    height: 18px;
    background: url(@/assets/screen/title-border-active.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    transform: rotate(180deg);
  }
  &.large {
    &:before {
      width: 320px;
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
    }
    &:after {
      width: 320px;
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
    }
  }
  &.title-opacity {
    &:before {
      background: url(@/assets/screen/title-border.png) no-repeat;
      background-size: 100% 100%;
    }
    &:after {
      background: url(@/assets/screen/title-border.png) no-repeat;
      background-size: 100% 100%;
    }
  }
}
.screen-side-container {
  display: flex;
  flex-direction: column;
  z-index: 20;
}
.info-item {
  margin-bottom: 12px;
  padding: 16px;
  background: url(@/assets/screen/info-back.png) no-repeat;
  background-size: 100% 100%;
  color: #a3c1d1;
  font-size: 13px;
  position: relative;
  .info-item-title {
    margin-bottom: 8px;
    font-size: 14px;
    color: #feffff;
  }
  .info-item-tag {
    width: 62px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    font-size: 15px;
    color: #feffff;
    position: absolute;
    right: 0;
    top: 0;
    background: url(@/assets/screen/info-tag.png) no-repeat;
    background-size: 100% 100%;
  }
}
.platform-container {
  display: flex;
  justify-content: space-between;
}
.platform-item {
  width: 33%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  color: #feffff;
  position: relative;
  &.active {
    .platform-item-count {
      color: #02e8ff;
    }
    &:after {
      content: "";
      width: 8px;
      height: 10px;
      background: url(@/assets/screen/platform-active.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      bottom: 0;
    }
  }
  .platform-item-count {
    margin-top: 8px;
    font-size: 29px;
    text-shadow: 0 0 11px rgba(87, 198, 255, 0.76);
    font-family: PangMenZhengDao;
  }
}
.platform-item:nth-child(4) {
  margin-top: 10px;
  margin-left: 40px;
}
.platform-item:nth-child(5) {
  margin-top: 10px;
  margin-right: 40px;
}
.platform-logo {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url(@/assets/screen/platform-back.svg) no-repeat;
  background-size: 100% 100%;
  img {
    animation: logo-move 3s infinite ease;
  }
  @keyframes logo-move {
    0% {
      transform: translate(0px, -10px);
    }
    50% {
      transform: translate(0px, 10px);
    }
    100% {
      transform: translate(0px, -10px);
    }
  }
}
.simple-info {
  font-size: 14px;
  color: #feffff;
  background: url(@/assets/screen/simple-back.svg) no-repeat;
  background-size: 100% 100%;
}
.sum-card {
  width: 33%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: PangMenZhengDao;
  .sum-card-title {
    margin-bottom: 8px;
    color: #fff;
    position: relative;
    &:before {
      content: "";
      width: 8px;
      height: 10px;
      background: url(@/assets/screen/platform-active.svg) no-repeat;
      background-size: 100% 100%;
      transform: rotate(90deg);
      position: absolute;
      left: -12px;
      top: 4px;
      z-index: 10;
    }
    &:after {
      content: "";
      width: 8px;
      height: 10px;
      background: url(@/assets/screen/platform-active.svg) no-repeat;
      background-size: 100% 100%;
      transform: rotate(-90deg);
      position: absolute;
      right: -12px;
      top: 4px;
      z-index: 10;
    }
  }
  .sum-card-number {
    margin-bottom: 23px;
    font-size: 29px;
    color: #02e8ff;
    text-shadow: 0 0 9px rgba(87, 242, 255, 0.4);
  }
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.scroll-container {
  height: 230px;
  color: white;
  overflow: hidden;
  line-height: 27px;
  position: relative; /* 确保子元素的绝对定位相对于此元素 */
}

.scroll-content {
  position: absolute; /* 使内容脱离文档流，以便动画控制 */
  top: 0;
  left: 0;
  width: 100%;
  &.running {
    animation: scroll 15s linear infinite; /* 调整滚动速度 */
  }
}

@keyframes scroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(calc(-100% + 230px)); /* 计算滚动距离 */
  }
}

.video-list::-webkit-scrollbar {
  width: 0;
}

/* 隐藏水平滚动条 */
.video-list::-webkit-scrollbar {
  height: 0;
}
.video-list {
  width: inherit;
  /*height: inherit;*/
  overflow-y: scroll;

  .video-item {
    width: inherit;
    height: 149px;
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 21px;

    .video-img {
      width: 131px;
      height: 149px;
      background: linear-gradient(
        180deg,
        rgba(0, 41, 97, 0) 0%,
        rgba(2, 94, 148, 0.41) 100%
      );
      border: 1px solid #00678c;
      padding: 5px;
      margin-right: 22px;
    }

    .video-info {
      height: 149px;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      font-size: 13px;
      color: #c5d3db;
      position: relative;

      .info-content {
        width: inherit;
        font-size: 14px;
        color: #ffb769;
        display: -webkit-box; /* 将容器以弹性盒子形式布局 */
        -webkit-line-clamp: 2; /* 限制文本显示为两行 */
        -webkit-box-orient: vertical; /* 将弹性盒子的主轴方向设置为垂直方向 */
        overflow: hidden; /* 隐藏容器中超出部分的内容 */
        text-overflow: ellipsis; /* 超出容器范围的文本显示省略号 */
        margin-bottom: 11px;
      }

      .video-platform {
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
}

:deep(.el-table) {
  --el-table-border-color: none;
}

.el-carousel {
  :deep(.el-carousel__indicators) {
    width: 100%;
    bottom: 10px;
    text-align: center;
    .el-carousel__indicator {
      --el-carousel-indicator-padding-vertical: 0;
      --el-carousel-indicator-padding-horizontal: 0;
      &.is-active {
        .el-carousel__button {
          background-color: #1991e8;
        }
      }
      .el-carousel__button {
        background-color: #0a4266;
        opacity: 1;
      }
    }
  }
}
</style>
