import { login, logout, getInfo } from "@/api/login";
import { getToken, setToken, removeToken } from "@/utils/auth";
import defAva from "@/assets/images/profile.jpg";
import { getConfigKey } from "@/api/system/config";

// 状态管理依赖
import { useUserStore as useFlyFlowStore } from "@/views/flyflow/stores/user";

const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken(),
    id: "",
    name: "",
    nickName: "",
    deptName: "",
    deptType: "",
    avatar: "",
    roles: [],
    permissions: [],
    signature: "",
    toScreeRoles: [],
    isWJLeader: false,
  }),
  actions: {
    // 登录
    login(userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid)
          .then((res) => {
            setToken(res.token);
            this.token = res.token;

            const userFlyFlowStore = useFlyFlowStore();

            userFlyFlowStore.token = res.token;

            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            const user = res.user;
            const avatar =
              user.avatar == "" || user.avatar == null
                ? defAva
                : import.meta.env.VITE_APP_BASE_API + user.avatar;

            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.roles;
              this.permissions = res.permissions;
            } else {
              this.roles = ["ROLE_DEFAULT"];
            }
            this.id = user.userId;
            this.name = user.userName;
            this.nickName = user.nickName;
            this.avatar = avatar;
            this.signature = user.signature;
            this.deptName = user?.dept?.deptName;
            this.deptType = user?.dept?.deptType;
            this.toScreeRoles = res.config?.toScreeRoles || [];

            const userFlyFlowStore = useFlyFlowStore();
            userFlyFlowStore.userId = user.userId;
            userFlyFlowStore.nickname = user.nickName;

            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    async getIsWJLeader() {
      // console.log('',);

      const res = await getConfigKey("isToScreen");
      console.log("this.roles", this.roles);
      console.log("this.toScreeRoles", this.toScreeRoles);
      console.log("res.msg", res.msg);

      console.log(
        "仓库getIsWJLeader",
        this.roles?.some((role) => role === res.msg) &&
          this.toScreeRoles?.some((role) => role === res.msg)
      );
      this.isWJLeader =
        this.roles?.some((role) => role === res.msg) &&
        this.toScreeRoles?.some((role) => role === res.msg);
    },
    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            this.token = "";
            this.roles = [];
            this.permissions = [];
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
});

export default useUserStore;
