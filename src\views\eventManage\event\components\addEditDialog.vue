<template>
  <el-dialog
    v-model="addEditVisible"
    @closed="onCancel"
    :title="dialogTitle"
    width="700"
  >
    <el-form
      ref="addEditFormRef"
      :model="dialogForm"
      label-width="140"
      :rules="ADD_EDIT_RULES"
    >
      <el-form-item label="事件名称" prop="eventName">
        <el-input
          clearable
          v-model="dialogForm.eventName"
          maxlength="50"
          show-word-limit
          placeholder="请输入事件名称"
        />
      </el-form-item>
      <el-form-item label="事件类型" prop="eventType">
        <el-select
          clearable
          v-model="dialogForm.eventType"
          placeholder="请选择事件类型"
        >
          <el-option
            v-for="item in eventTypeOptions"
            :key="item.value"
            :label="item?.label"
            :value="item?.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="事件开始时间" prop="startTime">
        <el-date-picker
          v-model="dialogForm.startTime"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetime"
          :disabled-date="disabledDateStartRange"
          placeholder="请选择事件开始时间"
        />
      </el-form-item>
      <el-form-item label="事件概述" prop="category">
        <el-input
          v-model="dialogForm.content"
          :rows="4"
          :autosize="{ minRows: 4, maxRows: 20 }"
          show-word-limit
          maxlength="500"
          type="textarea"
          placeholder="请输入事件概述"
        />
      </el-form-item>

      <el-form-item label="配置舆情采集策略"> </el-form-item>

      <el-row class="h-[60px]">
        <div class="w-6/7 flex flex-col relative">
          <el-form-item label="选择方案" prop="choiceScheme">
            <el-select
              clearable
              v-model="dialogForm.choiceScheme"
              placeholder="请选择方案"
            >
              <el-option
                v-for="item in choiceSchemeOptions"
                :key="item.value"
                :label="item?.label"
                :value="item?.value"
              />
            </el-select>
          </el-form-item>
          <div class="absolute top-[35px] left-[140px] text-[12px]">
            <span>若无可用方案，请点击创建方案按钮进行舆情采集方案的创建 </span>
          </div>
        </div>

        <div class="w-1/7 h-[32px] flex justify-center items-center">
          <el-button type="primary" link @click="handleAddScheme"
            >创建方案</el-button
          >
        </div>
      </el-row>

      <el-form-item label="选择钉钉群" prop="dingTalkGroup">
        <el-select
          clearable
          v-model="dialogForm.dingTalkGroup"
          placeholder="请选择钉钉群"
        >
          <el-option
            v-for="item in dingTalkGroupOptions"
            :key="item.value"
            :label="item?.label"
            :value="item?.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择微信群" prop="wechatGroup">
        <el-select
          clearable
          v-model="dialogForm.wechatGroup"
          placeholder="请选择微信群"
        >
          <el-option
            v-for="item in wechatGroupOptions"
            :key="item.value"
            :label="item?.label"
            :value="item?.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <div class="w-full flex justify-end">
          <el-button @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onSubmit"> 提交 </el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import { ADD_EDIT_RULES } from "../config/index.js";
import {
  addEvent,
  editEvent,
  // getEventTypes,
  // getChoiceSchemes,
  // getDingTalkGroups,
  // getWechatGroups,
} from "@/api/eventManagement/index.js";

const emit = defineEmits(["refreshList"]);
const dialogType = ref("add");
const addEditFormRef = ref(null); // 表单组件
const addEditVisible = ref(false);
const dialogForm = ref({
  eventName: "",
  eventType: "",
  startTime: "",
  content: "",
  choiceScheme: "",
  wechatGroup: "",
  dingTalkGroup: "",
});

const eventTypeOptions = ref([]);
const choiceSchemeOptions = ref([]);
const dingTalkGroupOptions = ref([]);
const wechatGroupOptions = ref([]);

const { proxy } = getCurrentInstance();

const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新增事件" : "编辑事件";
});

/**
 * 禁用开始时间选择框的日期
 */
const disabledDateStartRange = (date) => {
  return date.getTime() < new Date().getTime();
};

// 弹框打开事件
const openDialog = async (type, row) => {
  addEditVisible.value = true;
  dialogType.value = type;
  resetForm();
  if (type == "edit") {
    // 编辑
    if (row.id) {
      dialogForm.value = row;
    }
  }
};
const resetForm = () => {
  dialogForm.value = {
    eventName: "",
    eventType: "",
    startTime: "",
    content: "",
    choiceScheme: "",
    wechatGroup: "",
    dingTalkGroup: "",
  };
};

const getSelectOptions = async () => {
  // const promises = [
  //   getEventTypes,
  //   getChoiceSchemes,
  //   getDingTalkGroups,
  //   getWechatGroups,
  // ];
  // const resArr = await Promise.all(promises);
  // if (resArr.every((ele) => ele.code === 200)) {
  //   eventTypeOptions.value = resArr[0].data || [];
  //   choiceSchemeOptions.value = resArr[1].data || [];
  //   dingTalkGroupOptions.value = resArr[2].data || [];
  //   wechatGroupOptions.value = resArr[3].data || [];
  // }
};

const handleAddScheme = () => {
  proxy.$router.push("/eventManagement/addScheme");
};

const onCancel = () => {
  addEditVisible.value = false;
};
const onSubmit = () => {
  addEditFormRef.value.validate(async (val) => {
    if (val) {
      console.log("表单数据", dialogForm.value);
      const params = {
        ...dialogForm.value,
      };

      if (dialogType.value === "add") {
        const res = await addEvent(params);
        if (res.code === 200) {
          proxy.$message.success(res.msg);
          addEditVisible.value = false;
          emit("refreshList");
        }
      }
      if (dialogType.value === "edit") {
        const res = await editEvent(params);
        if (res.code === 200) {
          proxy.$message.success(res.msg);
          addEditVisible.value = false;
          emit("refreshList");
        }
      }
    }
  });
};

onMounted(async () => {
  await getSelectOptions();
});

defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
.addTitle {
  font-size: 16px;
  font-family:
    PingFangSC-Medium,
    PingFang SC;
  font-weight: 600;
  color: #1f2329;
  line-height: 22px;
}

.addDialogBtn {
  width: 80px;
  height: 32px;
  line-height: 20px;
  background: #01b197;
  border-radius: 5px;
  font-size: 14px;
  color: #ffffff;
}
.cancelDialogBtn {
  width: 80px;
  height: 32px;
  line-height: 32px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #cfd2d6;
  font-size: 14px;
  color: #1f2329;
}

:deep(.el-input__prefix) {
  width: 0;
  display: none;
}

:deep(.el-input__inner) {
  padding: 0;
}
</style>
