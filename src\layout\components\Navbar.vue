<template>
  <div class="navbar">
    <div class="home-logo pointer" @click="toControl">
      <img src="@/assets/images/logo.svg" class="w-[24px]" alt="logo" />
      <div class="home-logo-title">{{ defaultSettings.title }}</div>
    </div>

    <!-- <hamburger
      id="hamburger-container"
      :is-active="appStore.sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    /> -->
    <!-- <breadcrumb
      id="breadcrumb-container"
      class="breadcrumb-container"
      v-if="!settingsStore.topNav"
    /> -->
    <TopNav id="topmenu-container" class="topmenu-container" v-if="settingsStore.topNav" />

    <div class="right-menu">
      <!-- <el-button @click="toControl" link >工作台</el-button> -->
      <template v-if="appStore.device !== 'mobile'">
        <!-- <header-search id="header-search" class="right-menu-item" />

        <el-tooltip content="源码地址" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->

        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
      </template>

      <div class="backHome" @click="toControl">
        <img class="w-[13px] mr-[7px]" src="@/assets/images/home.png" alt="">
        <span class="backText">回到首页</span>
      </div>

      <UserDropdown />
    </div>
  </div>
</template>

<script setup>
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import HeaderSearch from "@/components/HeaderSearch";
import RuoYiGit from "@/components/RuoYi/Git";
import RuoYiDoc from "@/components/RuoYi/Doc";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import defaultSettings from "@/settings";
import logo from "@/assets/logo/logo.svg";
// import { getToken } from '@/utils/auth';
// import { encryptTokenAES } from '@/utils/crypto';
import { toMainSystem } from "@/utils/auth";
import Help from "@/assets/svg/help.vue";
import UserDropdown from "./UserDropdown"

const router = useRouter();

const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const firstLetter = computed(() => {
  return userStore?.nickname?.substring(0, 1) || ""; // 获取 nickname 的第一个字
});


function toggleSideBar() {
  appStore.toggleSideBar();
}

const toControl = () => {
  // let openedWindow = window.open('', 'mainSystemWindow')
  // let mainWindow = window.opener;  // `window.opener` 是指向打开当前窗口的父窗口

  // if (mainWindow && openedWindow) {
  //   mainWindow.focus();  // 激活主应用窗口
  // } else {
  //   const urlPrefix = window.location.origin;
  //   openedWindow.location.href = urlPrefix;
  // }

  router.push("/");
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";

.home-logo {
  display: flex;
  float: left;
  align-items: center;
  height: 100%;
  font-family: "YouSheBiaoTiHei";
  width: fit-content;
  font-size: 18px;
  gap: 10px;

  .home-logo-title {
    font-family: YouSheBiaoTiHei;
    font-size: 18px;
    color: #30334b;
  }
}
.navbar {
  height: 64px;
  overflow: hidden;
  position: relative;
  background: transparent;
  margin-left: -$base-sidebar-width;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: calc(210px + 10vw);
    height: 64px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    display: flex;
    align-items: center;
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      font-size: 18px;
      color: #ffffff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .backHome {
      display: flex;
      align-items: center;
      cursor: pointer;

      .backText {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #30334B;
      }

      &::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 27px;
        background-color: #E0E1E2;
        margin: 0 21px;
      }
    }

    @media screen and (max-width: 668px) {
      .backHome {
        display: none;
      }
    }


  }
}
</style>
