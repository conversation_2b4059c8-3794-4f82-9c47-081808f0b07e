<template>
  <div class="leftInfo-container flex flex-col">
    <div class="appTitle">应用管理</div>
    <div class="appListWrapper flex-1">
      <div class="appItem" v-for="item in appMenus" :key="item.path">
        <template v-if="item.path === '/opinionScreen'">
          <a
            :href="`${originUrl}${baseUrl === '/' ? '' : baseUrl}${item.path}`"
            target="screen"
          >
            <div class="appIcon">
              <img class="navItemIcon" :src="item.icon" alt="icon" />
            </div>
            <div class="navItemTitle">{{ item.title }}</div>
          </a>
        </template>
        <template v-else>
          <router-link :to="item.path">
            <div class="appIcon">
              <img class="navItemIcon" :src="item.icon" alt="icon" />
            </div>
            <div class="navItemTitle">{{ item.title }}</div>
          </router-link>
        </template>
      </div>
    </div>

    <div class="appTitle">近7日舆情数量走势</div>
    <div class="lineWrapper">
      <SevenPoTrendLineChart :line-data="lineData" />
    </div>
  </div>
</template>

<script setup>
import avatarIcon from "@/assets/images/avatar.svg";
import { getMenuList, getUserSevenDayOpinionReport } from "@/api/home";
import SevenPoTrendLineChart from "./SevenPoTrendLineChart.vue";

const appMenus = ref([]); // 应用列表
const baseUrl = import.meta.env.VITE_BASE_URL;
const originUrl = window.location.origin;

const lineData = ref([]);

/**
 * 获取配置的应用
 */
async function getApp() {
  const res = await getMenuList();
  if (res.code === 200) {
    appMenus.value = res.menus.map((ele) => ({
      icon: !!ele.uploadIconUrl
        ? import.meta.env.VITE_APP_BASE_API + ele.uploadIconUrl
        : avatarIcon,
      title: ele.menuName,
      path: `/${ele.redirectUrl}` || "",
    }));
  }
}

/**
 * 获取折线图数据
 */
async function getLineOption() {
  const res = await getUserSevenDayOpinionReport();
  if (res.code === 200) {
    lineData.value = res.data.map((i) => [i.date, i.count]);
    console.log("lineData.value ", lineData.value);
  }
}

getApp();
getLineOption();


</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;

.leftInfo-container {
  width: 100%;
  height: 100%;

  .appTitle {
    width: px2vw(467);
    height: px2vw(58);
    background: url("@/assets/images/home/<USER>") no-repeat center/cover;
    padding: px2vw(13) 0 px2vw(19) px2vw(65);
    margin-top: px2vw(30);

    font-family: YouSheBiaoTiHei;
    font-size: px2vw(20);
    color: #eaf4ff;
    line-height: px2vw(26);
  }

  .appListWrapper {
    display: flex;
    flex-wrap: wrap;
    padding: 0 px2vw(15);
    margin-top: px2vw(40);
    overflow: auto;

    // 浏览器滚动条
    &::-webkit-scrollbar {
      width: 0;
    }

    .appItem {
      width: 15%;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      text-align: center;
      margin-bottom: px2vw(34);
      cursor: pointer;

      &:not(:nth-child(6n)) {
        margin-right: 1.5%;
      }

      .appIcon {
        width: px2vw(94);
        height: px2vw(91);
        background: url("@/assets/images/home/<USER>") no-repeat center/cover;

        &:hover {
          background:
            url("@/assets/images/home/<USER>") no-repeat,
            url("@/assets/images/home/<USER>") no-repeat,
            url("@/assets/images/home/<USER>") no-repeat;
          background-size: cover, 100%, 100%;
          background-position:
            center center,
            center px2vw(30),
            center px2vw(20);
          animation: float-line 1s infinite ease-in-out;

          @keyframes float-line {
            0% {
              background-position:
                center center,
                center px2vw(30),
                center px2vw(20);
            }

            100% {
              background-position:
                center center,
                center px2vw(10),
                center 0;
            }
          }
        }

        .navItemIcon {
          margin-top: px2vw(24);
          width: px2vw(33);
          height: px2vw(34);
        }
      }

      .navItemTitle {
        width: 100%;
        font-family: YouSheBiaoTiHei;
        font-size: px2vw(16);
        color: #e1f2ff;
        line-height: px2vw(21);
        margin-top: px2vw(7);
      }
    }
  }

  .lineWrapper {
    height: px2vw(230);
  }
}
</style>
