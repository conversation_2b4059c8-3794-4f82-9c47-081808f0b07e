<template>
  <div class="filterCollapse-container">
    <el-dropdown
      trigger="click"
      :hide-on-click="false"
      :teleported="false"
      placement="bottom-end"
      @visible-change="(val) => (visible = val)"
    >
      <div
        class="filterCollapseWrapper flex-center"
        :style="{ borderColor: visible ? '#0070FF' : '' }"
      >
        <svg-icon
          class-name="w-[13px] h-[13px]"
          icon-class="filter"
          :color="visible ? '#0070FF' : ''"
        />
      </div>
      <template #dropdown>
        <div class="labelTip mb-[14px]">更多筛选</div>
        <section class="mb-[12px]">
          <el-input
            v-model="searchCondition.poId"
            placeholder="请输入舆情编号"
            :prefix-icon="Search"
            style="width: 224px"
            clearable
            @change="$emit('search')"
          />
        </section>
        <section class="mb-[12px]">
          <el-input
            v-model="searchCondition.poEvent"
            placeholder="请输入舆情事件"
            :prefix-icon="Search"
            style="width: 224px"
            clearable
            @change="$emit('search')"
          />
        </section>
        <section class="mb-[12px]">
          <el-input
            v-model="searchCondition.poContent"
            placeholder="请输入舆情内容"
            :prefix-icon="Search"
            style="width: 224px"
            clearable
            @change="$emit('search')"
          />
        </section>
        <section class="mb-[12px]">
          <el-select
            v-model="searchCondition.msgFrom"
            placeholder="请选择来源"
            clearable
            style="width: 224px"
            @change="$emit('search')"
          >
            <template #prefix>来源</template>
            <el-option label="全部" value="all" />
            <el-option
              v-for="item in msgFromOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </section>
        <section class="mb-[12px]">
          <el-select
            v-model="searchCondition.mediaType"
            placeholder="请选择媒体类型"
            clearable
            style="width: 224px"
            @change="$emit('search')"
          >
            <template #prefix>媒体类型</template>
            <el-option label="全部" value="all" />
            <el-option
              v-for="item in mediaTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </section>
        <section>
          <el-select
            v-model="searchCondition.handleType"
            placeholder="请选择处置状态"
            clearable
            style="width: 224px"
            @change="$emit('search')"
          >
            <template #prefix>处置状态</template>
            <el-option label="全部" value="all" />
            <el-option
              v-for="item in HANDLE_STATUS_LIST"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </section>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";
import { HANDLE_STATUS_LIST } from "@/views/publicOpinionManage/config/constant.js";

defineProps({
  // 搜索条件
  searchCondition: {
    type: Object,
    required: true,
  },
  // 舆情来源字典
  msgFromOption: {
    type: Array,
    required: true,
  },
  // 媒体类型字典
  mediaTypeOption: {
    type: Array,
    required: true,
  },
});
defineEmits(["search"]);

const visible = ref(false);
</script>

<style lang="scss" scoped>
.filterCollapseWrapper {
  width: 32px;
  height: 32px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #cfd2d6;
  cursor: pointer;
}

:deep(.el-dropdown__popper) {
  width: 307px;
  .el-dropdown__list {
    padding: 17px 0 23px 23px;
    .labelTip {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 14px;
      color: #1f2329;
      line-height: 20px;
    }
  }
}
</style>
