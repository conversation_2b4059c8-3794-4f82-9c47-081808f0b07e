<template>
  <div class="app-container">
    <CommonTable
      :columns="applicationColumns"
      :data="tableData"
      :total="total"
      :loading="loading"
      :page-params="pageParams"
      :show-default-btn="true"
      :show-operation-column="true"
      :show-pagination="true"
      :header-style="{ fontWeight: 'bold' }"
      @confirmEvent="deleteApplicationItem"
      @handleEdit="handleEdit"
    >
      <template #search>
        <el-form
          :model="searchParams"
          ref="queryRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="应用名称" prop="appName">
            <el-input
              v-model="searchParams.appName"
              placeholder="请输入应用名称"
              clearable
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item label="是否启用" prop="status">
            <el-select
              v-model="searchParams.status"
              placeholder="请选择是否启用"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in [
                  { label: '是', value: '1' },
                  { label: '否', value: '0' },
                ]"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="应用类别" prop="appType">
            <el-select
              v-model="searchParams.appType"
              placeholder="请选择应用类别"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in app_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">
              搜索
            </el-button>
            <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </template>

      <template #iconSlot="{ row }">
        <el-image
          v-if="row?.appImg"
          :src="baseUrl + row?.appImg"
          :preview-src-list="[baseUrl + row?.appImg]"
          preview-teleported
          style="width: 40px; height: 40px"
        />
      </template>
      <template #appTypeSlot="{ row }">
        <span>{{ getDict(row?.appType, app_type) }}</span>
      </template>
      <template #isEnableSlot="{ row }">
        <el-switch
          v-model="row.status"
          active-value="1"
          inactive-value="0"
          @click="updateApplicationStatus(row.appId)"
        />
      </template>

      <template #button>
        <el-button type="primary" @click="addApplicationSet"> 新增 </el-button>
      </template>
    </CommonTable>
    <div>
      <el-dialog
        v-model="operationDialog"
        @closed="resetForm"
        :title="dialogTitle"
        width="700"
      >
        <el-form
          ref="operationFormRef"
          :model="operationForm"
          :rules="operationFormRule"
          label-width="80"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="应用名称" prop="appName">
                <el-input
                  clearable
                  v-model="operationForm.appName"
                  placeholder="请输入应用名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="应用类别" prop="appType">
                <el-select
                  clearable
                  v-model="operationForm.appType"
                  placeholder="请选择应用类别"
                >
                  <el-option
                    v-for="dict in app_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item
            v-if="operationForm.appType === '1'"
            label="父级菜单"
            prop="menuId"
          >
            <el-select
              clearable
              v-model="operationForm.menuId"
              placeholder="请选择父级菜单"
            >
              <el-option
                v-for="dict in sysNameArr"
                :key="dict.searchValue"
                :label="dict?.searchName"
                :value="dict?.searchValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="operationForm.appType !== '3'"
            label="所属系统"
            prop="sysId"
          >
            <el-select
              clearable
              v-model="operationForm.sysId"
              placeholder="请选择所属系统"
            >
              <el-option
                v-for="(dict, index) in sysOptions"
                :key="index"
                :label="dict.searchName"
                :value="dict.searchValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="operationForm.appType === '2'"
            label="类别"
            prop="category"
          >
            <el-radio-group v-model="operationForm.category">
              <el-radio value="1">链接</el-radio>
              <el-radio value="2">安装包</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="应用链接"
            prop="appUrl"
            v-if="
              operationForm.appType !== '2' ||
              (operationForm.appType === '2' && operationForm.category === '1')
            "
          >
            <el-input
              clearable
              v-model="operationForm.appUrl"
              placeholder="请输入应用链接"
            />
          </el-form-item>
          <el-form-item
            label="安装包"
            prop="attachments"
            v-if="
              operationForm.appType === '2' && operationForm.category === '2'
            "
          >
            <fileUpload
              :limit="1"
              :fileSize="1024"
              v-model:modeldList="operationForm.attachments"
              :fileType="[
                'exe',
                'msi',
                'dmg',
                'pkg',
                'deb',
                'rpm',
                'tar.gz',
                'zip',
                'apk',
                'ipa',
                'jar',
                'iso',
                'bin',
              ]"
            ></fileUpload>
          </el-form-item>
          <el-form-item label="是否启用" prop="status">
            <el-switch
              v-model="operationForm.status"
              active-value="1"
              inactive-value="0"
            />
          </el-form-item>
          <el-form-item prop="sort">
            <template #label>
              <div class="flex items-center">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="仅允许输入整数，数值越大越靠前"
                  placement="top"
                >
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>排&emsp;序</span>
              </div>
            </template>

            <el-input-number
              v-model="operationForm.sort"
              :precision="0"
              :min="0"
              clearable
            />
          </el-form-item>

          <el-form-item label="应用图标" prop="icon">
            <ImageUpload
              :limit="1"
              v-model="operationForm.appImg"
            ></ImageUpload>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              clearable
              type="textarea"
              v-model="operationForm.remark"
              placeholder="请输入应用备注"
            />
          </el-form-item>
          <el-form-item>
            <div class="w-full flex justify-end">
              <el-button @click="cancelForm">取消</el-button>
              <el-button type="primary" @click="submitForm"> 提交 </el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import CommonTable from "./CommonTable/index.vue";
import { applicationColumns, operationFormRule } from "./content.js";
import { ElMessage } from "element-plus";
import { cloneDeepForm } from "@/utils/index.js";
import { cloneDeep } from "lodash";
import { getDict, useDict } from "@/utils/dict.js";
import ImageUpload from "@/components/ImageUpload/index.vue";
import { useCommonTable } from "@/hooks/useCommonTable.js";
import {
  addApp,
  deleteApp,
  getAppList,
  updateApp,
  getAppDetail,
  getSysOptions,
  setAppStatus,
  getFirstMenuList,
} from "@/api/system/appConfig.js";
const { proxy } = getCurrentInstance();
const { app_type } = proxy.useDict("app_type");
const baseUrl = import.meta.env.VITE_APP_PORTAL_API;
const operationFormRef = ref(null);
const operationDialog = ref(false);
const operationForm = ref({
  appName: "",
  appUrl: "",
  appType: "1",
  sysId: "",
  appImg: "",
  status: "1",
  remark: "",
  menuId: null,
  sort: 1,
  category: "1",
  attachments: [],
  // helpDocumentList: [],
});
const sysNameArr = ref([]);
const { operationFormCopy } = cloneDeepForm({
  operationForm: operationForm.value,
});
const dialogTitle = ref();

/* 表单搜索条件对象 */
const searchParams = ref({
  appName: "",
  status: null,
  appType: null,
});

const { tableData, pageParams, loading, total, initTableList } = useCommonTable(
  {
    onLoadData: getAppList,
    showPagination: true,
    searchParams: searchParams,
    isRealTimeListening: false,
  }
);

const sysOptions = ref([]);
const appLoading = ref(false);
async function initSysOptions() {
  try {
    appLoading.value = true;
    const params = {
      sysClientType: operationForm.value.appType,
    };
    const res = await getSysOptions(params);
    sysOptions.value = res;
    // 检查 operationForm.sysId 是否在新的 sysOptions 中
    if (
      !sysOptions.value.some(
        (option) => option.searchValue === operationForm.value.sysId
      )
    ) {
      operationForm.value.sysId = ""; // 如果不在，重置 sysId
    }
  } catch (e) {
  } finally {
    appLoading.value = false;
  }
}

watch(
  () => operationForm.value.appType,
  (newV) => {
    if (newV) {
      initSysOptions();
    }
  },
  { deep: true }
);

function handleQuery() {
  initTableList();
}

async function updateApplicationStatus(appId) {
  try {
    appLoading.value = true;
    const params = {
      appId,
    };
    await setAppStatus(params);
    ElMessage.success("操作成功");
    await initTableList();
  } catch (e) {
  } finally {
    appLoading.value = false;
  }
}

/* 表格对象删除方法 */
async function deleteApplicationItem(value) {
  await deleteApp({
    appId: value.appId,
  });
  await initTableList();
  ElMessage.success("删除成功");
}

/* 表格对象编辑方法 */
async function handleEdit(value) {
  try {
    loading.value = true;
    const res = await getAppDetail(value.appId);
    operationForm.value = res.data;
    operationDialog.value = true;
    dialogTitle.value = "编辑应用";
  } catch (e) {
  } finally {
    loading.value = false;
  }
}

/* 新增系统配置方法 */
function addApplicationSet() {
  operationDialog.value = true;
  dialogTitle.value = "新增应用";
}

/* 重置表单 */
function resetForm() {
  setTimeout(() => {
    operationForm.value = cloneDeep(operationFormCopy);
  }, 300);
}

async function submitForm() {
  // console.log(operationForm.value, 'sss');
  const valid = await operationFormRef.value.validate();
  if (!valid) return;
  if (operationForm.value.appId) {
    await updateApp(operationForm.value);
  } else {
    await addApp(operationForm.value);
  }
  await initTableList();
  operationDialog.value = false;
  ElMessage.success(operationForm.value.appId ? "编辑成功" : "新增成功");
  resetForm();
}

function cancelForm() {
  operationDialog.value = false;
  resetForm();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 获取一级菜单名称 */
async function getSysName() {
  const { data } = await getFirstMenuList();
  sysNameArr.value = data || [];
}

onMounted(() => {
  initSysOptions();
  getSysName();
});
</script>

<style scoped lang="scss"></style>
