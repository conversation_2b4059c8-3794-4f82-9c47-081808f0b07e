import { parseTime } from "./ruoyi";
import { cloneDeep } from "lodash";
import { getToken } from "@/utils/auth.js";
import { decryptTokenAES, encryptTokenAES } from "@/utils/crypto.js";
import { ElMessage } from "element-plus";

const SYSTEM_SCREEN_WIDTH = 1920;
const SYSTEM_SCREEN_HEIGHT = 1080;

/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == "") return "";
  var date = new Date(cellValue);
  var year = date.getFullYear();
  var month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;
  var day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  var minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  var seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return (
    year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds
  );
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (("" + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length;
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i);
    if (code > 0x7f && code <= 0x7ff) s++;
    else if (code > 0x7ff && code <= 0xffff) s += 2;
    if (code >= 0xdc00 && code <= 0xdfff) i--;
  }
  return s;
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split("?")[1]).replace(/\+/g, " ");
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split("&");
  searchArr.forEach((v) => {
    const index = v.indexOf("=");
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement("div");
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== "object") {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += "" + className;
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone");
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + "";
  const randomNum = parseInt((1 + Math.random()) * 65536) + "";
  return (+(randomNum + timestamp)).toString(32);
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += " " + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    ele.className = ele.className.replace(reg, " ");
  }
}

export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null);
  const list = str.split(",");
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true;
  }
  return expectsLowerCase ? (val) => map[val.toLowerCase()] : (val) => map[val];
}

export const exportDefault = "export default ";

export const beautifierConf = {
  html: {
    indent_size: "2",
    indent_char: " ",
    max_preserve_newlines: "-1",
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: "separate",
    brace_style: "end-expand",
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: "110",
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true,
  },
  js: {
    indent_size: "2",
    indent_char: " ",
    max_preserve_newlines: "-1",
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: "normal",
    brace_style: "end-expand",
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: "110",
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true,
  },
};

// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}

// 下划转驼峰
export function camelCase(str) {
  return str.replace(/_[a-z]/g, (str1) => str1.substr(-1).toUpperCase());
}

export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str);
}

// 计算当前月份
export function getCurrentMonth() {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  return year + "-" + month;
}
/*
 *获取本周的日期时间
 *@params date 时间对象/时间字符串/时间戳等等
 *@params type {String}  值：prev/current/next 上周/本周/下周
 *@params fmt {String} 日期连接符
 */
export function getWeekList(date = new Date(), type = "current", fmt = "-") {
  let arr = [];
  // 格式化日期
  const dateFormat = (date, fmt) => {
    let y = date.getFullYear(); // 年
    let m = date.getMonth() + 1; // 月
    let d = date.getDate(); // 日
    return `${y}${fmt}${m}${fmt}${d}`;
  };
  let currentDate = new Date(date);
  let w = currentDate.getDay(); // 当前星期 0-6
  let y = currentDate.getFullYear(); // 当前年
  let m = currentDate.getMonth() + 1; // 当前月
  let d = currentDate.getDate(); // 当前日期
  if (w === 0) w = 7;
  // 先算出周一是几号 根据type类型计算
  let Monday = 0;
  // 获取周一的年份 月份 日期
  const getMonday = (zf) => {
    if (zf <= 0) {
      // 日期小于0 且当前星期不是0
      if (m - 1 <= 0) {
        // 月份<=0 年份-1
        y = y - 1; // 年份 -1
        m = 12; // 月份 = 12
        let n = d - (w - 1); // 负的星期
        Monday = new Date(y, m, 0).getDate() + n;
      } else {
        m = m - 1; // 月份 -1
        let n = d - (w - 1); // 负的星期
        Monday = new Date(y, m, 0).getDate() + n;
      }
    } else if (zf > 0) {
      // 日期大于0
      Monday = d - (w - 1);
    }
  };
  // 本周一
  switch (type) {
    case "current": // 本周
      break;
    case "prev": // 上一周
      if (d - 7 < 0) {
        if (m - 1 < 1) {
          m = 12;
          y = y - 1;
          d = new Date(y, m, 0).getDate() + (d - 7);
        } else {
          m = m - 1;
          d = new Date(y, m, 0).getDate() + (d - 7);
        }
      } else {
        d = d - 7;
      }
      break;
    case "next": // 下一周
      if (d + 7 > new Date(y, m, 0).getDate()) {
        if (m + 1 > 12) {
          m = 1;
          y = y + 1;
          d = d + 7 - new Date(y - 1, 12, 0).getDate(); // d + 7 大于 12月的天数多少天 就是几号
        } else {
          m = m + 1;
          d = d + 7 - new Date(y, m - 1, 0).getDate();
        }
      } else {
        d = d + 7;
      }
      break;
  }
  getMonday(d - (w - 1)); // 获取周一日期

  // 获取日期
  const getDate = (mon, i) => {
    if (mon + i > new Date(y, m, 0).getDate()) {
      // 大于当月天数
      Monday = -i + 1;
      mon = 1;
      if (m + 1 > 12) {
        y += 1;
        m = 1;
      } else {
        m += 1;
      }
      return dateFormat(new Date(`${y}-${m}-${mon}`), fmt);
    }
    return dateFormat(new Date(`${y}-${m}-${mon + i}`), fmt);
  };
  // currentDate = new Date(y, m, Monday, 0)
  // 往后推7天
  for (let i = 0; i < 7; i++) {
    arr[i] = getDate(Monday, i);
  }
  return arr;
}
/*
 *获取不同类型的日期范围
 *@params type today/week/month/year
 *@params beginTime {String}  返回的开始时间字段
 *@params endTime {String}  返回的结束时间字段
 *@return date {Object} 日期范围
 */
export const getDates = (
  type = "today",
  beginTime = "beginTime",
  endTime = "endTime"
) => {
  let queryParams = {};
  const now = new Date();
  const dayOfMonth = now.getDate();
  const month = now.getMonth();
  const year = now.getFullYear();
  const weekList = getWeekList();
  if (type === "today") {
    queryParams[beginTime] = `${year}-${month + 1}-${dayOfMonth}`;
    queryParams[endTime] = `${year}-${month + 1}-${dayOfMonth}`;
  } else if (type === "week") {
    queryParams[beginTime] = `${weekList[0]}`;
    queryParams[endTime] = `${weekList.at(-1)}`;
  } else if (type === "month") {
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    queryParams[beginTime] =
      `${firstDayOfMonth.getFullYear()}-${firstDayOfMonth.getMonth() + 1}-${firstDayOfMonth.getDate()}`;
    queryParams[endTime] =
      `${lastDayOfMonth.getFullYear()}-${lastDayOfMonth.getMonth() + 1}-${lastDayOfMonth.getDate()}`;
  }
  // 上个月
  else if (type === "prevMonth") {
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const prevMonthEndDate = new Date(today.getFullYear(), today.getMonth(), 0);
    (queryParams[beginTime] =
      `${lastMonth.getFullYear()}-${lastMonth.getMonth() + 1}-${lastMonth.getDate()}`),
      (queryParams[endTime] =
        `${prevMonthEndDate.getFullYear()}-${prevMonthEndDate.getMonth() + 1}-${prevMonthEndDate.getDate()}`);
  } else if (type === "year") {
    queryParams[beginTime] = `${year}-01-01`;
    queryParams[endTime] = `${year}-12-31`;
  }
  return queryParams;
};

/**
 * 检查给定日期范围是否超过指定天数。
 * @param {Array} dateArray - 包含两个日期字符串的数组，格式为 YYYY-MM-DD。第一个元素表示起始日期，第二个元素表示结束日期。
 * @param {number} days - 用于比较的天数阈值。
 * @returns {boolean} - 如果日期范围超过指定天数，返回 true；否则返回 false。
 */
export const checkDateRangeExceedsDays = (dateArray, days) => {
  // 参数检验
  if (
    !Array.isArray(dateArray) ||
    dateArray.length !== 2 ||
    !dateArray.every(
      (item) => typeof item === "string" && /^\d{4}-\d{2}-\d{2}$/.test(item)
    )
  ) {
    throw new Error(
      "dateArray 参数必须是包含两个格式正确（YYYY-MM-DD）的日期字符串的数组。"
    );
  }
  if (typeof days !== "number") {
    throw new Error("days 参数必须是一个数字。");
  }
  const [startDateStr, endDateStr] = dateArray;
  const startDate = new Date(startDateStr);
  const endDate = new Date(endDateStr);
  const timeDiff = endDate.getTime() - startDate.getTime();
  const daysDiff = timeDiff / (1000 * 3600 * 24);
  return daysDiff + 1 > days;
};
/**
 * 填充日期范围
 * @param {Array} dateRange - 包含两个日期字符串的数组，格式为 YYYY-MM-DD。第一个元素表示起始日期，第二个元素表示结束日期。
 * @returns {Array} - 返回包含所有日期的数组。
 */
export function fillDatesInRange(dateRange) {
  try {
    const startDate = new Date(dateRange[0]);
    const endDate = new Date(dateRange[1]);
    const dates = [];
    let currentDate = startDate;
    while (currentDate <= endDate) {
      dates.push(currentDate.toISOString().split("T")[0]);
      currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
    }
    return dates;
  } catch (error) {
    return null;
  }
}

export const changeSingleToObj = (arr) => {
  if (arr && arr.length > 0) {
    let list = arr.map((i) => {
      return {
        label: i,
        name: i,
        date: i,
      };
    });
    return list;
  } else {
    return [];
  }
};
/**
 * 获取开始和结束日期
 * @param {Array} arr - 包含日期字符串的数组，格式为 YYYY-MM-DD。
 * @returns {Object} - 返回包含开始日期和结束日期的对象。
 */
export function findStartAndEndDates(arr, dateField = "schedulingDate") {
  try {
    let minDate = null;
    let maxDate = null;

    for (const item of arr) {
      const currentDate = new Date(item[dateField]);
      if (!minDate || currentDate < minDate) {
        minDate = currentDate;
      }
      if (!maxDate || currentDate > maxDate) {
        maxDate = currentDate;
      }
    }

    return {
      startDate: minDate ? minDate.toISOString().split("T")[0] : null,
      endDate: maxDate ? maxDate.toISOString().split("T")[0] : null,
    };
  } catch (error) {
    console.error("An error occurred:", error);
    return null;
  }
}
// 生成随机字符串
export function randomString(len) {
  len = len || 12;
  var $chars =
    "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678"; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  var maxPos = $chars.length;
  var pwd = "";
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
}
/**
 * 特定格式date
 *
 */
export const formatTimemmss = (timeStr) => {
  if (!timeStr || !timeStr.match(/^\d{2}:\d{2}:\d{2}$/)) {
    return timeStr;
  }
  return timeStr.split(":").slice(0, 2).join(":");
};
/**
 * 获取一段时间范围的所有日期
 * @param {Array} arr - 包含日期字符串的数组，格式为 [YYYY-MM-DD,YYYY-MM-DD]。
 * @returns {Array} - 返回包含arr区间所有日期的数组 并包含结束日期_。
 */
export function getAllDatesInDateRange(arr) {
  try {
    const [startDate, endDate] = arr;
    const dates = [];
    let currentDate = new Date(startDate);
    while (currentDate <= new Date(endDate)) {
      dates.push(currentDate.toISOString().split("T")[0]);
      currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
    }
    dates.push(endDate);
    return dates;
  } catch (error) {
    console.error("An error occurred:", error);
    return null;
  }
}
/**
 * 这个函数用于检查一个对象的非空键的数量。它接受一个对象作为参数，并返回一个布尔值，表示该对象中非空键的数量是否大于 6。
 *
 */
export function checkObject(obj) {
  let nonEmptyKeyCount = 0;
  for (let key in obj) {
    if (
      obj.hasOwnProperty(key) &&
      obj[key] !== null &&
      obj[key] !== undefined &&
      obj[key] !== ""
    ) {
      nonEmptyKeyCount++;
    }
  }
  return nonEmptyKeyCount > 6;
}

// 初始表单深拷贝
export function cloneDeepForm(formListObj) {
  const objList = Object.keys(formListObj).map((key) => {
    return {
      [`${key}Copy`]: cloneDeep(formListObj[key]),
    };
  });
  return Object.assign({}, ...objList);
}

// 生成唯一 ID
export const uuid = () =>
  ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) =>
    (
      c ^
      (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (c / 4)))
    ).toString(16)
  );

/**
 * @description: 图片转base64
 * @param url {*} 图片地址
 * @returns {Promise<*>}
 * */
export async function imgToBase64(url) {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.src = url;
    image.onload = () => {
      const canvas = document.createElement("canvas");
      canvas.width = image.naturalWidth; // 使用 naturalWidth 为了保证图片的清晰度
      canvas.height = image.naturalHeight;
      canvas.style.width = `${canvas.width / window.devicePixelRatio}px`;
      canvas.style.height = `${canvas.height / window.devicePixelRatio}px`;
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        return null;
      }
      ctx.drawImage(image, 0, 0);
      const base64 = canvas.toDataURL("image/png");
      return resolve(base64);
    };
    image.onerror = (err) => {
      return reject(err);
    };
  });
}

/**
 * @description 预加载loading
 * @param {*} loading
 * @param {Function} fun
 * @returns {*}
 * */
export async function onload(loading, ...fun) {
  loading.value = true; // 开始加载
  try {
    // 执行所有加载函数
    return await Promise.all(fun.map((loader) => loader()));
  } catch (error) {
    console.error("加载错误:", error);
  } finally {
    loading.value = false; // 结束加载
  }
}

/**
 * @description 预加载loading, 支持多个loading状态以及默认加载时间，传入对象易于可扩张
 * @param {{}} configuration
 * @return {Promise<void>}
 */
export function useLoading(
  configuration = {
    loading: [],
    delay: 300,
    fun: [],
  }
) {
  return new Promise((resolve, reject) => {
    // 检查 loading 和 fun 的有效性
    const { loading, fun, delay } = configuration;

    if (!Array.isArray(loading) || loading.length === 0) {
      console.error("请传入有效的 loading 对象数组");
      return reject("请传入有效的 loading 对象数组");
    }

    if (loading.some((item) => !item)) {
      console.error("无效的 loading 对象");
      return reject("无效的 loading 对象");
    }

    if (
      !Array.isArray(fun) ||
      fun.length === 0 ||
      fun.some((fn) => typeof fn !== "function")
    ) {
      console.error("无效的加载函数数组");
      return reject("无效的加载函数数组");
    }

    // 开始加载
    loading.forEach((item) => (item.value = true));

    setTimeout(async () => {
      try {
        // 执行所有加载函数
        const results = await Promise.allSettled(fun.map((loader) => loader()));
        resolve(results); // 成功时解析 Promise
      } catch (error) {
        console.error("加载错误:", error);
        reject(error); // 出现错误时拒绝 Promise
      } finally {
        // 结束加载
        loading.forEach((item) => (item.value = false));
      }
    }, delay);
  });
}

/**
 * @description 外部链接跳转(加上token的跳转)
 * @param {string} url
 * */
export function openLink(url) {
  if (!url) {
    return;
  }
  const token = getToken(); // 获取 token
  if (!token) {
    ElMessage.error("未登录，无法跳转");
    return;
  }
  const queryValue = url.indexOf("?") > -1 ? "&" : "?";
  const encryptedToken = encryptTokenAES(token); // 对 token 进行加密
  const decryptToken = decryptTokenAES(encryptedToken);
  const urlWithToken = `${url}${queryValue}token=${encryptedToken}`;
  window.open(urlWithToken, "_blank");
}

/**
 * @description string转number
 * @param {*} str
 * @returns {number}
 * */
export function stringToNumber(str) {
  if (!str) {
    return NaN;
  }
  if (typeof str !== "string") {
    return str;
  }
  // 判断str每个字符是否为数字
  const reg = /^[0-9]+.?[0-9]*$/;
  if (!reg.test(str)) {
    return NaN;
  }
  return Number(str);
}

/**
 * @description 将px转为vw,支持px字符串
 * */
export function pxToVw(px) {
  // 检查输入是否为字符串且以px结尾
  if (typeof px === "string" && px.endsWith("px")) {
    // 提取数字部分
    const num = parseFloat(px);
    if (isNaN(num)) {
      return NaN; // 如果提取的数字是NaN，则返回NaN
    }
    px = num; // 更新px为数字
  }
  return `${(px / SYSTEM_SCREEN_WIDTH) * 100}vw`;
}

/**
 * @description 获取当前时段
 */
export function getMoment() {
  const h = new Date().getHours();
  let greetingMsg = "";
  if (6 <= h && h <= 11) {
    greetingMsg = "早上好";
  } else if (12 <= h && h <= 17) {
    greetingMsg = "下午好";
  } else if (18 <= h || h <= 5) {
    greetingMsg = "晚上好";
  }
  return greetingMsg;
}
