<template>
    <div class="app-container">
        <CommonTable
            :columns="applicationColumns"
            :data="tableData"
            :total="total"
            :loading="loading"
            :page-params="pageParams"
            :show-default-btn="true"
            :show-operation-column="true"
            :show-pagination="true"
            :header-style="{ fontWeight: 'bold' }"
            @confirmEvent="deleteApplicationItem"
            @handleEdit="handleEdit"
        >
            <template #search>
                <el-form
                    :model="searchParams"
                    ref="queryRef"
                    :inline="true"
                    label-width="68px"
                >
                    <el-form-item
                        label="系统名称"
                        prop="userName"
                    >
                        <el-input
                            v-model="searchParams.name"
                            placeholder="请输入系统名称"
                            clearable
                            style="width: 240px"
                        />
                    </el-form-item>
                    <el-form-item
                        label="是否启用"
                        prop="status"
                    >
                        <el-select
                            v-model="searchParams.status"
                            placeholder="请选择是否启用"
                            clearable
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in [
                                    { label: '是', value: '1' },
                                    { label: '否', value: '0' },
                                ]"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button
                            type="primary"
                            icon="Search"
                            @click="handleQuery"
                        >
                            搜索
                        </el-button>
                    </el-form-item>
                </el-form>
            </template>

            <template #iconSlot="{ row }">
                <el-image
                    v-if="row?.icon"
                    :src="baseUrl + row?.icon"
                    :preview-src-list="[baseUrl + row?.icon]"
                    preview-teleported
                    style="width: 40px; height: 40px"
                />
            </template>

            <template #isEnableSlot="{ row }">
                <span>{{ row?.status === '1' ? '是' : '否' }}</span>
            </template>

            <template #button>
                <el-button
                    type="primary"
                    @click="addApplicationSet"
                >
                    新增
                </el-button>
            </template>
        </CommonTable>
        <div>
            <el-dialog
                v-model="operationDialog"
                @closed="resetForm"
                title="新增系统"
                width="500"
            >
                <div class="flex-center">
                    <el-form
                        ref="operationFormRef"
                        :model="operationForm"
                        :rules="operationFormRule"
                        label-width="auto"
                        style="width: 400px"
                    >
                        <el-form-item
                            label="系统名称"
                            prop="name"
                        >
                            <el-input
                                clearable
                                v-model="operationForm.name"
                                placeholder="请输入系统名称"
                            />
                        </el-form-item>
                        <el-form-item
                            label="系统地址"
                            prop="url"
                        >
                            <el-input
                                clearable
                                v-model="operationForm.url"
                                placeholder="请输入系统地址"
                            />
                        </el-form-item>
                        <el-form-item
                            label="系统图标"
                            prop="icon"
                        >
<!--                            <el-input-->
<!--                                v-model="operationForm.icon"-->
<!--                                placeholder="请输入系统图标"-->
<!--                            />-->
                          <ImageUpload
                              :limit="1"
                              v-model="operationForm.icon"
                          ></ImageUpload>
                        </el-form-item>
                        <el-form-item
                            label="是否启用"
                            prop="status"
                        >
                            <el-select
                                clearable
                                v-model="operationForm.status"
                                placeholder="请选择是否启用"
                            >
                                <el-option
                                    v-for="dict in [
                                        { label: '是', value: '1' },
                                        { label: '否', value: '0' },
                                    ]"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <div class="w-full flex justify-end">
                                <el-button @click="cancelForm">取消</el-button>
                                <el-button
                                    type="primary"
                                    @click="submitForm"
                                >
                                    提交
                                </el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script setup>
    import CommonTable from './CommonTable/index.vue';
    import { applicationColumns, operationFormRule } from '@/views/system/application/content.js';
    import { ElMessage } from 'element-plus';
    import { cloneDeepForm } from '@/utils/index.js';
    import { cloneDeep } from 'lodash';
    import ImageUpload from "@/components/ImageUpload/index.vue";
    import {useCommonTable} from "@/hooks/useCommonTable.js";
    import {addSys, deleteSys, getSysList, updateSys} from "@/api/system/application.js";
    import {useDict} from "@/utils/dict.js";
    const baseUrl = import.meta.env.VITE_APP_PORTAL_API;
    const operationFormRef = ref(null);
    const operationDialog = ref(false);
    const operationForm = ref({
      cid: '',
      name: '',
      url: '',
      status: '',
      icon: ''
    });

    const { operationFormCopy } = cloneDeepForm({ operationForm: operationForm.value });
    const { sys_client_type } = useDict(
        "sys_client_type")

    /* 表单搜索条件对象 */
    const searchParams = ref({
      name: '',
      status: '',
    });

    const { tableData, pageParams, loading, total, initTableList } = useCommonTable({
      onLoadData: getSysList,
      showPagination: true,
      searchParams: searchParams,
      isRealTimeListening: false
    })

    function handleQuery() {
        initTableList()
    }

    /* 表格对象删除方法 */
    async function deleteApplicationItem(value) {
        await deleteSys({
          ids: [value.cid],
        })
        await initTableList()
        ElMessage.success('删除成功');
    }

    /* 表格对象编辑方法 */
    function handleEdit(value) {
        console.log(value);
        operationForm.value = value;
        operationDialog.value = true;
    }

    /* 新增系统配置方法 */
    function addApplicationSet() {
        operationDialog.value = true;
    }

    /* 重置表单 */
    function resetForm() {
      setTimeout(() => {
        operationForm.value = cloneDeep(operationFormCopy);
      }, 300);
    }

    async function submitForm() {
      console.log(operationForm.value, 'sss')
        const valid = await operationFormRef.value.validate();
        if (!valid) return;
        if (operationForm.value.cid) {
          await updateSys(operationForm.value);
        } else {
          await addSys(operationForm.value);
        }
        await initTableList()
        operationDialog.value = false;
        ElMessage.success(operationForm.value.cid ? '编辑成功' : '新增成功');
        resetForm();
    }

    function cancelForm() {
        operationDialog.value = false;
        resetForm();
    }

</script>

<style scoped lang="scss"></style>
