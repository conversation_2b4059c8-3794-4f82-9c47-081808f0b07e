import { ElMessage } from 'element-plus'
import { debounce } from 'lodash'
export function useCommonTable(
    options = {
      onLoadData: null,
      onDelData: null,
      showPagination: false,
      searchParams: {},
      listAttributeName: '',
      isRealTimeListening: false,  // 是否实时监听
      // propMap: null, // 映射表 Array<{label: string, value: string}>
      // mapKey: null // 需要转换是Array<key:string>
    },
) {
  const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
  })
  const tableData = ref([])
  const total = ref(0)
  const loading = ref(false)
  const dynamicParams = ref({})

  /**
   * 初始化表格数据列表。
   * 如果提供了onLoadData回调函数，则调用该函数以加载数据。
   */
  async function initTableList() {
    if (typeof options.onLoadData === 'function') {
      try {
        loading.value = true
        // 是否分页展示，如果分页则请求分页数据，否则请求全部数据
        dynamicParams.value = options.showPagination
            ? { ...pageParams.value }
            : {}
        if (options.showPagination) {
          dynamicParams.value = {
            ...options.searchParams.value,
            ...pageParams.value,
          }
        } else {
          dynamicParams.value = { ...options.searchParams.value }
        }

        // 调用onLoadData函数，并在数据加载完成后更新数据
        const res = await options.onLoadData({ ...dynamicParams.value })
        const tableDataObj = options.listAttributeName
            ? res?.[options.listAttributeName]
            : res
        console.log(tableDataObj, 'tableRes')
        // 明确检查返回结果中的records和total字段
        tableData.value =
            tableDataObj?.records ||
            tableDataObj?.data?.records ||
            tableDataObj?.content ||
            tableDataObj?.data ||
            tableDataObj?.rows ||
            tableDataObj?.list ||
            tableDataObj ||
            []
        total.value =
            tableDataObj?.total ||
            tableDataObj?.data?.total ||
            tableData.value.length ||
            0
        loading.value = false
      } catch (error) {
        loading.value = false
        // 处理可能发生的错误
        console.error('Failed to load table data:', error)
      }
    }
  }

  initTableList()

  /**
   * 异步删除表格数据
   * @param {Object} delIds 要删除的行数据的id或多个id组成的字符串，用英文逗号拼接，例 '1,2,3'
   * 此函数首先会检查是否存在一个名为onDelData的回调函数，
   * 如果存在，则调用该函数进行数据删除操作，
   * 并在成功删除后重新初始化表格数据。
   * @returns {无}
   */
  async function delTableData(delIds) {
    if (typeof options.onDelData === 'function') {
      try {
        loading.value = true // 开始显示加载中的状态
        // 调用用户定义的onDelData函数，用于执行实际的数据删除操作
        await options.onDelData(delIds)
        initTableList() // 数据删除成功后，重新初始化表格数据
        ElMessage({
          message: '删除成功！',
          type: 'success',
          plain: true,
        })
        loading.value = false // 隐藏加载中的状态
      } catch (error) {
        loading.value = false // 发生错误时，隐藏加载中的状态
        // 处理数据删除过程中可能发生的错误
        console.error('Failed to delete table data:', error)
      }
    }
  }

  /**
   * 对tableData中的每一项数据进行映射转换，将item中指定的mapKey属性的值，替换为propMap中对应value的label值。
   * 要求options对象中必须同时存在propMap和mapKey属性才能进行转换。
   */
  // function enumerationTransform(){
  //   // 当options中包含propMap和mapKey时，对tableData的每一项执行映射转换
  //   if(options.propMap && options.mapKey && tableData.value.length > 0){
  //     tableData.value.forEach(item => {
  //       // 将当前item的mapKey属性值，替换为propMap中对应value的label
  //       for(let i = 0; i < options.propMap.length; i++){
  //         if(options.propMap[i].find(enumItem => enumItem.value === item[options.mapKey[i]])){
  //           item[options.mapKey[i]] = options.propMap[i].find(enumItem => enumItem.value === item[options.mapKey[i]]).label
  //         }
  //       }
  //     })
  //   }
  // }

  /**
   * 将给定的值转换为与之匹配的枚举标签。
   * @param {any} value - 需要转换的值。
   * @param {Array} options - 包含枚举项的数组，每个枚举项都是一个对象，拥有value和label两个属性。
   * @returns {String} - 如果找到匹配的枚举值，返回对应的枚举标签；否则返回空字符串。
   */
  function enumerationTransform(value, options) {
    // 当给定值非空且选项数组非空时进行处理
    if (value && options.length > 0) {
      // 在选项中查找是否存在匹配的枚举值
      if (options.find((enumItem) => enumItem.value === value)) {
        // 如果找到匹配的枚举值，返回其标签
        return options.find((enumItem) => enumItem.value === value).label
      }
    }
    // 如果没有找到匹配的枚举值，返回空字符串
    return ''
  }

  onActivated(initTableList)

  const debouncedInitTableList = debounce(initTableList, 500)

  function initDynamicParams(newParams) {
    dynamicParams.value = {
      ...pageParams.value,
      params: { ...newParams },
    }
  }

  /**
   * 使用watch监听器来响应options.searchParams的变化，并初始化表格列表。
   * 当options.searchParams对象内部发生深度变化时，将触发initTableList函数。
   *
   * @param {Object} options - 包含searchParams的对象，searchParams是需要被监听的参数。
   * @returns {Object} - 返回一个包含监听器配置的对象。
   */
  const watchParams = options.isRealTimeListening ? [options.searchParams.value, pageParams.value.pageSize] : [pageParams.value.pageSize]
  watch(
      () => watchParams,
      async (newParams) => {
        pageParams.value.pageNum = 1
        initDynamicParams(newParams[0])
        debouncedInitTableList() // 调用初始化表格的函数
      },
      { deep: true },
  )

  watch(
      () => [pageParams.value.pageNum, pageParams.value.pageSize],
      async (newParams) => {
        initDynamicParams(options.searchParams.value)
        debouncedInitTableList()
      },
  )

  // 返回initTableList方法
  return {
    tableData,
    total,
    loading,
    pageParams,
    debouncedInitTableList,
    initTableList,
    delTableData,
    enumerationTransform,
  }
}
