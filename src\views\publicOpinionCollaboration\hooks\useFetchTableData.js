import { debounce } from "lodash";

/**
 * 请求表格数据
 * @param {Function} api 请求接口
 * @param {Object} params 接口参数
 * @param {Function} getDataMap 数据结构转换
 */
export function useFetchTableData(p) {
  const { api, params = {}, getDataMap = () => {}, isPage = true } = p;

  const tableData = ref([]); // 表格数据
  const tableLoading = ref(false); // 表格加载状态
  const pageObj = ref({ pageNum: 1, pageSize: 15, total: 0 }); // 分页数据

  onMounted(() => {
    pageObj.value.pageNum = params.value.pageNum || 1;
    pageObj.value.pageSize = params.value.pageSize || 15;
    getTableData();
  });

  /**
   * 获取数据
   */
  async function getTableData() {
    tableLoading.value = true;

    const newParams = {
      ...unref(params),
      reportSource: params.value.reportSource === "all" ? "" : params.value.reportSource,
      targetGroup: params.value.targetGroup === "all" ? "" : params.value.targetGroup
    };
    if (isPage) {
      newParams.pageNum = pageObj.value.pageNum;
      newParams.pageSize = pageObj.value.pageSize;
    }

    const res = await api.value(newParams);

    if (res.code === 200) {
      tableData.value = getDataMap(
        res?.data?.records || res?.rows || res?.data
      );
      if (isPage) {
        pageObj.value.total = Number(res?.total);
      }
    }
    tableLoading.value = false;
  }

  if (isRef(params)) {
    // 搜索值变化，重新请求数据
    const getTableDataDebounce = debounce(getTableData, 500);
    watch(
      () => params.value,
      () => {
        getTableDataDebounce();
      }
    );
  }

  return {
    tableData,
    tableLoading,
    pageObj,
    getTableData: getTableData,
  };
}
