<template>
  <div class="table-container" :class="[{ noPadding: noPadding }]">
    <div class="p-b-[15px]" :class="[{ noPadding: noPadding }]">
      <slot name="search"> </slot>
      <slot v-if="props.showDefaultBtn" name="button">
        <el-button type="primary" :icon="Edit" @click="handleAdd"
          >新增</el-button
        >
        <el-button type="danger" :icon="Delete" @click="handleBatchDel"
          >批量删除</el-button
        >
      </slot>
    </div>
    <el-table
      class="tableRefWrapper"
      ref="tableRef"
      v-loading="loading"
      :highlight-current-row="highlightCurrentRow"
      :row-key="rowKey"
      :reserve-selection="true"
      :data="data"
      :header-cell-style="calcHeaderStyles"
      :cell-style="calcCellStyle"
      :style="style"
      :max-height="maxHeight"
      :row-class-name="rowClassName"
      :tooltip-options="{ 'show-after': 300 }"
      @row-click="handleRowClick"
      @sortChange="handleSort"
      @selection-change="handleSelectionChange"
      @current-change="handleCurrentChange"
      :stripe="isStripe"
      :key="tableKey"
    >
      <el-table-column
        v-for="col in columns"
        :key="col.prop"
        :prop="col.prop"
        :label="col.label"
        :width="col.width"
        :min-width="col.minWidth"
        :fixed="col.fixed"
        :align="calcAlign(col)"
        :sortable="col.sortable"
        :type="col.type"
        :selectable="col.selectable"
        :show-overflow-tooltip="col.showOverflowTooltip ?? true"
      >
        <template v-if="col.slotLabel" #header>
          <slot :name="col.slotLabel"></slot>
        </template>
        <!-- 自定义动态渲染 -->
        <template v-if="col.slotName" #default="scope">
          <slot
            :name="col.slotName"
            :row="scope.row"
            :index="scope.$index"
          ></slot>
        </template>
        <!-- 默认渲染 -->
        <template
          v-else-if="
            !col.slotName && col.type !== 'index' && col.type !== 'selection'
          "
          #default="scope"
        >
          <span>{{
            scope.row[col.prop] === undefined ||
            scope.row[col.prop] === null ||
            scope.row[col.prop] === ""
              ? "-"
              : scope.row[col.prop]
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="showOperationColumn"
        label="操作"
        :width="operationColumn"
        fixed="right"
        align="left"
      >
        <template #default="scope">
          <slot name="operation" :row="scope.row">
            <el-button link type="primary" @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-popconfirm
              :width="200"
              confirm-button-text="是"
              cancel-button-text="否"
              :icon="InfoFilled"
              icon-color="#626AEF"
              title="你是否删除这条记录吗?"
              @confirm="confirmEvent(scope.row)"
              @cancel="cancelEvent(scope.row)"
            >
              <template #reference>
                <el-button link type="danger" @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-popconfirm>
          </slot>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup name="listTable">
import { Delete, Edit, InfoFilled } from "@element-plus/icons-vue";
import Sortable from "sortablejs"; // 引入拖拽插件

/**
 * 定义组件发出的事件类型。
 *
 * @returns {Object} 返回一个对象，包含组件可能发出的所有事件类型。
 */
const emits = defineEmits([
  "sortChange", // 排序变化事件
  "pageChange", // 页码变化事件
  "handleAdd", // 添加操作事件
  "handleBatchDel", // 批量删除操作事件
  "handleDelete", // 删除操作事件
  "handleEdit", // 编辑操作事件
  "confirmEvent", // 确认操作事件
  "cancelEvent", // 取消操作事件
  "handlePageSizeChange", // 页面大小变化事件
  "handleCurrentPageChange", // 当前页码变化事件
  "update:pageNum", // 更新页码事件
  "update:pageSize", // 更新页面大小事件
  "handleSelectionChange", // 选择项变化事件
  "handleCurrentChange", //选择项变化事件（单选）
  "handleRowClick", // 行点击事件
  "updateColumns", // 更新列事件
]);
const props = defineProps({
  /**
   * 定义数据属性。
   *
   * @description 表格所需的数据。
   * @property {Array} type 数据类型，此处指定为数组。
   * @property {Function} default 默认值回调函数，返回一个空数组作为默认值。
   */
  data: {
    type: Array,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
  // 定义组件属性：当前页码
  // 类型为Number，默认值为1
  initialCurrentPage: {
    type: Number,
    default: 1,
  },
  // 设置初始页面大小的属性
  initialPageSize: {
    type: Number, // 属性类型为数字
    default: 10, // 默认页面大小为10
  },
  loading: {
    type: Boolean,
    default: false,
  },
  //表格渲染的列
  columns: {
    type: Array,
    default: () => [],
  },
  style: {
    type: [Object, String],
    default: () => {},
  },
  // 定义头部样式的配置项
  headerStyle: {
    type: [Object, String], // 可以是对象或字符串类型
    default: () => {}, // 默认值为空函数，返回一个空对象
  },
  cellStyle: {
    type: Object,
    default: () => {},
  },
  // 控制分页显示的配置项
  showPagination: {
    type: Boolean, // 数据类型为布尔值
    default: true, // 默认值为true，即在组件初始化时，分页器是可见的
  },
  // 控制是否显示默认操作列
  showDefaultBtn: {
    type: Boolean,
    default: true,
  },
  /**
   * showOperationColumn 属性配置
   * 用于控制是否显示操作列
   *
   * @type {Boolean} 是否显示操作列
   * @default {true} 默认为 true，即显示操作列
   */
  showOperationColumn: {
    type: Boolean,
    default: true,
  },
  operationColumn: {
    type: Number,
    default: 150,
  },
  noPadding: {
    type: Boolean,
    default: false,
  },
  maxHeight: {
    type: [Number, String],
    default: "-",
  },
  // 高亮当前行
  highlightCurrentRow: {
    type: Boolean,
    default: false,
  },
  rowClassName: {
    type: Function,
    default: () => {},
  },
  isStripe: {
    type: Boolean,
    default: true,
  },
  rowKey: {
    type: [String, Function],
    default: "id",
  },
  isColumnDragSort: {
    type: Boolean,
    default: false,
  },
});

const tableRef = ref(null);

const tableKey = ref(Math.random());

const headerStyles = ref({
  backgroundColor: "#F2F3F6", // 使用淡雅的浅灰色背景，保持简洁
  color: "#3F4F66", // 使用深灰色文字，确保清晰可读
  fontSize: "14px", // 合适的字体大小，兼顾阅读性和空间效率
  fontWeight: "550", // 稍微加重字体，但不过于显眼
  borderBottom: "1px solid #eaeef5", // 细实线下边框，清晰划分表头与内容
});

/**
 * 计算并返回头部样式的对象。这个计算属性会根据传入的props中的headerStyle来决定返回的样式，
 * 如果props中没有提供headerStyle，则返回外部定义的headerStyles的值。
 *
 * @return {Object} 返回一个包含头部样式的对象。
 */
const calcHeaderStyles = computed(() => {
  // 根据props中的headerStyle决定返回值
  return props.headerStyle ? props.headerStyle : headerStyles.value;
});

const calcCellStyle = computed(() => {
  console.log("props.cellStyle", props.cellStyle);
  return {
    color: "#3F4F66",
    "vertical-align": "top",
    padding: "10px 0",
    ...props.cellStyle,
  };
});

// 处理是左对齐还是右对齐
function calcAlign(col) {
  if (["selection"].includes(col.type)) {
    return "left";
  } else if (col.align) {
    return col.align;
  }
  return "center";
}

/**
 * 处理排序操作
 * @param {any} data - 排序数据或信息
 * @emits 'sortChange' - 发出排序变更事件，携带排序数据或信息
 */
function handleSort(data) {
  emits("sortChange", data); // 触发排序变更事件
}

/**
 * 处理行数据点击事件
 * @param {any} data - 行数据
 * @emits 'sortChange' - 表格行被点击触发的事件
 */
function handleRowClick(row, column, e) {
  emits("handleRowClick", row); // 触发行数据点击事件
}

/**
 * 处理添加操作的函数。
 * 该函数没有参数。
 * 没有返回值。
 * 通过触发名为'handleAdd'的自定义事件来响应操作。
 * @emits 'handleAdd' 当调用此函数时，会发出此事件。
 */
function handleAdd() {
  emits("handleAdd"); // 触发'handleAdd'事件
}

/**
 * 处理批量删除操作的函数。
 * 该函数会触发一个名为 'handleBatchDel' 的自定义事件。
 *
 * @emits 'handleBatchDel' 当调用此函数时，会发出此事件。
 */
function handleBatchDel() {
  emits("handleBatchDel"); // 触发 'handleBatchDel' 事件
}

/**
 * 处理编辑操作的函数。
 * 该函数会触发一个名为 'handleEdit' 的事件。
 *
 * @emits 'handleEdit' 当调用此函数时，会发出 'handleEdit' 事件。
 */
function handleEdit() {
  emits("handleEdit"); // 触发 'handleEdit' 事件
}

/**
 * 处理单个删除操作的函数。
 * 该函数会触发一个名为 'handleDelete' 的事件。
 *
 * @emits 'handleDelete' 当调用此函数时，会发出 'handleDelete' 事件。
 */
function handleDelete() {
  emits("handleDelete"); // 触发 'handleDelete' 事件
}

/**
 * 确认事件操作
 * @param {Object} row - 行对象，代表需要操作的数据行
 * @emits 'confirmEvent' - 触发名为'confirmEvent'的自定义事件，将行对象作为参数传递给事件监听器
 */
function confirmEvent(row) {
  emits("confirmEvent", row);
}

/**
 * 取消事件操作
 * @param {Object} row - 代表一行数据的对象，此参数在取消事件时会传递给监听此事件的父组件。
 * @emits 'cancelEvent' - 发出一个名为'cancelEvent'的自定义事件，携带参数row。
 */
function cancelEvent(row) {
  emits("cancelEvent", row); // 触发'cancelEvent'事件，通知父组件取消操作的发生。
}

/**
 * 处理选择列表的变化
 * @param selectedList 选择后的列表
 * @emits 'handleSelectionChange' 触发选择列表变化的事件，携带当前选择的列表作为参数
 */
function handleSelectionChange(selectedList) {
  emits("handleSelectionChange", selectedList);
}
/**
 * 处理选择项的变化
 * @param selectedRow 选择后的列表
 * @emits 'handleCurrentChange' 触发选择变化的事件，携带当前选择的项作为参数
 */
function handleCurrentChange(selectedRow) {
  emits("handleCurrentChange", selectedRow);
}

// 暴露给父组件的方法,处理你选中的行需要回显
function handleSelectRow(selectedRowIdList, key) {
  //回显被选中的数据
  nextTick(() => {
    props.data.forEach((row) => {
      if (selectedRowIdList.includes(row[key])) {
        tableRef.value.toggleRowSelection(row, true);
      }
    });
  });
}

/**
 * 列拖拽
 */
function columnDrop() {
  // console.log("数据", props.columns);
  const wrapperTr = document.querySelector(".el-table__header-wrapper tr");
  Sortable.create(wrapperTr, {
    animation: 180,
    delay: 0,
    // 使用 filter 选项来指定哪些元素不能被拖拽
    filter: function (evt) {
      return evt.target.innerText === "" || evt.target.innerText === "操作";
    },
    onEnd: (evt) => {
      const empty = 0;
      // 跳过显示的列数量，例如开头若用了一个多选框和序号，empty=2
      const oldItem = props.columns[evt.oldIndex - empty];
      props.columns.splice(evt.oldIndex - empty, 1);
      props.columns.splice(evt.newIndex - empty, 0, oldItem);

      reDrawTable();
      // 每一次拖拽后都要重绘一次
    },
  });
}

/**
 * 触发表格重绘
 */
function reDrawTable() {
  tableKey.value = Math.random();
  nextTick(() => {
    columnDrop();
  });
  emits("updateColumns", props.columns);
}

onMounted(() => {
  if (props.isColumnDragSort) {
    //表格列拖拽方法
    columnDrop();
  }
});

defineExpose({
  handleSelectRow,
  tableRef,
});
</script>

<style scoped lang="scss">
.table-container {
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
}

.noPadding {
  padding: 0px;
}

// .isCursorPointer {
:deep(.el-table) {
  .el-table__row:hover {
    cursor: pointer; /* 这里可以更改为其他的光标样式，比如 'crosshair', 'help', 'move', 'not-allowed' 等 */
  }
  .cell {
    line-height: unset;
  }
}
// }
</style>
