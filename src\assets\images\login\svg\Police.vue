<template>
    <svg
        width="26px"
        height="29px"
        viewBox="0 0 26 29"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
    >
        <!-- Generator: Sketch 63 (92445) - https://sketch.com -->
        <title>警员1备份</title>
        <desc>Created with Sketch.</desc>
        <defs>
            <linearGradient
                x1="50%"
                y1="0%"
                x2="50%"
                y2="100%"
                id="linearGradient-1"
            >
                <stop
                    stop-color="#E5F5FF"
                    offset="0%"
                ></stop>
                <stop
                    stop-color="#6FAFFE"
                    offset="100%"
                ></stop>
            </linearGradient>
            <path
                d="M18,19.7922691 C17.4267242,19.9480282 13.5208705,20.6782435 9.60061988,20.75 L8.39938015,20.75 C4.47932949,20.6782436 0.573375786,19.9480282 0,19.7922691 C0.0181960614,19.5687008 0.0384916562,19.3389587 0.0607868427,19.1031442 C0.208654814,17.5629615 0.44780298,15.8075095 0.842717417,14.1606537 C0.954193265,13.5742548 0.93589722,13.4142451 1.37360238,13.0634583 C1.71392864,12.7907029 3.89585588,12.7312938 4.96952328,12.7189464 C5.11949079,12.7169223 5.26935831,12.712874 5.41922585,12.7067003 C5.69676571,12.6943529 5.97430559,12.6759331 6.25184543,12.6493154 C6.21731517,12.8744953 6.19163383,13.1009748 6.17486212,13.3282183 C6.16594398,13.4681807 6.1612095,13.608384 6.1606652,13.7486362 C6.1606652,13.9434615 6.17076301,14.1442581 6.19315816,14.3453582 C6.22145204,14.5811728 6.26814192,14.8191127 6.34102611,15.0447052 C6.67935281,16.1172059 7.22023561,17.1795858 7.95357674,17.8132488 L8.33849332,15.2435788 C8.36257066,15.0833466 8.49851796,14.964853 8.65862396,14.9645485 L9.33527737,14.9645485 C9.49538336,14.964853 9.63133065,15.0833466 9.65540799,15.2435788 L10.0403246,17.8132488 C10.7736657,17.1794846 11.3146485,16.1150805 11.6528752,15.0447052 C11.7258594,14.8191127 11.7724493,14.5831969 11.8008431,14.3453582 C11.8251379,14.1443593 11.8331361,13.9434615 11.8331361,13.7486362 C11.8331361,13.6050221 11.8270375,13.4634322 11.8190392,13.3282183 C11.8042514,13.1007891 11.77856,12.8742212 11.7420559,12.6493154 C12.0193958,12.6760343 12.2969356,12.6964783 12.5744755,12.7067003 C12.724443,12.7129752 12.8744105,12.7170235 13.024378,12.7189464 C14.0978455,12.7312938 16.2818723,12.7928282 16.6203989,13.0634583 C17.0578041,13.4142451 17.0376085,13.5742549 17.1510839,14.1606537 C17.5461983,15.8075095 17.7852465,17.5629615 17.9331145,19.1031442 C17.9615083,19.3390599 17.9818039,19.5687007 18,19.7922691 L18,19.7922691 Z M13.3365104,4.20209468 C13.536967,4.12406335 14.7748988,3.59909769 14.7951944,2.49785395 C14.8194891,1.29813486 11.86163,-0.340421906 9.07203439,-0.2460961 C9.04773965,-0.2460961 9.02344492,-0.244071937 9.00114975,-0.244071937 C8.97675503,-0.2460961 8.95246029,-0.2460961 8.93026511,-0.2460961 C6.14046956,-0.340523119 3.18261045,1.29803364 3.20700515,2.49987811 C3.22930032,3.59909769 4.46503258,4.12618872 4.66558913,4.20411884 C4.68178562,4.21039374 4.68988387,4.22668821 4.68588473,4.24105976 C4.62109876,4.50754029 4.65749089,5.32378244 4.65749089,5.32378244 C4.65749089,5.32378244 4.72237683,5.46324701 4.84585006,5.63145462 C4.8500199,5.63821663 4.85217232,5.64605361 4.85204872,5.65402399 C4.85204872,7.41979917 5.30984953,8.66668117 5.9197174,9.54233239 C6.28633796,10.0693222 6.70984621,10.4610982 7.1210571,10.7542976 C8.02876042,11.3962597 8.88767433,11.5499946 8.99705065,11.5623419 L9.00514889,11.5623419 C9.11452519,11.5499946 9.97353908,11.3961585 10.8811424,10.7542976 C11.3485669,10.4245562 11.7552123,10.0143483 12.0825821,9.54233239 C12.6944495,8.66657995 13.1502508,7.41979917 13.1502508,5.65402399 C13.1502508,5.64582615 13.1522504,5.6376283 13.1564494,5.63145462 C13.2799227,5.46334822 13.3447087,5.32378244 13.3447087,5.32378244 C13.3447087,5.32378244 13.3813007,4.50754029 13.3164148,4.24105976 C13.3118914,4.22473665 13.3206941,4.20766827 13.3365104,4.20209468 Z M7.50587374,2.60645009 C7.7061217,2.5912456 7.90713211,2.5891834 8.10764334,2.60027641 C8.16843017,2.60442592 8.21502009,2.55513767 8.21502009,2.49360322 L8.21502009,2.49147784 C8.21502009,2.05678969 8.55334679,1.69375674 9.00114975,1.62392327 C9.44885275,1.69365554 9.78717945,2.05668847 9.78717945,2.49147784 L9.78717945,2.49360322 C9.78717945,2.55513764 9.83376935,2.60230057 9.89445621,2.60027641 C10.0949318,2.58907984 10.2959156,2.59114212 10.4961258,2.60645009 C10.5307183,2.60847425 10.5387166,2.65563715 10.5064236,2.67000868 C9.88645795,2.9387158 9.59872029,3.13354112 9.58042424,3.14578726 C9.58042424,3.14578726 9.57842467,3.14578726 9.57842467,3.14791264 C9.40016329,3.29759921 9.18740939,3.35295996 9.00114975,3.36733149 C8.81469016,3.35295996 8.60203623,3.29972457 8.42377484,3.14791264 C8.42377484,3.14791264 8.42167529,3.14791264 8.42167531,3.14578726 C8.40347925,3.13354109 8.11574159,2.93871578 7.49587589,2.67000868 C7.46338292,2.65573837 7.47148117,2.60847425 7.50587374,2.60645009 L7.50587374,2.60645009 Z M12.5665772,4.34348219 L5.4355223,4.34348219 L5.4355223,3.68735103 L12.5666772,3.68735103 L12.5666772,4.34348219 L12.5665772,4.34348219 Z"
                id="path-2"
            ></path>
            <filter
                x="-33.3%"
                y="-28.6%"
                width="166.7%"
                height="157.1%"
                filterUnits="objectBoundingBox"
                id="filter-3"
            >
                <feOffset
                    dx="0"
                    dy="0"
                    in="SourceAlpha"
                    result="shadowOffsetOuter1"
                ></feOffset>
                <feGaussianBlur
                    stdDeviation="2"
                    in="shadowOffsetOuter1"
                    result="shadowBlurOuter1"
                ></feGaussianBlur>
                <feColorMatrix
                    values="0 0 0 0 0.20226753   0 0 0 0 0.564873198   0 0 0 0 1  0 0 0 1 0"
                    type="matrix"
                    in="shadowBlurOuter1"
                ></feColorMatrix>
            </filter>
        </defs>
        <g
            id="统一门户（内网）"
            stroke="none"
            stroke-width="1"
            fill="none"
            fill-rule="evenodd"
        >
            <g
                id="首页"
                transform="translate(-43.000000, -147.000000)"
                fill-rule="nonzero"
            >
                <g
                    id="编组-20"
                    transform="translate(31.000000, 115.000000)"
                >
                    <g id="编组-15">
                        <g
                            id="警员1备份"
                            transform="translate(16.000000, 36.000000)"
                        >
                            <g id="形状">
                                <use
                                    fill="black"
                                    fill-opacity="1"
                                    filter="url(#filter-3)"
                                    xlink:href="#path-2"
                                ></use>
                                <use
                                    fill="url(#linearGradient-1)"
                                    xlink:href="#path-2"
                                ></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script setup></script>

<style scoped lang="less"></style>
