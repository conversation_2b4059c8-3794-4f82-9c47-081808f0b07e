import portalRequest from "@/utils/portalRequest.js";

// 查询微信群列表
export function getWechatGroupInfo() {
  return portalRequest({
    url: "/business/groupMember/list/group/",
    method: "get",
  });
}

// 查询工作号列表
export function getWorkAccountInfo() {
  return portalRequest({
    url: "/business/groupMember/list/work/",
    method: "get",
  });
}

// 查询报送账号列表
export function getAccountList(params) {
  return portalRequest({
    url: "/business/groupMember/list/groupMember/",
    method: "get",
    params,
  });
}

// 查询报送账号列表
export function bindWechatDept(data) {
  return portalRequest({
    url: "/business/groupMember/bindWechatDept/",
    method: "post",
    data,
  });
}
