// 默认配置
export const defaultOptions = {
    unitToConvert: "px",       // 需要转换的单位，默认为"px"
    designViewportWidth: 1920,   // 设计稿的视口宽度
    precision: 5,               // 单位转换后保留的小数位数
    targetViewportUnit: "vw",   // 目标视口单位
    minValueToConvert: 1        // 最小的转换值，只有大于1的值会被转换
};

// 保留小数位数
const roundToPrecision = (value, precision) => {
    const factor = Math.pow(10, precision);
    return Math.round(value * factor) / factor;
};

// 创建 px 转 vw 的替换逻辑
const createPxToViewportConverter = (viewportWidth, minValue, precision, viewportUnit) => {
    return (match, pixelValue) => {
        const pixels = parseFloat(pixelValue);
        if (isNaN(pixels) || pixels <= minValue) return match;
        return roundToPrecision((pixels / viewportWidth) * 100, precision) + (viewportUnit ? viewportUnit : '');
    };
};

// 将 px 转换为 vw 的函数
export const convertPxToViewportWithUnit = (cssCode, userOptions = {}) => {
    const options = { ...defaultOptions, ...userOptions };
    const pxRegex = new RegExp(`(\\d*\\.?\\d+)${options.unitToConvert}`, 'g'); // 支持浮点数
    console.log(cssCode)
    return cssCode?.replace(pxRegex, createPxToViewportConverter(
        options.designViewportWidth,
        options.minValueToConvert,
        options.precision,
        options.targetViewportUnit
    ));
};

export const convertPxToViewport = (cssCode, userOptions = {}) => {
    const options = { ...defaultOptions, ...userOptions };
    const pxRegex = new RegExp(`(\\d*\\.?\\d+)${options.unitToConvert}`, 'g'); // 支持浮点数
    return Number(cssCode?.replace(pxRegex, createPxToViewportConverter(
        options.designViewportWidth,
        options.minValueToConvert,
        options.precision
    )));
}
