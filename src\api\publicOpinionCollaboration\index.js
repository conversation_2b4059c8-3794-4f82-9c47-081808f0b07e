import request from "@/utils/request";
import { HANDLE_RESULT_OPTIONS } from "@/views/publicOpinionCollaboration/config/index.js";

// 舆情协同-'全部'列表
export function getPoAllList(params) {
  return request({
    url: "/business/opinionReport/list/rumor",
    method: "get",
    params,
  });
}

// 舆情协同-任务数量
export function getMineCount() {
  return request({
    url: "/business/opinionReport/task/untreated/count",
    method: "get",
  });
}

// 舆情协同-我的任务
export function getMineList(query) {
  return request({
    url: "/business/opinionReport/list/task",
    method: "get",
    params: query,
  });
}

// 舆情流程列表
export function getProcessInfo(query) {
  return request({
    url: "/process/coord/list",
    method: "get",
    params: query,
  });
}
// 报送WA处理
export function handleSendWA(data) {
  return request({
    url: "/process/coord/create",
    method: "post",
    data,
  });
}

// 转阅
export function transmitMessage(data) {
  // return request({
  //   url: `/teacher/auth/mng/finish/${params.id}`,
  //   method: "post",
  //   data,
  // });
  return {
    code: 200,
  };
}

// 舆情处置
export function toHandleOpinion(data) {
  return request({
    url: "/process/coord/create",
    method: "post",
    data,
  });
}

// 获取处置措施
export function getHandleResultOptions() {
  // return request({
  //   url: "/process/command-config/list-opts",
  //   method: "get",
  // });
  return {
    code: 200,
    data: HANDLE_RESULT_OPTIONS,
  };
}

// 获取处置措施
export function getHandleMeasuresOptions() {
  return request({
    url: "/process/command-config/list-opts",
    method: "get",
  });
}

// 获取舆情流传-类型数据
export function getCommandCategoryOptions() {
  return request({
    url: "/process/command-config/list-categories",
    method: "get",
  });
}
