<template>
  <div class="publicOpinionTable-container" ref="tableContainerRef">
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :show-default-btn="false"
      :show-operation-column="showOperationColumn"
      :operation-column="operationColumnWidth"
      no-padding
      :max-height="tableHeightF"
      :highlight-current-row="false"
      :row-key="rowKey"
      :header-style="headerCellStyle"
      :cell-style="cellStyle()"
      :isStripe="false"
    >
      <template #isDelete="{ row }">
        <span>{{ getIsDelete(row.isDelete) }}</span>
      </template>
      <template #operation="{ row }">
        <slot name="operation" :row="row"> </slot>
      </template>
    </CommonTable>

    <!-- <div class="flex justify-end items-center">
      <pagination
        v-model:page="pageObj.pageNum"
        v-model:limit="pageObj.pageSize"
        :total="pageObj.total"
        @pagination="$emit('refreshData')"
      />
    </div> -->
  </div>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
// import { useFetchTableData } from "@/hooks/useFetchTableData";
import { useTableHeight } from "@/hooks/useTableHeight";
import { pxToVw } from "@/utils/index.js";
import { IS_DELETE } from "../config/constant";

const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  // 表格列配置
  tableColumns: {
    type: Array,
    default: () => [],
  },
  // 操作栏宽度
  operationColumnWidth: {
    type: Number,
    default: 220,
  },
  // 表格最大高度
  tableMaxHeight: {
    type: Number,
    default: 0,
  },
  rowKey: {
    type: [String, Function],
    default: "id",
  },
  showOperationColumn: {
    type: Boolean,
    default: false,
  },
});

const { proxy } = getCurrentInstance();

const tableContainerRef = ref();
const { tableHeight, getTableHeight } = useTableHeight(tableContainerRef, 0);
const tableHeightF = computed(() =>
  props.tableMaxHeight ? props.tableMaxHeight : tableHeight.value
); // 兼容固定高度和自适应高度

const getIsDelete = (value) => {
  console.log("value", value);
  console.log("IS_DELETE", IS_DELETE);
  console.log(
    "IS_DELETE.find((item) => item.value == value)?.label",
    IS_DELETE.find((item) => item.value == value)?.label
  );
  return IS_DELETE.find((item) => item.value == value)?.label;
};

const headerCellStyle = ({ columnIndex }) => {
  return {
    background: "#063858",
    fontFamily: "PingFangSC, PingFang SC",
    fontSize: pxToVw(15),
    fontweight: "400",
    border: "none !important",
    color: "#fff",
  };
};

const cellStyle = () => {
  return {
    background: "transparent",
    fontFamily: "PingFangSC, PingFang SC",
    fontSize: pxToVw(15),
    color: "#fff",
    border: "none !important",
  };
};

// defineExpose({
//   tableData,
//   getTableData,
// });
</script>

<style lang="scss" scoped>
.publicOpinionTable-container {
  width: 100%;
  height: 100%;
}

// ... existing code ...
.publicOpinionTable-container {
  width: 100%;
  height: 100%;
}

:deep(.el-table) {
  background: transparent !important;

  tr {
    background: transparent !important;
  }
}

:deep(.el-table__body tr:hover > td) {
  background-color: transparent !important;
  // background-image: url(~@/assets/images/homePage/table-row-selected.png) !important;
  // background-repeat: no-repeat !important;
  // background-size: cover !important;
}

/* 去掉表格整体最下面的边框 */
:deep(.el-table) ::before {
  width: 0;
  height: 0;
}
:deep(.el-table__header-wrapper) {
  background: transparent !important;
}
:deep(.el-table__body) {
  background: transparent !important;
}
</style>
