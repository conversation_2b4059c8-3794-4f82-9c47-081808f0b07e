/** 舆情协同-舆情列表表格列 */
export const publicOpinionInfoColumns = [
  {
    prop: "reportNumber",
    label: "舆情编号",
    width: "110px",
    align: "left",
    slotName: "poId",
  },
  {
    prop: "createTime",
    label: "报送时间",
    width: "100px",
    align: "left",
    slotName: "createTime",
  },
  {
    prop: "platformType",
    label: "发布平台",
    width: "100px",
    align: "left",
  },
  {
    prop: "poLink",
    label: "贴文链接",
    width: "220px",
    align: "left",
    slotName: "poLink",
  },
  {
    prop: "netizenNickname",
    label: "网民昵称",
    width: "140px",
    slotName: "netizenNickname",
    align: "left",
  },
  {
    prop: "poContent",
    label: "舆情内容",
    width: "230px",
    slotName: "poContent",
    align: "left",
  },
  {
    prop: "poImg",
    label: "图片",
    slotName: "poImg",
    width: "160px",
    align: "left",
  },
  {
    prop: "workUnit",
    label: "上报单位",
    align: "left",
    width: "150px",
    slotName: "workUnit",
  },
  {
    prop: "isFirst",
    label: "是否首报",
    width: "80px",
    slotName: "isFirst",
  },
  {
    prop: "poName",
    label: "舆情标题",
    width: "150px",
    align: "left",
    slotName: "poName",
  },
  {
    prop: "poEvent",
    label: "舆情事件",
    align: "left",
    width: "270px",
    slotName: "poEvent",
    showOverflowTooltip: false,
  },
  {
    prop: "mediaType",
    label: "媒体类型",
    width: "140px",
    align: "left",
  },
  {
    prop: "reportSource",
    label: "来源",
    width: "140px",
    slotName: "reportSource",
    align: "left",
  },
  {
    prop: "wechatNickname",
    label: "微信报送昵称",
    align: "left",
    width: "140px",
  },
  {
    prop: "netizenAccount",
    label: "网民账号",
    align: "left",
    width: "140px",
  },
  {
    prop: "publicTime",
    label: "发布时间",
    width: "100px",
    align: "left",
    slotName: "publicTime",
  },
  {
    prop: "articleStatus",
    label: "贴文状态",
    width: "100px",
    slotName: "articleStatus",
  },
  {
    prop: "targetGroup",
    label: "目标群",
    width: "150px",
    align: "left",
  },
  {
    prop: "deadlineTime",
    label: "截止时间",
    width: "100px",
    align: "left",
    slotName: "deadlineTime",
  },
  {
    prop: "handleUnit",
    label: "处理单位",
    width: "120px",
    align: "left",
  },
  {
    prop: "handlePerson",
    label: "处理人",
    width: "120px",
  },
  {
    prop: "handleStatus",
    label: "处置状态",
    slotName: "handleStatus",
  },
];
