<template>
  <div class="summary-of-events w-full h-full flex flex-col">
    <item-title class="w-full" :title="title" />
    <div
      class="content"
      style="
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        flex: 1;
      "
    >
      <div class="sum-card">
        <div class="sum-card-title">发现数</div>
        <div class="sum-card-number">
          {{ summaryOfEventsData?.discoveryNum || 0 }}
        </div>
      </div>
      <div class="sum-card">
        <div class="sum-card-title">敏感数</div>
        <div class="sum-card-number" style="color: #ffb769">
          {{ summaryOfEventsData?.sensitiveNum || 0 }}
        </div>
      </div>
      <div class="sum-card">
        <div class="sum-card-title">处置数</div>
        <div class="sum-card-number">
          {{ summaryOfEventsData?.dealNum || 0 }}
        </div>
      </div>
      <div class="sum-card">
        <div class="sum-card-title">谣言数</div>
        <div class="sum-card-number">
          {{ summaryOfEventsData?.rumorNum || 0 }}
        </div>
      </div>
      <div class="sum-card">
        <div class="sum-card-title">自媒体贴文数</div>
        <div class="sum-card-number">
          {{ summaryOfEventsData?.selfMediaNum || 0 }}
        </div>
      </div>
      <div class="sum-card">
        <div class="sum-card-title">媒体报导展示</div>
        <div class="sum-card-number">
          {{ summaryOfEventsData?.newsNum || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ItemTitle from "@/views/eventManage/screen/component/ItemTitle.vue";

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
  summaryOfEventsData: {
    type: Object,
    default: () => {},
  },
});

const summaryOfEventsData = computed(() => props.summaryOfEventsData);
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.content {
  height: px2vw(300);
  padding-top: px2vw(23);
}
.sum-card {
  width: 33%;
  height: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: PangMenZhengDao, sans-serif;
  .sum-card-title {
    font-size: px2vw(17);
    margin-bottom: px2vw(8);
    color: #fff;
    position: relative;
    &:before {
      content: "";
      width: px2vw(8);
      height: px2vw(10);
      background: url(@/assets/screen/platform-active.svg) no-repeat;
      background-size: 100% 100%;
      transform: rotate(90deg) translateX(-55%);
      position: absolute;
      left: px2vw(-12);
      top: 50%;
      z-index: 10;
    }
    &:after {
      content: "";
      width: px2vw(8);
      height: px2vw(10);
      background: url(@/assets/screen/platform-active.svg) no-repeat;
      background-size: 100% 100%;
      transform: rotate(-90deg) translateX(55%);
      position: absolute;
      right: px2vw(-12);
      top: 50%;
      z-index: 10;
    }
  }
  .sum-card-number {
    margin-bottom: px2vw(23);
    font-size: px2vw(29);
    color: #02e8ff;
    text-shadow: 0 0 9px rgba(87, 242, 255, 0.4);
  }
}
</style>
