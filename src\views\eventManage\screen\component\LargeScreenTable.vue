<template>
  <div class="h-full flex flex-col">
    <!-- 表格 grid布局 -->
    <div class="h-full">
      <!-- 大屏表格表头 1行，根据传入的column计算列数 -->
      <div
        class="grid-header-container mb-[0.3vw]"
        :style="{ background: headerBackground }"
      >
        <div
          class="grid-items-container"
          :style="{
            gridTemplateColumns: gridTemplateColumns,
          }"
        >
          <!-- 渲染表头数据，支持插槽：slotLabel -->
          <div
            v-for="column in columns"
            :key="column.prop"
            class="flex justify-start items-center pl-[1vw] w-full h-full"
          >
            <template v-if="column?.slotLabel">
              <slot :name="column.slotLabel"></slot>
            </template>
            <span
              v-else
              :style="{ fontSize: headerFontSize, color: headerColor }"
              >{{ column.label }}</span
            >
          </div>
        </div>
      </div>
      <div class="h-[calc(100%-4vw)] overflow-hidden">
        <component
          :is="useSeamlessScroll ? 'vue3-seamless-scroll' : 'div'"
          v-bind="useSeamlessScroll ? seamlessScrollProps : {}"
          :class="{
            'scroll-seamless':
              useSeamlessScroll && !isLessThanMaxRowHeightLimit,
            'scroll-seamless-min':
              useSeamlessScroll && isLessThanMaxRowHeightLimit,
          }"
          class="h-full"
        >
          <!-- 表格内容部分 -->
          <div
            ref="gridValueContainerRef"
            class="grid-value-container h-full overflow-hidden gap-[0.3vw]"
            @wheel="scrollChange"
          >
            <!-- 根据传入的column计算列数，可自定义行高：rowHeight -->
            <div
              v-for="(row, rowIndex) in tableData"
              :key="rowIndex"
              class="grid-items-container"
              :class="` ${intervalOpacity ? (rowIndex % 2 === 0 ? 'bg-[#283d4f4d]' : 'bg-[#283d4f80]') : 'bg-[#283d4f80]'}`"
              :style="{
                gridTemplateColumns: gridTemplateColumns,
                height: gridTemplateRowHeight,
                background: intervalOpacity
                  ? rowIndex % 2 === 0
                    ? oddRowBackground
                    : eveRowBackground
                  : defaultRowBackground,
              }"
            >
              <!-- 渲染表格数据，支持插槽：slotName，每列字体大小：column.fontSize -->
              <div
                v-for="column in columns"
                :key="column.prop"
                class="text-[#fff] flex justify-start items-center pl-[1vw]"
              >
                <template v-if="column.slotName">
                  <slot
                    :name="column.slotName"
                    :row="row"
                    :index="rowIndex"
                  ></slot>
                </template>
                <div v-else class="flex items-center">
                  <span
                    :style="{
                      fontSize: column.fontSize,
                      color: column.color || contentColor,
                    }"
                    >{{ row[column.prop] }}</span
                  >
                  <span
                    class="text-[#5E7FA2]"
                    v-if="column?.unit"
                    :style="{ ...column?.unitStyle, fontSize: column.fontSize }"
                    >{{ column?.unit }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </component>
      </div>
    </div>
  </div>
</template>

<script setup>
import { handleWheel } from "@/utils/scroll.js";
import { pxToVw, stringToNumber } from "@/utils/index.js";
import { SYSTEM_SCREEN_WIDTH } from "@/constant/index.js";

const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
    required: true,
  }, // 列的定义，必填，默认值为空数组
  tableData: {
    type: Array,
    default: () => [],
    required: true,
  }, // 表格数据的定义，必填，默认值为空数组
  defaultRowHeight: {
    type: Number,
    default: () => null,
  }, // 默认行高的定义，默认值为null，在maxRowHeightLimit不存在并且rowHeight也不存在时或者表格数据超过maxRowHeightLimit时生效
  rowHeight: {
    type: Number,
    default: () => null,
  }, // 行高的定义，默认值为null，在表格数据不超过maxRowHeightLimit或者maxRowHeightLimit为null生效
  columnsWidth: {
    type: String,
    default: () => "100%",
  }, // 列宽的定义，默认值为100%
  contentColor: {
    type: String,
    default: () => "#fff",
  }, // 内容字体颜色的定义，默认值为#fff
  headerFontSize: {
    type: Number,
    default: () => 16,
  }, // 表头字体大小的定义，默认值为16
  headerBackground: {
    type: String,
    default: () => "",
  }, // 表头背景颜色的定义，默认值为空字符串
  headerColor: {
    type: String,
    default: () => "#fff",
  }, // 表头字体颜色的定义，默认值为#fff
  maxRowHeightLimit: {
    type: Number,
    default: () => null,
  }, // 表示数据行数的最大限制。当数据行数超过此值时，将根据该属性调整行高以保持布局整洁
  useSeamlessScroll: {
    type: Boolean,
    default: false,
  }, // 是否使用滚动的定义，默认值为false
  intervalOpacity: {
    type: Boolean,
    default: false,
  }, // 是否使用斑马纹
  oddRowBackground: {
    type: String,
    default: () => "transparent",
  }, // 单数行背景颜色的定义
  eveRowBackground: {
    type: String,
    default: () => "transparent",
  }, // 双数行背景颜色的定义
  defaultRowBackground: {
    type: String,
    default: () => "transparent",
  },
});

// 从props结构出需要的值并且使其变为响应式
const { tableData } = toRefs(props);

// 表格内容区域的ref对象
const gridValueContainerRef = ref(null);

// 对传入的列属性进行处理
const columns = computed(() => {
  return props.columns.map((column, index) => {
    const fontSize = column.fontSize || 15;
    const screenFontSize = pxToVw(fontSize);
    return {
      ...column,
      fontSize: screenFontSize,
    };
  });
});

// vue3-seamless-scroll组件需要的属性
const seamlessScrollProps = computed(() => {
  return {
    list: tableData.value,
    hover: true,
    step: 0.3,
    wheel: true,
    copyNum: 0,
  };
});

// 判断maxRowHeightLimit和tableData的大小
const isLessThanMaxRowHeightLimit = computed(() => {
  return tableData.value.length > props.maxRowHeightLimit;
});

// 表头字体大小处理
const headerFontSize = computed(() => {
  return pxToVw(props.headerFontSize || 16);
});

// 根据列中宽度，计算每列宽度
const gridTemplateColumns = computed(() => {
  try {
    return columns.value
      .map((column) => {
        if (column.width) {
          const widthValue = stringToNumber(column.width);
          return isNaN(widthValue) ? pxToVw(column.width) : pxToVw(widthValue);
        }
        return "1fr";
      })
      .join(" ");
  } catch (error) {
    console.error("计算网格列模板时发生错误:", error);
    return "1fr";
  }
});

// 表格内容行高的计算处理
const gridTemplateRowHeight = computed(() => {
  try {
    const { rowHeight, defaultRowHeight, maxRowHeightLimit, tableData } = props;
    const calculatedHeight = rowHeight || defaultRowHeight; // 如果没有提供行高，则使用默认值

    // 获取数据长度
    const dataLength = tableData.length;

    // 检查是否有最大行高限制
    if (maxRowHeightLimit) {
      // 如果传入的数据长度不超过 maxRowHeightLimit
      if (dataLength <= maxRowHeightLimit) {
        return rowHeight ? pxToVw(rowHeight) : "100%";
      } else {
        // 如果超出最大行高限制，则优先返回 defaultRowHeight 或 100%
        return defaultRowHeight ? pxToVw(defaultRowHeight) : "100%";
      }
    } else {
      // 如果没有最大行高限制，返回计算的行高
      return calculatedHeight ? pxToVw(calculatedHeight) : "100%";
    }
  } catch (error) {
    console.error("计算网格行高时发生错误:", error);
    return "100%"; // 返回默认值以防出错
  }
});

// 自定义滚动条事件
function scrollChange(event) {
  handleWheel(event, gridValueContainerRef, 20);
}

// 自动滚动
onMounted(() => {
  /*const container = gridValueContainerRef.value;
  if (container) {
    let scrollInterval = setInterval(() => {
      if (container.scrollHeight !== container.scrollTop + container.clientHeight) {
        container.scrollTop += 1;  // 每次滚动1px
      } else {
        container.scrollTop = 0;  // 滚动到底部时重置回顶部
      }
    }, 50);  // 每50毫秒滚动1px

    // 清理定时器
    onBeforeUnmount(() => {
      clearInterval(scrollInterval);
    });
  }*/
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.grid-header-container {
  height: px2vw(40);
  width: 100%;
  display: grid;
  grid-template-rows: 100%;
}

.grid-value-container {
  width: 100%;
  display: grid;
}

.grid-items-container {
  display: grid;
}

.grid-column-box {
  font-size: px2vw(16);
  width: 100%;
  height: 100%;
  color: #fff;
  font-weight: bold;
  display: grid;
  justify-items: center;
}

.grid-value-box {
  font-size: px2vw(18);
  color: #fff;
}

.header-title {
  font-family: YouSheBiaoTiHei, serif;
  letter-spacing: px2vw(2);
  text-shadow: 0 px2vw(2) px2vw(20) #2971fc;
  text-align: left;
  font-style: normal;
  font-size: px2vw(24);
  color: #ffffff;
}

.scroll-seamless {
  :deep(> div) {
    height: 100% !important;
    & > div {
      height: 100% !important;
    }
  }
}

.scroll-seamless-min {
  :deep(> div) {
    & > div {
      height: 100% !important;
    }
  }
}
</style>
