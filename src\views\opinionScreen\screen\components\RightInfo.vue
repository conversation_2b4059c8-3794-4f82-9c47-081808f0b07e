<template>
  <div class="rightInfo-container">
    <div
      class="screen-title event-title"
      :class="{ 'title-opacity': titleOpacity }"
    >
      正在处置事件
    </div>
    <el-carousel class="event-wrapper" arrow="never">
      <el-carousel-item v-for="item in inList" :key="item.eventId">
        <div class="simple-info cursor-pointer" @click="goToEventScreen(item)">
          <div class="flex items-center">
            <el-tooltip
              :content="item.eventDetail?.name"
              placement="top"
              :show-after="300"
            >
              <p class="ellipsis simple-info-title">
                {{ item.eventDetail?.content }}
              </p>
            </el-tooltip>
            <div class="po-total">
              当前舆情数量：{{ item.eventDetail?.currentPoNum }}
            </div>
          </div>
          <p class="create-time">
            创建时间: {{ parseTime(item.eventDetail?.publishTime) }}
          </p>
        </div>
        <div class="po-type-wrapper">
          <div
            v-for="(item, index) in item.eventSum"
            :key="index"
            class="type-item"
            :class="{ 'has-border': index !== 2 && index !== 4 }"
          >
            <div class="type-item-title">
              {{ eventNumMap[item.key] }}
            </div>
            <div class="type-item-num cursor-pointer">
              {{ item.value }}
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <div
      class="screen-title user-title"
      :class="{ 'title-opacity': titleOpacity }"
    >
      重点网民发帖
    </div>
    <div class="flex-1" style="overflow: hidden">
      <vue3-seamless-scroll
        :list="userPostList"
        hover
        style="height: 100%"
        :step="0.2"
        :wheel="true"
      >
        <div
          v-for="(item, index) in userPostList"
          :key="index"
          class="user-post-item"
          :class="{ active: index % 2 === 0 }"
          @click="openPortraitDialog(item)"
        >
          <div class="user-post-left">
            <img :src="item.avatar" alt="" class="user-post-avatar" />
            <span class="user-post-title">{{ item.internetUserName }}</span>
          </div>
          <div class="user-post-content">累计发文： {{ item.totalNum }}</div>
          <!-- <div class="user-post-content">年末发文: {{ item.yearNum }}</div> -->
          <!-- <div class="user-post-content">本月发文: {{ item.monthNum }}</div> -->
          <div class="user-post-content">参与事件：{{ item.eventNum }}</div>
        </div>
      </vue3-seamless-scroll>
    </div>

    <div class="w-full flex flex-col">
      <div
        class="screen-title po-pie-title"
        :class="{ 'title-opacity': titleOpacity }"
      >
        舆情数据展示
      </div>
      <div style="display: flex; justify-content: flex-end">
        <div
          class="time-range-btn w-full"
          :class="{ active: activeTimeRange === 1 }"
          @click="getTimeRange(1)"
        >
          周
        </div>
        <div
          class="time-range-btn w-full"
          :class="{ active: activeTimeRange === 2 }"
          @click="getTimeRange(2)"
        >
          日
        </div>
      </div>

      <PieChart :render-data="pieData" />
    </div>

    <NetizenPortraitDialog ref="netizenPortraitDialogRef" />
  </div>
</template>

<script setup>
import PieChart from "./PieChart.vue";
import {
  eventOpinionIn,
  eventOpinionUserPost,
  eventOpinionTimeRange,
} from "@/api/eventOpinion/index";
import { eventDetails } from "@/api/eventManagement/index";
import NetizenPortraitDialog from "./NetizenPortraitDialog.vue";

defineProps({
  titleOpacity: {
    type: Boolean,
    default: true,
  },
});

const inList = ref([]);
const userPostList = ref([]); // 贴文列表
const activeTimeRange = ref(1);
const pieData = ref({}); // 饼图数据
const netizenPortraitDialogRef = ref(null);

const eventNumMap = {
  discoveryNum: "发现数",
  newsNum: "媒体报道数",
  rumorNum: "谣言数",
  sensitiveNum: "敏感数",
  dealNum: "处置数",
};

const openPortraitDialog = (row) => {
  console.log("row", row);
  netizenPortraitDialogRef.value.openDialog(row);
};

/**
 * 获取处置事件
 */
async function getIn() {
  const res = await eventOpinionIn();
  if (res.code === 200) {
    inList.value = res.data.map((item) => ({
      ...item,
      poList: [],
      isScroll: true,
    }));

    // 获取事件对应的舆情
    const resArr = [];
    for (let i = 0; i < inList.value.length; i++) {
      const ele = inList.value[i];
      resArr.push(
        await eventDetails({
          eventId: ele.eventId,
          isExclude: false,
          pageNum: 1,
          pageSize: 1,
        })
      );
    }

    // console.log("resArr", resArr);
    // const resArr = await Promise.all(promiseArr);
    // resArr.forEach((ele, index) => {
    //   if (ele.code === 200) {
    //     inList.value[index].poList = ele.rows.map((i) => {
    //       return {
    //         id: i.id,
    //         publishTime: i.publishTime,
    //         platformTag: i.platformTag,
    //         content: i.content,
    //       };
    //     });
    //   }
    // });
    inList.value.forEach((ele, index) => {
      ele.eventDetail = {
        // ...resArr[index].rows[0],
        id: resArr[index].rows[0].id,
        publishTime: resArr[index].rows[0].publishTime,
        platformTag: resArr[index].rows[0].platformTag,
        content: resArr[index].rows[0].content,
        currentPoNum: resArr[index].total,
      };
    });

    // console.log("inList.value", inList.value);
  }
}

function goToEventScreen({ eventId, name }) {
  window.open(
    `${window.location.origin}/patrol-opinion/eventManage/screen?eventId=${eventId}&eventName=${name || "暂无事件名"}`,
    "_blank"
  );
}

/**
 * 获取网名发帖
 */
function getUserPost() {
  eventOpinionUserPost().then((res) => {
    userPostList.value = res.data;
  });
}

/**
 * 获取舆情数据
 */
function getTimeRange(value) {
  activeTimeRange.value = value;
  eventOpinionTimeRange(value).then((res) => {
    pieData.value = res.data;
  });
}

getIn();
getUserPost();
getTimeRange(1);
</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;

.rightInfo-container {
  width: 100%;
  height: 100%;
  padding: px2vw(87) px2vw(50) 0 px2vw(45);

  display: flex;
  flex-direction: column;

  .screen-title {
    width: 100%;
    line-height: px2vw(18);
    color: #feffff;
    text-align: center;
    font-size: px2vw(19);
    font-family: PangMenZhengDao;
    position: relative;
    &:before {
      content: "";
      width: px2vw(160);
      height: px2vw(18);
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      transition: opacity 1s ease;
      opacity: 1;
    }
    &:after {
      content: "";
      width: px2vw(160);
      height: px2vw(18);
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      right: 0;
      transform: rotate(180deg);
      transition: opacity 1s ease;
      opacity: 1;
    }
    &.title-opacity {
      &:before {
        background: url(@/assets/screen/title-border.png) no-repeat;
        background-size: 100% 100%;
      }
      &:after {
        background: url(@/assets/screen/title-border.png) no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  // 正在处置事件
  .event-title {
    margin-bottom: px2vw(18);
  }
  .event-wrapper {
    height: px2vw(255);
  }
  .el-carousel {
    :deep(.el-carousel__indicators) {
      width: 100%;
      text-align: center;
      .el-carousel__indicator {
        --el-carousel-indicator-padding-vertical: 0;
        --el-carousel-indicator-padding-horizontal: 0;
        &.is-active {
          .el-carousel__button {
            background-color: #1991e8;
          }
        }
        .el-carousel__button {
          background-color: #0a4266;
          opacity: 1;
        }
      }
    }
  }
  .simple-info {
    height: px2vw(64);
    padding: px2vw(9) px2vw(30) px2vw(10) px2vw(19);
    background: linear-gradient(
      270deg,
      rgba(25, 145, 232, 0.3) 0%,
      rgba(25, 145, 232, 0.15) 47%,
      rgba(25, 145, 232, 0.3) 100%
    );
    margin-bottom: px2vw(13);
    position: relative;

    &::before {
      content: "";
      display: inline-block;
      width: px2vw(8);
      height: px2vw(64);
      background: rgba(25, 145, 232, 0.5);
      position: absolute;
      left: 0;
      top: 0;
    }

    .simple-info-title {
      font-family: PangMenZhengDao;
      font-size: px2vw(17);
      color: #feffff;
      line-height: px2vw(20);
      flex: 1;
    }
    .po-total {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 800;
      font-size: px2vw(12);
      color: #feffff;
      line-height: px2vw(14);
      opacity: 0.8;
    }
    .create-time {
      margin-top: px2vw(6);
      color: #feffff;
      font-size: px2vw(12);
      line-height: px2vw(14);
      opacity: 0.6;
    }
  }
  .po-type-wrapper {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    align-items: center;

    .type-item {
      height: px2vw(80);
      width: 33.33%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &.has-border {
        position: relative;
        &:after {
          content: "";
          width: px2vw(2);
          height: 100%;
          background: url(@/assets/screen/type-border.svg) no-repeat;
          background-size: 100% 100%;
          position: absolute;
          right: 0;
        }
      }
      .type-item-title {
        margin-bottom: px2vw(8);
        font-size: px2vw(15);
        color: #feffff;
      }
      .type-item-num {
        font-family: PangMenZhengDao;
        font-size: px2vw(29);
        color: #ffffff;
        text-shadow: 0 0 11px rgba(87, 198, 255, 0.76);
      }
    }
  }
  .event-po-list {
    height: px2vw(147);
    padding: px2vw(13) px2vw(10);
    .event-po-item {
      height: px2vw(68);
      background: rgba(25, 145, 232, 0.12);
      border-radius: px2vw(9);
      padding: px2vw(10) px2vw(16) px2vw(15) px2vw(8);
      margin-bottom: px2vw(13);

      .create-time {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        font-size: px2vw(14);
        color: #ffffff;
        line-height: px2vw(16);
      }
      .po-plat {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 800;
        font-size: px2vw(12);
        color: #feffff;
        line-height: px2vw(14);
        opacity: 0.6;
      }

      .event-info-title {
        margin-top: px2vw(6);
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        font-size: px2vw(12);
        color: #a3c1d1;
        line-height: px2vw(14);
        opacity: 0.8;
      }
    }
  }

  // 重点网民发帖
  .user-title {
    margin-top: px2vw(45);
    margin-bottom: px2vw(18);
  }
  .user-post-item {
    cursor: pointer;
    height: px2vw(51);
    // display: flex;
    // align-items: center;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    align-items: center;
    justify-items: center; // 让每个格子内容水平居中
    font-size: px2vw(14);
    color: #d6e8f0;
    .user-post-left {
      // margin-left: px2vw(10);
      display: flex;
      gap: px2vw(10);
      align-items: center;
    }
    &.active {
      background: url(@/assets/screen/simple-back.svg) no-repeat;
      background-size: cover;
    }
    .user-post-avatar {
      width: px2vw(32);
      height: px2vw(32);
      // margin-left: px2vw(10);
      flex-shrink: 0;
      border-radius: 50%;
    }
    .user-post-title {
      // margin-left: px2vw(10);
      // margin-right: px2vw(30);
      flex-shrink: 0;
      font-family: PangMenZhengDao;
      font-size: px2vw(17);
      color: #6bf2ff;
      text-shadow: 0 px2vw(1) px2vw(4) rgba(124, 255, 254, 0.6);
    }
    .user-post-content {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: px2vw(14);
      color: #d6e8f0;
      line-height: px2vw(21);
      text-align: center;
      font-style: normal;
      // width: 30%;
      // &:not(:last-child) {
      //   margin-right: px2vw(30);
      // }
    }
  }

  // 舆情数据展示
  .po-pie-title {
    margin-top: px2vw(40);
    margin-bottom: px2vw(15);
  }
  .time-range-btn {
    width: px2vw(67);
    height: px2vw(20);
    text-align: center;
    font-family: PangMenZhengDao;
    font-size: px2vw(16);
    color: #feffff;
    z-index: 20;
    cursor: pointer !important;
    &.active {
      background: url(@/assets/screen/time-range.svg) no-repeat;
      background-size: contain;
      background-position: 0 px2vw(8);
    }
  }
  .pieChart-container {
    height: px2vw(180);
    position: relative;
    &:before {
      content: "";
      width: px2vw(310);
      height: px2vw(350);
      position: absolute;
      top: px2vw(-95);
      left: 0;
      right: 0;
      margin-left: auto;
      margin-right: auto;
      background: url(@/assets/screen/pie-back.gif) no-repeat;
      background-position: center center;
      background-size: 100% 100%;
      transform: rotateX(55deg);
      opacity: 0.6;
      filter: saturate(65%);
    }
  }
}
</style>
