<template>
  <div class="event-container p-[20px]">
    <div class="h-full flex gap-x-[20px]">
      <!-- 事件管理 -->
      <div class="container-left">
        <div class="w-full h-full flex flex-col gap-y-[10px]">
          <!-- 表单 -->
          <div class="flex justify-between">
            <el-select
              class="w-[150px]"
              v-model="listParams.status"
              clearable
              @change="selectChange"
            >
              <el-option
                v-for="(item, index) in [
                  { label: '全部', value: '全部' },
                  ...event_status,
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <img
              src="@/assets/images/eventAdd.svg"
              @click="handleAdd"
              style="cursor: pointer"
              alt=""
            />
          </div>

          <!-- 卡片列表 -->
          <!--          <InfiniteScrollList-->
          <!--              ref="scrollRef"-->
          <!--              v-slot="{ row }"-->
          <!--              :get-list="listEvent"-->
          <!--              :get-list-after="handleList"-->
          <!--              :params="listParams"-->
          <!--          >-->
          <div
            class="w-full h-full flex flex-col gap-[16px] pr-[10px]"
            style="overflow-y: auto; overflow-x: hidden"
          >
            <Card
              v-for="(row, index) in eventList"
              :key="index"
              :content="row"
              :is-active="detailParams.eventId === row.id"
              @handle-edit="handleEdit"
              @handle-click="handleClickCard"
              @refresh-list="handleRefresh"
            />
          </div>
          <!--          </InfiniteScrollList>-->

          <!-- 新增编辑弹框 -->
          <AddEditDialog ref="addEditDialogRef" @refresh-list="handleRefresh" />
        </div>
      </div>
      <!-- 舆情管理 -->
      <div
        class="w-[82%] h-full flex flex-col"
        style="background-color: #fff; padding: 20px 10px"
      >
        <div class="event-title">
          <p class="aed-name" style="margin-bottom: 10px; font-size: 16px">
            {{ activeEventDetail.name }}
          </p>
          <!-- qnmd ydwz -->
          <div id="des-container">
            <div style="font-size: 14px; color: #646a73" v-if="!isDouble">
              {{ activeEventDetail.description }}
            </div>
            <el-tooltip
              v-else
              :content="activeEventDetail.description"
              placement="bottom-end"
            >
              <div class="des-hide" style="font-size: 14px; color: #646a73">
                {{ activeEventDetail.description }}
              </div>
            </el-tooltip>
          </div>
        </div>
        <div style="display: flex; margin-bottom: 16px">
          <div class="event-data-card flex justify-between">
            <div class="w-[130px]" style="border-right: 1px solid #ebeef2">
              <div class="event-data-title">信息总数</div>
              <div class="event-data-value">{{ activeEvent.infoTotalNum }}</div>
            </div>
            <div class="flex justify-between ml-[24px]">
              <div class="mr-[42px]">
                <p class="event-data-title" style="margin-bottom: 12px">
                  舆情平台总数
                </p>
                <p class="event-data-value">{{ activeEvent.platformNum }}</p>
              </div>
              <div class="mr-[42px]">
                <p class="event-data-title" style="margin-bottom: 12px">
                  群消息报送
                </p>
                <p class="event-data-value">{{ activeEvent.groupNum }}</p>
              </div>
              <div class="mr-[11px]">
                <p class="event-data-title" style="margin-bottom: 12px">
                  舆情报送
                </p>
                <p class="event-data-value">{{ activeEvent.infoReportNum }}</p>
              </div>
            </div>
          </div>
          <div class="event-data-card flex justify-between">
            <!--            <div class="w-[130px]" style="border-right: 1px solid #EBEEF2;">-->
            <!--              <div class="event-data-title">舆情状态统计</div>-->
            <!--            </div>-->
            <div class="flex justify-between">
              <div class="mr-[42px]">
                <p class="event-data-title" style="margin-bottom: 12px">
                  待研判
                </p>
                <p class="event-data-value">{{ activeEvent.waitJudgeNum }}</p>
              </div>
              <div class="mr-[11px]">
                <p class="event-data-title" style="margin-bottom: 12px">
                  已处置
                </p>
                <p class="event-data-value">{{ activeEvent.pointedNum }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-between" style="margin-top: 25px">
          <div>
            <el-button type="primary" @click="handleAddOpinion" icon="Plus"
              >新增</el-button
            >
            <el-button @click="handleRemove(false)" icon="Delete"
              >批量移除</el-button
            >
          </div>
          <el-form :model="detailParams" ref="queryDetailRef" :inline="true">
            <el-form-item prop="reportNumber">
              <el-input
                v-model="detailParams.reportNumber"
                placeholder="请输入輿情编号"
                clearable
                style="width: 200px"
                @change="handleQuery"
                @keyup.enter="handleQuery"
                :prefix-icon="Search"
              />
            </el-form-item>
            <el-form-item prop="categoryTag">
              <el-select
                v-model="detailParams.categoryTag"
                placeholder="请选择輿情类别"
                clearable
                style="width: 200px"
                @change="handleQuery"
              >
                <template #prefix>輿情类别</template>
                <el-option label="全部" value="全部" />
                <el-option
                  v-for="dict in opinion_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="reportStatus">
              <el-select
                v-model="detailParams.reportStatus"
                placeholder="请选择輿情状态"
                clearable
                style="width: 200px"
                @change="handleQuery"
              >
                <template #prefix>輿情状态</template>
                <el-option label="全部" value="全部" />
                <el-option
                  v-for="dict in process_status_handle"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <!-- 舆情表格 -->
        <PublicOpinionTable
          ref="publicOpinionTableRef"
          :interface-info="interfaceInfo"
          :table-columns="
            PUBLIC_OPINION_INFO_COLUMNS([
              'handleUnit',
              'handler',
              'handleStatus2',
              'score',
            ])
          "
          :operation-column-width="240"
          :table-max-height="'unset'"
          @mulSelect="
            (val) => {
              ids = val.map((item) => {
                return item.id;
              });
            }
          "
          @showDrawer="showDrawer"
          @showDialog="showDialog"
          @refreshData="refreshData"
          style="flex: 1"
        >
        </PublicOpinionTable>
      </div>
    </div>
    <!-- 添加舆情对话框 -->
    <el-dialog title="新增" v-model="open" width="1200px" append-to-body>
      <el-form :model="addParams" ref="queryAddDetailRef" :inline="true">
        <el-form-item prop="reportNumber" style="margin-right: 8px !important">
          <el-input
            v-model="addParams.reportNumber"
            placeholder="请输入輿情编号"
            clearable
            style="width: 200px"
            @change="handleQueryAdd"
            @keyup.enter="handleQueryAdd"
          />
        </el-form-item>
        <el-form-item prop="categoryTag">
          <el-select
            v-model="addParams.categoryTags"
            placeholder="请选择"
            clearable
            style="width: 200px"
            @change="handleQueryAdd"
          >
            <template #prefix>輿情类别</template>
            <el-option label="全部" value="全部" />
            <el-option
              v-for="dict in opinion_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <el-table
        :data="addOpinionList"
        height="500px"
        ref="addTableRef"
        row-key="id"
        @selection-change="handleAddSelectionChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
          reserve-selection
        />
        <el-table-column
          prop="reportNumber"
          label="舆情编号"
          width="150"
        ></el-table-column>
        <el-table-column prop="createTime" label="报送时间" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="platformTag"
          label="发布平台"
          width="150"
        ></el-table-column>
        <el-table-column
          prop="netizenNickname"
          label="网民昵称"
          width="150"
        ></el-table-column>
        <el-table-column
          prop="content"
          label="舆情内容"
          width="300"
          show-overflow-tooltip
        >
          <template #default="scope">
            <ImagePreview
              v-if="contentIsImg(scope.row.content)"
              :src="scope.row.content"
              :width="100"
              :height="100"
              @click.stop
            />
            <div v-else class="poContentWrapper">
              {{ scope.row.content || "-" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="photoUrl" label="图片" width="150">
          <template #default="scope">
            <ImagePreview
              v-if="scope.row.photoUrl.length"
              :src="'/profile' + scope.row.photoUrl[0].split('/profile')[1]"
              :width="100"
              :height="100"
            />
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="firstReportTag" label="是否首报" width="120">
          <template #default="scope">
            <div>{{ firstLabel(scope.row.isFirst) }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="舆情标题"
          width="150"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div>{{ scope.row.title || "-" }}</div>
          </template>
        </el-table-column>
        <!--        <el-table-column prop="eventName" label="舆情事件" width="150"></el-table-column>-->
        <el-table-column prop="reportSource" label="舆情来源" width="150">
          <template #default="scope">
            {{ getDict(scope.row.reportSource, opinion_source_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.publishTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sourceStatus" label="贴文状态" width="150">
          <template #default="scope">
            {{ getDict(scope.row.sourceStatus, source_status) }}
          </template>
        </el-table-column>
        <el-table-column prop="reportStatus" label="处置状态" width="150">
          <template #default="scope">
            <div
              class="truncate statusClass"
              :style="handleStatusF(scope.row.reportStatus).style"
            >
              {{ handleStatusF(scope.row.reportStatus).text }}
            </div>
            <!--            {{ getDict(scope.row.reportStatus, process_opt_status) }}-->
          </template>
        </el-table-column>
        <el-table-column
          prop="targetGroup"
          label="目标群"
          width="150"
        ></el-table-column>
      </el-table>
      <pagination
        style="position: relative !important; margin: 0 20px 32px 0"
        :total="addTotal"
        v-model:page="addParams.pageNum"
        v-model:limit="addParams.pageSize"
        :page-sizes="[15, 20, 30, 50]"
        @pagination="getAddEventDetails"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 单个操作弹窗 -->
    <HandleDialog
      v-model="showHandleDialog"
      :type-text="typeText"
      :po-info="poInfo"
      @refreshData="refreshProcess"
    />

    <!-- 编辑/查看抽屉 -->
    <EditInfoDrawer
      v-model="showInfoDrawer"
      :drawer-type="drawerType"
      :po-info="poInfo"
      @showDialog="
        (callback) => showDialog(TRANSFER_TYPE.VIEW, poInfo, callback)
      "
      @refreshData="refreshData"
    />
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";
import Card from "./components/card.vue";
import {
  listEvent,
  eventDetails,
  removeEventOpinion,
  addEventOpinion,
  eventSumInfo,
} from "@/api/eventManagement/index";
import AddEditDialog from "./components/addEditDialog.vue";
import { PUBLIC_OPINION_INFO_COLUMNS } from "@/views/publicOpinionManage/config/tableColumns";
import PublicOpinionTable from "@/views/eventManage/components/PublicOpinionTable.vue";
import EditInfoDrawer from "@/views/publicOpinionManage/components/EditInfoDrawer/index.vue";
import { HANDLE_STATUS } from "@/views/publicOpinionManage/config/constant.js";
import { useDict, getDict } from "@/utils/dict.js";

import { TRANSFER_TYPE } from "./config/mapRel.js";
import HandleDialog from "../publicOpinionManage/publicOpinionInfo/components/HandleDialog.vue";
import { IS_FIRST_STATUS_LIST } from "../publicOpinionManage/config/constant.js";
import { HANDLE_STATUS_LIST } from "./config/constant";

const {
  event_status,
  opinion_type,
  process_opt_status,
  opinion_source_type,
  platform_type,
  source_status,
  process_status_handle,
  media_type,
} = useDict(
  "event_status",
  "opinion_type",
  "process_opt_status",
  "opinion_source_type",
  "platform_type",
  "source_status",
  "process_status_handle",
  "media_type"
);

const { proxy } = getCurrentInstance();

const eventList = ref([]);
const publicOpinionTableRef = ref();
const addEditDialogRef = ref(null);
const addOpinionList = ref([]);
const total = ref(0);
const addTotal = ref(0);
const open = ref(false);
const ids = ref([]);
const addIds = ref([]);

// dogp code here
const showHandleDialog = ref(false); // 是否展示处理弹窗
const showInfoDrawer = ref(false);
const drawerType = ref(""); // edit or view
const typeText = ref("");
const poInfo = ref({});
const callbackFun = ref(null); // 获取舆情流程的回调
const addScoreRef = ref(null);
const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 内容是否为图片

const data = reactive({
  listParams: {
    status: "全部",
  },
  detailParams: {
    eventId: null,
    reportNumber: null,
    categoryTag: "全部",
    reportStatus: "全部",
    isExclude: false,
    pageNum: 1,
    pageSize: 15,
  },
  addParams: {
    eventId: null,
    reportNumber: null,
    categoryTags: "全部",
    isExclude: true,
    pageNum: 1,
    pageSize: 15,
  },
  activeEvent: {
    infoTotalNum: 0,
    platformNum: 0,
    groupNum: 0,
    infoReportNum: 0,
    waitJudgeNum: 0,
    pointedNum: 0,
  },
  isDouble: false,
});

const { listParams, detailParams, activeEvent, addParams, isDouble } =
  toRefs(data);

const interfaceInfo = computed(() => ({
  api: getEventDetails,
  params: {
    reportProcess: HANDLE_STATUS.FINISH_HANDLE,
    ...detailParams.value,
    // categoryTag: detailParams.value.categoryTag === 'all' ? null : detailParams.value.categoryTag,
    // reportStatus: detailParams.value.reportStatus === 'all' ? null : detailParams.value.reportStatus,
    categoryTag:
      detailParams.value.categoryTag === "全部"
        ? null
        : detailParams.value.categoryTag,
    reportStatus:
      detailParams.value.reportStatus === "全部"
        ? null
        : detailParams.value.reportStatus,
    total: total.value,
  }, // 已处置
  getDataMap,
}));

const handleStatusF = computed(() => (val) => {
  const obj = HANDLE_STATUS_LIST.find((i) => i.value === val);
  return {
    text: obj?.label || "-",
    style: { color: obj?.color || "#333333", background: obj?.bgColor },
  };
});

/**
 * 数据整理
 */
function getDataMap(data) {
  return data.map((ele) => {
    let res = {
      id: ele.id,
      poId: ele.reportNumber,
      isFirst: ele.firstReportTag, // 0否 1是
      poName: ele.title,
      workUnit: ele.workUnit || "-", // 上报单位
      poContent: ele.content,
      poLink: ele.linkUrl,
      poEvent: ele.eventList?.length ? ele.eventList : "-",
      poMediaType: ele.mediaType, // 媒体类型
      poFrom:
        opinion_source_type.value.find((i) => i.value === ele.reportSource)
          ?.label || "-",
      platformType: ele.platformTag || "-",
      happenLocation: ele.involvedArea || "-",
      isSensitive: ele.sensitiveTag + "", // 0否 1是
      poType:
        opinion_type.value.find((i) => i.value === ele.categoryTag)?.label ||
        "-",
      wechatNickname: ele.wechatNickname || "-", // 微信报送昵称
      netizenNickname: ele.netizenNickname || "-", // 网名昵称
      netizenAccountId: ele.netizenAccountId, // 网名id
      publicTime: proxy.parseTime(ele.publishTime) || "-",
      poImg: ele.photoUrl ? ele.photoUrl.split(",") : "-",
      articleStatus: ele.sourceStatus, // 0未删除 1已删除
      handleStatus: ele.reportStatus,
      createTime: proxy.parseTime(ele.createTime),
      targetGroup: ele.targetGroup || "-",
      isInvalid: ele.disabled, // 1无效 0有效
      rumorStatus: ele.rumorStatus,
      score:
        ele.baseScore === null && ele.scorePointValue === null
          ? "-"
          : ele.baseScore + ele.scorePointValue, // 赋分
      linkUrlCount: ele.linkUrlCount,
    };
    return res;
  });
}

const openDialog = (type, row) => {
  addEditDialogRef.value.openDialog(type, row);
};
const activeEventDetail = computed(() => {
  if (detailParams.value.eventId) {
    return eventList.value.find(
      (item) => item.id === detailParams.value.eventId
    );
    // return proxy.$refs.scrollRef.tableData.find(item => item.id === detailParams.value.eventId)
  } else {
    return {
      name: "",
      description: "",
    };
  }
});

function getEventList() {
  // listEvent({ status: listParams.value.status === 'all' ? null : listParams.value.status }).then(res => {
  listEvent({
    status: listParams.value.status === "全部" ? null : listParams.value.status,
  }).then((res) => {
    eventList.value = res.data;
    let isValid = res.data.some(
      (item) => item.id === detailParams.value.eventId
    );

    if (history.state.eventId) {
      handleClickCard(
        res.data.find((item) => item.id === history.state.eventId)
      );
    } else if (!detailParams.value.eventId || !isValid) {
      handleClickCard(res.data[0]);
    }
  });
}

const handleEdit = (row) => {
  // console.log("---->handleEdit");
  openDialog("编辑事件", row);
};

const handleAdd = () => {
  // console.log("---->handleAdd");
  openDialog("新增事件");
};

const selectChange = () => {
  judgeHeight();
  getEventList();
  // console.log("selectChange");

  // 更新列表数据
  // proxy.$refs['scrollRef'].onRefresh();
};

function handleRefresh() {
  // proxy.$refs['scrollRef'].onRefreshCurrent();
  getEventList();
}
function handleClickCard(data) {
  console.log("ssssssssssssssssssssssssssssssss");
  detailParams.value.eventId = data.id;
  addParams.value.categoryTag =
    addParams.value.categoryTags === "全部" ? "" : addParams.value.categoryTags;
  addParams.value.eventId = data.id;
  if (proxy.$refs.queryDetailRef) {
    proxy.resetForm("queryDetailRef");
  }
  proxy.$nextTick(() => {
    judgeHeight();
  });
  handleQuery();
}
function handleList(data) {
  if (data.length > 0) {
    handleClickCard(data[0]);
  }
  return data;
}
function handleQuery() {
  detailParams.value.pageNum = 1;
  refreshData();
}
function handleQueryAdd() {
  addParams.value.categoryTag =
    addParams.value.categoryTags === "全部" ? "" : addParams.value.categoryTags;
  addParams.value.pageNum = 1;
  getAddEventDetails();
}
function getEventSumInfo() {
  eventSumInfo(detailParams.value.eventId).then((res) => {
    activeEvent.value = res.data;
  });
}
function getEventDetails() {
  if (!detailParams.value.eventId) return { code: 0 };
  getEventSumInfo();
  return eventDetails({
    ...detailParams.value,
    categoryTag:
      detailParams.value.categoryTag === "全部"
        ? null
        : detailParams.value.categoryTag,
    reportStatus:
      detailParams.value.reportStatus === "全部"
        ? null
        : detailParams.value.reportStatus,
    /*    categoryTag: detailParams.value.categoryTag === 'all' ? null : detailParams.value.categoryTag,
    reportStatus: detailParams.value.reportStatus === 'all' ? null : detailParams.value.reportStatus,*/
  });
}
/**
 * 刷新表格数据
 */
function refreshData(params) {
  if (params) {
    detailParams.value.pageNum = params.pageNum;
    detailParams.value.pageSize = params.pageSize;
  }
  publicOpinionTableRef.value.getTableData(detailParams.value);
}
function getAddEventDetails() {
  addParams.value.categoryTag =
    addParams.value.categoryTags === "全部" ? "" : addParams.value.categoryTags;
  eventDetails(addParams.value).then((res) => {
    addOpinionList.value = res.rows.map((item) => {
      return {
        ...item,
        photoUrl: item.photoUrl
          ? item.photoUrl.split(",").map((photo) => {
              return `${import.meta.env.VITE_APP_PORTAL_API}${photo}`;
            })
          : [],
      };
    });
    addTotal.value = res.total;
  });
}
function resetQuery() {
  proxy.resetForm("queryDetailRef");
  handleQuery();
}
function resetQueryAdd() {
  proxy.resetForm("queryAddDetailRef");
  handleQueryAdd();
}
function handleAddOpinion() {
  resetAdd();
  open.value = true;
  getAddEventDetails();
}

function getContent(str) {
  let list = str.split(",");
  list.forEach((e) => {
    if (e.includes("/profile/upload")) {
    }
  });
  return str;
}
function handleRemove(row) {
  const payload = {
    eventId: detailParams.value.eventId,
    opinionIds: row ? [row.id] : ids.value,
  };
  proxy.$modal
    .confirm("是否确认移除？")
    .then(function () {
      return removeEventOpinion(payload);
    })
    .then(() => {
      refreshData();
      proxy.$modal.msgSuccess("移除成功");
    })
    .catch(() => {});
}
/** 选择条数  */
function handleAddSelectionChange(selection) {
  addIds.value = selection.map((item) => item.id);
}
function submitForm() {
  let payload = {
    eventId: detailParams.value.eventId,
    opinionIds: addIds.value,
  };
  addEventOpinion(payload).then((response) => {
    proxy.$modal.msgSuccess("添加成功");
    open.value = false;
    // proxy.$refs['scrollRef'].onRefreshCurrent();
    refreshData();
  });
}
function cancel() {
  open.value = false;
}
function resetAdd() {
  addIds.value = [];
  addOpinionList.value = [];
  if (proxy.$refs.addTableRef) {
    proxy.$refs.addTableRef.clearSelection();
  }
  if (proxy.$refs.queryAddDetailRef) {
    proxy.resetForm("queryAddDetailRef");
  }
}

/**
 * 展示编辑/查看抽屉
 */
function showDrawer(type, row) {
  drawerType.value = type;
  poInfo.value = row;
  showInfoDrawer.value = true;
}

/**
 * 刷新舆情流程数据和表格数据
 */
function refreshProcess() {
  if (callbackFun.value) {
    callbackFun.value();
  }

  refreshData();
}

/**
 * 展示处理弹窗/赋分弹窗
 */
function showDialog(text, row, callback) {
  if (text === TRANSFER_TYPE.SCORE) {
    addScoreRef.value.openDialog(row);
  } else if (text === TRANSFER_TYPE.REMOVE) {
    handleRemove(row);
  } else {
    typeText.value = text;
    poInfo.value = row;
    showHandleDialog.value = true;
    callbackFun.value = callback;
  }
}

const firstLabel = computed(
  () => (val) =>
    IS_FIRST_STATUS_LIST.find((ele) => ele.value === val)?.label || "-"
);

function judgeHeight() {
  const desc = document.getElementById("des-container");
  console.log(desc.clientHeight);
  if (desc.clientHeight > 10) {
    isDouble.value = true;
  } else {
    isDouble.value = false;
  }
}

onMounted(() => {
  proxy.$nextTick(() => {
    judgeHeight();
  });
  window.addEventListener("resize", () => {
    judgeHeight();
  });
});

getEventList();
</script>

<style lang="scss" scoped>
.event-container {
  width: 100%;
  height: 100%;

  .container-left {
    width: 18%;
    height: 100%;
    padding: 20px 10px;
    background-color: #fff;
    border-right: 1px solid #e9eaf2;
  }
}
.event-title {
  flex-shrink: 0;
  height: 80px;
  margin-bottom: 16px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f3f3f3;
  color: #1f2329;
  p {
    width: 100%;
  }
}
.event-data-card {
  height: 100px;
  padding: 16px;
  margin-right: 20px;
  background-color: #f9fafb;
}
.event-data-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #646a73;
}
.event-data-value {
  margin-top: 13px;
  font-family: DINAlternate-Bold;
  font-size: 29px;
  color: #1f2329;
  letter-spacing: 0;
}

.poContentWrapper {
  white-space: normal;
  word-break: break-all;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4; /* 控制几行打点 */
  line-clamp: 4;
  overflow: hidden;
}

.statusClass {
  width: 50px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  border-radius: 3px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
}

.des-hide {
  display: -webkit-box; /* 将容器以弹性盒子形式布局 */
  -webkit-line-clamp: 2; /* 限制文本显示为两行 */
  -webkit-box-orient: vertical; /* 将弹性盒子的主轴方向设置为垂直方向 */
  overflow: hidden; /* 隐藏容器中超出部分的内容 */
  text-overflow: ellipsis; /* 超出容器范围的文本显示省略号 */
}

.aed-name {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: #1f2329;
}

:deep(.el-dialog__body) {
  overflow-x: hidden !important;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 8px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 8px !important;
}
</style>
