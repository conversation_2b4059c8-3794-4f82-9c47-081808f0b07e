import portalRequest from "@/utils/portalRequest.js";
// 获取列表
export function getTreeList() {
  return portalRequest({
    url: "/solution/manage/list",
    method: "get",
  });
}

// 获取文件夹-方案列表
export function getPatrolPlanList() {
  return portalRequest({
    url: "/solution/manage/folder/solution/tree",
    method: "get",
  });
}

// 新增方案
export function addPlan(data) {
  return portalRequest({
    url: "/solution/manage/add",
    method: "post",
    data: data,
  });
}

//修改方案
export function editPlan(data) {
  return portalRequest({
    url: "/solution/manage/modify",
    method: "post",
    data: data,
  });
}

//删除方案
export function delPlan(id) {
  return portalRequest({
    url: `/solution/manage/${id}`,
    method: "post",
  });
}

// 查询上级文件夹树数据
export function AllFolderList() {
  return portalRequest({
    url: `/solution/manage/folder/list`,
    method: "get",
  });
}

// 新增文件夹
export function editFolder(data) {
  return portalRequest({
    url: `/solution/manage/folder/edit`,
    method: "post",
    data,
  });
}

// 新增文件夹
export function addFolder(data) {
  return portalRequest({
    url: `/solution/manage/folder/add`,
    method: "post",
    data,
  });
}

// 删除文件夹
export function delFolder(id) {
  return portalRequest({
    url: `/solution/manage/folder/remove/${id}`,
    method: "post",
  });
}
