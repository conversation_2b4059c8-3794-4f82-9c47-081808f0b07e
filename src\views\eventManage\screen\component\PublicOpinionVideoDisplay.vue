<template>
  <div class="public-opinion-video w-full h-full flex flex-col">
    <item-title class="w-full" :title="title" />
    <div v-if="publicOpinionVideoDisplay.length" class="video-list h-full">
      <div
        class="video-item"
        v-for="(item, i) in publicOpinionVideoDisplay"
        :key="i"
        @click="viewVideo(item.linkUrl)"
      >
        <ImagePreview :src="item.photoUrl" class="video-img" />
        <div class="video-info">
          <div class="info-content">{{ item.title }}</div>
          <div>
            {{
              item.publishTime
                ? dayjs(item.publishTime).format("YYYY-MM-DD HH:mm:ss")
                : ""
            }}
          </div>
          <div class="video-platform">来源：{{ item.platformTag }}</div>
        </div>
      </div>
    </div>
    <div
      class="h-[150px]"
      style="
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
      "
      v-else
    >
      暂无数据
    </div>
  </div>
</template>

<script setup>
import ItemTitle from "@/views/eventManage/screen/component/ItemTitle.vue";
import dayjs from "dayjs";
import { onElementReady } from "@/utils/element.js";
import { debounce } from "lodash";

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
  publicOpinionVideoDisplay: {
    type: Array,
    default: () => [],
  },
});

const publicOpinionVideoDisplay = computed(() => {
  return props.publicOpinionVideoDisplay;
});

function viewVideo(url) {
  window.location.href = url;
}

onMounted(async () => {
  //onElementReady(
  //  ".video-list",
  //  () => {
  //    // 禁用原生滚动条
  //
  //    const container = document.querySelector(".video-list");
  //    const items = document.querySelectorAll(".video-item");
  //    let currentIndex = 0;
  //    // 监听鼠标滚轮事件
  //    items.forEach((item, index) => {
  //      function wheel(event) {
  //        event.preventDefault(); // 阻止默认的滚动行为
  //        const delta = event.deltaY; // 获取鼠标滚轮的滚动方向
  //        if (delta > 0) {
  //          currentIndex = Math.min(items.length - 1, currentIndex + 1);
  //        }
  //        if (delta < 0) {
  //          currentIndex = Math.max(0, currentIndex - 1);
  //        }
  //        // 计算滚动位置
  //        let scrollTop = 0;
  //        for (let i = 0; i < currentIndex; i++) {
  //          scrollTop += items[i].offsetHeight + 23;
  //        }
  //        container.scrollTo({
  //          top: scrollTop,
  //          behavior: "smooth",
  //        });
  //      }
  //
  //      const debounceWheel = debounce(wheel, 100);
  //      item.addEventListener("wheel", (event) => {
  //        event.preventDefault(); // 阻止默认的滚动行为
  //        debounceWheel(event);
  //      });
  //    });
  //  },
  //  {
  //    observeOnce: true,
  //  },
  //);
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.video-list::-webkit-scrollbar {
  width: 0;
}

/* 隐藏水平滚动条 */
.video-list::-webkit-scrollbar {
  height: 0;
}

.public-opinion-video {
  gap: px2vw(10);
}

.video-list {
  width: inherit;
  /*height: inherit;*/
  overflow-y: scroll;

  .video-item {
    width: inherit;
    height: px2vw(149);
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: px2vw(21);

    .video-img {
      width: px2vw(131);
      height: px2vw(149);
      background: linear-gradient(
        180deg,
        rgba(0, 41, 97, 0) 0%,
        rgba(2, 94, 148, 0.41) 100%
      );
      border: 1px solid #00678c;
      padding: px2vw(5);
      margin-right: px2vw(22);
    }

    .video-info {
      height: px2vw(149);
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      font-size: px2vw(13);
      color: #c5d3db;
      position: relative;

      .info-content {
        width: inherit;
        font-size: px2vw(14);
        color: #ffb769;
        display: -webkit-box; /* 将容器以弹性盒子形式布局 */
        -webkit-line-clamp: 2; /* 限制文本显示为两行 */
        -webkit-box-orient: vertical; /* 将弹性盒子的主轴方向设置为垂直方向 */
        overflow: hidden; /* 隐藏容器中超出部分的内容 */
        text-overflow: ellipsis; /* 超出容器范围的文本显示省略号 */
        margin-bottom: px2vw(11);
      }

      .video-platform {
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
}

:deep(.el-image) {
  box-shadow: none !important;
}
</style>
