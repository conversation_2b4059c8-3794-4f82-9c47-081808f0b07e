<template>
  <div class="distribution-of-posts-platform w-full h-full flex flex-col">
    <item-title class="w-full" :title="title" />
    <div class="platform-container" style="flex-shrink: 0">
      <div
        class="num-box h-full"
        style="display: flex; justify-content: space-around; flex-wrap: wrap"
      >
        <div
          v-for="(item, index) in platformList"
          :key="index"
          class="platform-item"
          :class="{ active: activePlatformIndex === index }"
        >
          <div>{{ item.label }}</div>
          <div class="platform-item-count">
            {{ item.value }}
          </div>
        </div>
      </div>
      <div class="platform-logo">
        <img
          draggable="false"
          :src="platformList[activePlatformIndex].logo"
          class=""
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import ItemTitle from "@/views/eventManage/screen/component/ItemTitle.vue";
import weibo from "@/assets/screen/weibo.svg";
import zhihu from "@/assets/screen/zhihu.svg";
import weixin from "@/assets/screen/weixin.svg";
import douyin from "@/assets/screen/douyin.svg";
import qita from "@/assets/screen/qita.png";

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
  platformListRawData: {
    type: Array,
    default: () => [],
  },
});

const platformList = ref([
  { label: "微博", value: 0, logo: weibo, key: "weiBoNum" },
  { label: "知乎", value: 0, logo: zhihu, key: "zhiHuNum" },
  { label: "微信", value: 0, logo: weixin, key: "weiXinNum" },
  { label: "抖音", value: 0, logo: douyin, key: "douYinNum" },
  { label: "其他网站", value: 0, logo: qita, key: "otherNum" },
]);
const activePlatformIndex = ref(0);
const platformTimer = ref(null);

watch(
  () => props.platformListRawData,
  (newV) => {
    initPlatformList(newV);
  },
  {
    deep: true,
    immediate: true,
  },
);

function initPlatformList(newV) {
  platformList.value.forEach((item) => {
    item.value = newV?.[item.key];
  });
}

onMounted(() => {
  platformTimer.value = setInterval(() => {
    if (activePlatformIndex.value === 4) {
      activePlatformIndex.value = 0;
    } else {
      activePlatformIndex.value += 1;
    }
  }, 3000);
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.platform-container {
  margin-bottom: px2vw(60);
  height: px2vw(170);
  display: flex;
  justify-content: space-between;

  .num-box {
    width: px2vw(360);
  }
}

.distribution-of-posts-platform {
  gap: px2vw(30);
}

.platform-item {
  width: 33%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: px2vw(15);
  color: #feffff;
  position: relative;
  &.active {
    .platform-item-count {
      color: #02e8ff;
    }
    &:after {
      content: "";
      width: px2vw(8);
      height: px2vw(10);
      background: url(@/assets/screen/platform-active.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      bottom: px2vw(-2);
    }
  }
  .platform-item-count {
    margin-top: px2vw(8);
    font-size: px2vw(29);
    text-shadow: 0 0 11px rgba(87, 198, 255, 0.76);
    font-family: PangMenZhengDao, sans-serif;
  }
}
.platform-item:nth-child(4) {
  margin-top: px2vw(10);
  margin-left: px2vw(40);
}
.platform-item:nth-child(5) {
  margin-top: px2vw(10);
  margin-right: px2vw(40);
}
.platform-logo {
  width: px2vw(105);
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url(@/assets/screen/platform-back.svg) no-repeat;
  background-size: 100% 100%;
  img {
    margin-bottom: px2vw(50);
    width: px2vw(59);
    animation: logo-move 3s infinite ease;
  }
  @keyframes logo-move {
    0% {
      transform: translate(0px, px2vw(-10));
    }
    50% {
      transform: translate(0px, px2vw(10));
    }
    100% {
      transform: translate(0px, px2vw(-10));
    }
  }
}
</style>
