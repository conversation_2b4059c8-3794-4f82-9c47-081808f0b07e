<?xml version="1.0" encoding="UTF-8"?>
<svg width="59px" height="48px" viewBox="0 0 59 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63 (92445) - https://sketch.com -->
    <title>文档</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="27.4679886%" y1="26.9293219%" x2="70.2116379%" y2="71.1889543%" id="linearGradient-1">
            <stop stop-color="#3183FF" offset="0%"></stop>
            <stop stop-color="#2D7BF8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#0773FD" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#006FFE" stop-opacity="0.638139205" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="27.4679886%" y1="26.9293219%" x2="69.7321707%" y2="70.692482%" id="linearGradient-3">
            <stop stop-color="#4A92FF" offset="0%"></stop>
            <stop stop-color="#3180FF" offset="100%"></stop>
        </linearGradient>
        <filter x="-7.0%" y="-7.0%" width="113.9%" height="114.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.9152238%" id="linearGradient-5">
            <stop stop-color="#E6F1FF" offset="0%"></stop>
            <stop stop-color="#C9DAFF" offset="100%"></stop>
        </linearGradient>
        <path d="M0.0270144628,10.3320595 C0.0270144628,8.80887603 0.0270144628,8.04814876 0.340814463,7.46636529 C0.623963575,6.94752068 1.06390575,6.53148182 1.59774339,6.27772893 C2.21324091,5.98208264 3.01805579,5.98208264 4.62941446,5.98208264 L16.1717219,5.98208264 C17.7830806,5.98208264 18.5878955,5.98208264 19.203393,6.27772893 C19.7372306,6.53148182 20.1771728,6.94752068 20.4603219,7.46636529 C20.7741219,8.04814876 20.7741219,8.80887603 20.7741219,10.3320595 L20.7741219,13.8037421 C20.7741219,15.3260612 20.7741219,16.0876529 20.4603219,16.6694364 C20.1769815,17.1879228 19.737068,17.603632 19.203393,17.8572083 C18.5878955,18.153719 17.7830806,18.153719 16.1717219,18.153719 L4.62941446,18.153719 C3.01805579,18.153719 2.21324091,18.153719 1.59774339,17.8572083 C1.06406835,17.603632 0.62415488,17.1879228 0.340814463,16.6694364 C0.0270144628,16.0876529 0.0270144628,15.3260612 0.0270144628,13.8037421 L0.0270144628,10.3320595 Z M2.17347562,4.68106612 C2.17347562,3.56072231 3.1338938,2.65303636 4.31907231,2.65303636 L16.482064,2.65303636 C17.6663781,2.65303636 18.6276607,3.56072231 18.6276607,4.68106612 L2.17347562,4.68106612 Z M4.31993678,1.3528843 C4.31993678,0.605123967 4.95963926,-9.59746516e-16 5.75062273,-9.59746516e-16 L15.0505136,-9.59746516e-16 C15.8414971,-9.59746516e-16 16.482064,0.605123967 16.482064,1.3528843 L4.31907231,1.3528843 L4.31993678,1.3528843 Z" id="path-6"></path>
        <filter x="-28.9%" y="-33.1%" width="157.8%" height="166.1%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0392156863   0 0 0 0 0.439215686   0 0 0 0 0.984313725  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="统一工作平台" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-455.000000, -63.000000)">
            <g id="文档" transform="translate(451.000000, 60.000000)">
                <path d="M27.8045042,26.7901513 L51.3603894,17.1968494 C52.3833765,16.7802307 53.5504076,17.2717893 53.9670264,18.2947764 C54.1642282,18.7789958 54.1639744,19.3211856 53.9663193,19.8052202 L44.3249541,43.4158214 C43.2051834,46.158012 41.0268349,48.3327282 38.2827794,49.4479211 L14.7737053,59.0020838 C13.7504132,59.417953 12.5837426,58.9255394 12.1678734,57.9022474 C11.9714614,57.4189538 11.9716595,56.8780442 12.1684256,56.3948947 L21.7659059,32.8287496 C22.8834019,30.0847926 25.0605472,27.9076474 27.8045042,26.7901513 Z" id="矩形" fill="url(#linearGradient-1)" opacity="0.657366071" transform="translate(33.071522, 38.095768) rotate(-315.000000) translate(-33.071522, -38.095768) "></path>
                <polygon id="路径-5" fill="url(#linearGradient-2)" points="18.5977437 31.6331242 5 3 60.2873563 3 48.5617695 31.6331242"></polygon>
                <path d="M27.8418605,20.8865956 L51.3977457,11.2932937 C52.4207328,10.8766749 53.5877639,11.3682335 54.0043827,12.3912206 C54.2015845,12.8754401 54.2013307,13.4176298 54.0036757,13.9016644 L44.3623105,37.5122656 C43.2425397,40.2544562 41.0641912,42.4291724 38.3201357,43.5443653 L14.8110616,53.0985281 C13.7877696,53.5143972 12.6210989,53.0219837 12.2052297,51.9986916 C12.0088177,51.5153981 12.0090158,50.9744884 12.2057819,50.4913389 L21.8032622,26.9251938 C22.9207583,24.1812368 25.0979035,22.0040916 27.8418605,20.8865956 Z" id="矩形" fill="url(#linearGradient-3)" opacity="0.674339658" transform="translate(33.108879, 32.192212) rotate(-315.000000) translate(-33.108879, -32.192212) "></path>
                <path d="M33.2715554,26.2801911 L38.3808209,24.1994064 C39.403808,23.7827876 40.5708392,24.2743462 40.9874579,25.2973333 C41.1846597,25.7815528 41.184406,26.3237425 40.9867509,26.8077771 L38.8897641,31.9430581 C37.7699933,34.6852487 35.5916448,36.8599649 32.8475893,37.9751578 L27.7532737,40.0455039 C26.7299817,40.4613731 25.563311,39.9689595 25.1474418,38.9456675 C24.9510298,38.4623739 24.951228,37.9214643 25.147994,37.4383147 L27.2329572,32.3187894 C28.3504532,29.5748324 30.5275984,27.3976871 33.2715554,26.2801911 Z" id="矩形" fill="#026FFF" opacity="0.767694382" filter="url(#filter-4)" transform="translate(33.071522, 32.118756) rotate(-315.000000) translate(-33.071522, -32.118756) "></path>
                <g id="service-ticket" transform="translate(22.666667, 13.750000)" fill-rule="nonzero">
                    <g id="形状">
                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        <use fill="url(#linearGradient-5)" xlink:href="#path-6"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>