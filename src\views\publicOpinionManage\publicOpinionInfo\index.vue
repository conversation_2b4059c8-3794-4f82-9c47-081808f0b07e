<template>
  <div class="app-container">
    <div class="appTitle">舆情信息</div>
    <div class="flex justify-between flex-wrap">
      <div class="flex items-center mb-[10px]">
        <FilterSearch
          :search-condition="searchCondition"
          :msg-from-option="opinion_source_type"
          @search="refreshData"
        />
        <FilterCollapse
          :search-condition="searchCondition"
          :msg-from-option="opinion_source_type"
          :media-type-option="media_type"
          @search="refreshData"
          class="ml-[6px]"
        />
        <ControlColumn
          :columns="PUBLIC_OPINION_INFO_COLUMNS([...delColumns, 'selection'])"
          :hide-columns="hideColumns"
          @toggle="toggleColumn"
          class="ml-[6px]"
        />
      </div>
      <div class="flex gap-x-[8px] mb-[10px]">
        <!-- 删帖 -->
        <!-- <el-button class="batchBtn delPo" @click="showBatchDialog(TRANSFER_TYPE.DELPO)">
          批量{{ TRANSFER_TYPE.DELPO }}
        </el-button> -->
        <el-button class="batchBtn delPo" @click="delPoValidate">
          {{ TRANSFER_TYPE.DELPO }}校验
        </el-button>
        <div class="batchBtn importPoBtn" @click="showImport">导出</div>
        <el-button
          class="batchBtn invalidBtn"
          @click="showBatchDialog(TRANSFER_TYPE.INVALID)"
          >批量无效</el-button
        >
        <el-dropdown trigger="click">
          <el-button class="batchBtn dispatchBtn"
            >批量指派<el-icon class="el-icon--right"><arrow-down /></el-icon
          ></el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <div @click="showBatchDialog(TRANSFER_TYPE.VIEW)">
                  批量{{ TRANSFER_TYPE.VIEW }}
                </div>
              </el-dropdown-item>
              <el-dropdown-item>
                <div @click="showBatchDialog(TRANSFER_TYPE.CHECK)">
                  批量{{ TRANSFER_TYPE.CHECK }}
                </div>
              </el-dropdown-item>
              <el-dropdown-item>
                <div @click="showBatchDialog(TRANSFER_TYPE.RESOLVE)">
                  批量{{ TRANSFER_TYPE.RESOLVE }}
                </div>
              </el-dropdown-item>
              <el-dropdown-item>
                <div @click="showBatchDialog(TRANSFER_TYPE.REPORT)">
                  批量{{ TRANSFER_TYPE.REPORT }}
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button class="batchBtn reportBtn" @click="showAddDialog = true"
          >舆情上报</el-button
        >
      </div>
    </div>

    <!-- 舆情表格 -->
    <PublicOpinionTable
      ref="publicOpinionTableRef"
      :interface-info="interfaceInfo"
      :table-columns="tableColumns"
      :is-column-drag-sort="true"
      show-add-event
      :media-type-option="media_type"
      @mulSelect="(val) => (poInfoList = val)"
      @showDrawer="showDrawer"
      @showDialog="showDialog"
      @refreshData="refreshData"
      @updateColumns="updateColumns"
    />

    <!-- 舆情上报弹窗 -->
    <AddDialog
      v-model="showAddDialog"
      :po-type-option="opinion_type"
      :po-media-type-option="media_type"
      @refreshData="refreshData"
    />
    <!-- 单个操作弹窗 -->
    <HandleDialog
      v-model="showHandleDialog"
      :type-text="typeText"
      :po-info="poInfo"
      @refreshData="refreshProcess"
    />

    <!-- 批量操作弹窗 -->
    <BatchHandleDialog
      v-model="showBatchHandleDialog"
      :type-text="typeText"
      :po-info-list="poInfoList"
      @refreshData="refreshData"
    />

    <!-- 编辑/查看抽屉 -->
    <EditInfoDrawer
      v-model="showInfoDrawer"
      :drawer-type="drawerType"
      :po-info="poInfo"
      @showDialog="
        (callback) => showDialog(TRANSFER_TYPE.VIEW, poInfo, callback)
      "
      @refreshData="refreshData"
    />

    <!-- 赋分弹窗 -->
    <AddScore ref="addScoreRef" @refresh="refreshData" />

    <!-- 导出弹窗 -->
    <ImportPoDialog
      v-model="showImportDialog"
      :search-condition="searchCondition"
    />

    <!-- 鉴定弹窗 -->
    <JudgeDialog
      v-model="showJudgeDialog"
      :judge-type="judgeType"
      :judge-po-info="judgePoInfo"
      @refreshData="refreshData"
    />
  </div>
</template>

<script setup>
import FilterSearch from "./components/FilterSearch.vue";
import FilterCollapse from "./components/FilterCollapse.vue";
import ControlColumn from "./components/ControlColumn.vue";
import PublicOpinionTable from "../components/PublicOpinionTable.vue";
import AddDialog from "./components/AddDialog.vue";
import HandleDialog from "./components/HandleDialog.vue";
import BatchHandleDialog from "./components/BatchHandleDialog.vue";
import EditInfoDrawer from "../components/EditInfoDrawer/index.vue";
import AddScore from "./components/AddScore.vue";
import ImportPoDialog from "./components/ImportPoDialog.vue";
import JudgeDialog from "./components/JudgeDialog.vue";
import { PUBLIC_OPINION_INFO_COLUMNS } from "../config/tableColumns";
import { ARCTICLE_STATUS, INVALID_STATUS } from "../config/constant";
import { TRANSFER_TYPE } from "../config/mapRel";
import {
  getPoInfoListWithRumorAll,
  getUserShowFieldConfig,
  changeUserShowFieldConfig,
  batchInvalidPo,
  delPoJudge,
} from "@/api/poManage/poInfo";
import { usePoManageStore } from "@/store/modules/poManage.js";
import { COLUMN_FIELD_CONFIG } from "../config/columnRel";

const { proxy } = getCurrentInstance();
const { opinion_source_type, opinion_type, media_type } = proxy.useDict(
  "opinion_source_type",
  "opinion_type",
  "media_type"
); // 舆情来源 舆情类型 媒体类型
const poManageStore = usePoManageStore();

// 搜索条件
const searchCondition = ref({
  targetGroup: "all",
  happenLocation: "",
  articleStatus: "all",
  createTime: [],
  poId: "",
  poEvent: "",
  poContent: "",
  msgFrom: "all",
  mediaType: "all",
  handleType: "all",
});
const publicOpinionTableRef = ref();
const showAddDialog = ref(false); // 是否展示报送弹窗
const showHandleDialog = ref(false); // 是否展示处理弹窗
const typeText = ref("");
const poInfo = ref({});
const showBatchHandleDialog = ref(false);
const poInfoList = ref([]);
const showInfoDrawer = ref(false);
const drawerType = ref(""); // edit or view
const callbackFun = ref(null); // 获取舆情流程的回调
const addScoreRef = ref(null);
const showImportDialog = ref(false);
const showJudgeDialog = ref(false);
const judgeType = ref("");
const judgePoInfo = ref({});

const interfaceInfo = computed(() => ({
  api: getPoInfoListWithRumorAll,
  getDataMap,
}));
const tableColumns = computed(() =>
  PUBLIC_OPINION_INFO_COLUMNS(
    [...delColumns.value, ...hideColumns.value],
    selectable,
    currentSortColumns.value
  )
);

// 网安端多展示一个分值列
const delColumns = computed(() =>
  poManageStore.isWangAn
    ? ["handleUnit", "handler", "handleStatus2"]
    : ["handleUnit", "handler", "handleStatus2", "score"]
);

/**
 * 数据整理
 */
function getDataMap(data) {
  return data.map((ele) => {
    return {
      id: ele.id,
      poId: ele.reportNumber,
      taskId: ele.taskId,
      isFirst: ele.firstReportTag, // 0否 1是
      poName: ele.title,
      poContent: ele.content,
      poLink: ele.linkUrl,
      poEvent: ele.event?.length ? ele.event : "-",
      workUnit: ele.workUnit || "-", // 上报单位
      wechatDeptId: ele.wechatDeptId, // 上报单位id
      poMediaType: ele.mediaType, // 媒体类型
      poFrom:
        opinion_source_type.value.find((i) => i.value === ele.reportSource)
          ?.label || "-",
      platformType: ele.platformTag || "-",
      happenLocation: ele.involvedArea || "-",
      isSensitive: ele.sensitiveTag + "", // 0否 1是
      poType:
        opinion_type.value.find((i) => i.value === ele.categoryTag)?.label ||
        "-",
      poTypeId: ele.categoryTag,
      wechatNickname: ele.wechatNickname || "-", // 微信报送昵称
      netizenNickname: ele.netizenNickname || "-", // 网名昵称
      netizenAccountId: ele.netizenAccountId, // 网名id
      netizenAccount: ele.netizenAccount || "-", // 网名账号
      publicTime: ele.publishTime || "-",
      poImg: ele.photoUrl ? ele.photoUrl.split(",") : "-",
      articleStatus: ele.sourceStatus, // 0未删除 1已删除
      rumorStatus: ele.rumorOpinion?.rumorFlag || "0", // 0不是谣言 1是谣言
      handleStatus: ele.reportProcess,
      createTime: ele.createTime || "-",
      targetGroup: ele.targetGroup || "-",
      isInvalid: ele.disabled, // 1无效 0有效
      score:
        ele.baseScore === null && ele.scorePointValue === null
          ? "-"
          : ele.baseScore + ele.scorePointValue, // 赋分
      linkUrlCount: ele.linkUrlCount,
    };
  });
}

/**
 * 刷新表格数据
 */
function refreshData() {
  const params = {
    targetGroup:
      searchCondition.value.targetGroup === "all"
        ? ""
        : searchCondition.value.targetGroup,
    involvedArea: searchCondition.value.happenLocation,
    sourceStatus:
      searchCondition.value.articleStatus === "all"
        ? ""
        : searchCondition.value.articleStatus,
    startTime: searchCondition.value.createTime?.[0],
    endTime: searchCondition.value.createTime?.[1],
    reportNumber: searchCondition.value.poId,
    event: searchCondition.value.poEvent,
    content: searchCondition.value.poContent,
    reportSource:
      searchCondition.value.msgFrom === "all"
        ? ""
        : searchCondition.value.msgFrom,
    mediaType:
      searchCondition.value.mediaType === "all"
        ? ""
        : searchCondition.value.mediaType,
    reportProcess:
      searchCondition.value.handleType === "all"
        ? ""
        : searchCondition.value.handleType,
  };
  publicOpinionTableRef.value.getTableData(params);
}

/**
 * 刷新舆情流程数据和表格数据
 */
function refreshProcess() {
  if (callbackFun.value) {
    callbackFun.value();
  }
  refreshData();
}

/**
 * 删帖校验
 */
async function delPoValidate() {
  proxy.$modal.confirm(`是否开始进行删帖判定?`).then(async function () {
    await delPoJudge();
    proxy.$modal.msgSuccess("操作成功");
  });
}

/**
 * 展示编辑/查看抽屉
 */
function showDrawer(type, row) {
  drawerType.value = type;
  poInfo.value = row;
  showInfoDrawer.value = true;
}

/**
 * 展示谣言鉴定/删帖判定/赋分弹窗/处理弹窗
 */
function showDialog(text, row, callback) {
  if (text === "rumor" || text === "del") {
    showJudgeDialog.value = true;
    judgeType.value = text;
    judgePoInfo.value = {
      id: row.id,
      articleStatus: row.articleStatus,
      rumorStatus: row.rumorStatus,
      handleStatus: row.handleStatus,
      handleStatus: row.handleStatus,
    };
  } else if (text === TRANSFER_TYPE.SCORE) {
    addScoreRef.value.openDialog(row);
  } else {
    typeText.value = text;
    poInfo.value = row;
    showHandleDialog.value = true;
    callbackFun.value = callback;
  }
}

/**
 * 展示导出弹窗
 */
function showImport() {
  if (tableColumns.value.length <= 3) {
    proxy.$modal.msgWarning(`请至少显示3个有效列`);
    return;
  }

  showImportDialog.value = true;
}

/**
 * 展示批量处理弹窗
 */
function showBatchDialog(text) {
  if (!poInfoList.value.length) {
    proxy.$modal.msgWarning(`请选择舆情信息`);
    return;
  }

  if (text === TRANSFER_TYPE.INVALID) {
    proxy.$modal
      .confirm(`是否确认将所选舆情都标记为无效舆情?`)
      .then(async function () {
        await batchInvalidPo(poInfoList.value?.map((i) => i.id)?.join(","));
        refreshData();
        proxy.$modal.msgSuccess(`操作成功`);
      })
      .catch(() => {});
  } else {
    typeText.value = text;
    showBatchHandleDialog.value = true;
  }
}

/**
 * 判断当前项的复选框是否可勾选
 */
function selectable(row) {
  return (
    row.articleStatus !== ARCTICLE_STATUS.FINISH_DEL &&
    row.isInvalid !== INVALID_STATUS.YES
  );
  // return (
  //   row.articleStatus !== ARCTICLE_STATUS.FINISH_DEL &&
  //   row.isInvalid !== INVALID_STATUS.YES
  // );
}

const hideColumns = ref([]);
const currentSortColumns = ref([]);

/**
 * 设置排序列
 */

async function updateColumns(newColumns) {
  console.log("---->newColumns", newColumns);

  // 更新回数据库
  const params = {};

  newColumns.forEach((i, index) => {
    if (i.prop) {
      params[
        COLUMN_FIELD_CONFIG.find(
          (config) => config.columnProp === i.prop
        )?.responseField
      ] = {
        sort: index + 1,
      };

      // 前端更新
      currentSortColumns.value.push({
        prop: i.prop,
        sort: index + 1,
      });
    }
  });
  const res = await changeUserShowFieldConfig(params);
  // if (res.code) {
  //   proxy.$modal.msgSuccess("操作成功");
  // }
}

/**
 * 设置显隐列
 */
async function toggleColumn(list) {
  const params = {};
  COLUMN_FIELD_CONFIG.forEach((i) => {
    params[i.responseField] = {
      show: !list.includes(i.columnProp),
    };
  });

  const res = await changeUserShowFieldConfig(params);
  if (res.code === 200) {
    hideColumns.value = list;
  }
}

/**
 * 获取显隐列配置
 */
async function getTableColumnConfig() {
  hideColumns.value = [];
  const res = await getUserShowFieldConfig();
  if (res.code === 200) {
    // 获取当前排序列

    // 获取隐藏列
    COLUMN_FIELD_CONFIG.forEach((i) => {
      if (!res.data[i.responseField]?.show) {
        hideColumns.value.push(i.columnProp);
      } else {
        currentSortColumns.value.push({
          prop: i.columnProp,
          sort: res.data[i.responseField]?.sort,
        });
      }
    });
  }
}

getTableColumnConfig();
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: absolute; // 撑开高度
  padding: 10px 20px;

  .appTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
    margin: 12px 0 22px;
  }

  .batchBtn {
    width: 76px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #3370ff;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #0070ff;
    cursor: pointer;

    // &:not(:last-child) {
    //   margin-right: 6px;
    // }
  }

  .invalidBtn {
    width: 80px;
    height: 32px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #3370ff;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #0070ff;
    line-height: 32px;
    text-align: left;
    font-style: normal;
  }

  .dispatchBtn {
    width: 94px;
    height: 32px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #3370ff;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #0070ff;
    line-height: 32px;
    text-align: left;
    font-style: normal;
  }

  .reportBtn {
    width: 80px;
    height: 32px;
    background: #0070ff;
    border-radius: 6px;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 32px;
    text-align: left;
    font-style: normal;
  }

  .delPo {
    width: 80px;
    height: 32px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #f54b46;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #f54b46;
    line-height: 32px;
    text-align: right;
    font-style: normal;
  }

  .importPoBtn {
    width: 68px;
    border: 1px solid #cfd2d6;
    color: #1f2329;
  }

  .publicOpinionTable-container {
    height: calc(100% - 42px - 56px);
  }
}
</style>
