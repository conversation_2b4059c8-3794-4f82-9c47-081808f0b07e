<!--
 * @Author: 周杰 <EMAIL>
 * @Date: 2025-03-14 13:59:11
 * @LastEditors: 周杰 <EMAIL>
 * @LastEditTime: 2025-07-21 15:25:47
 * @FilePath: \patrol-intel-web\src\views\opinionScreen\detail\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="w-full h-full detail-container" ref="tableContainerRef">
    <div class="backBtn flex-center" @click="$router.back()">返回首页</div>

    <CommonTable
      :columns="PO_MANAGE_DETAIL_COLUMNS"
      :data="tableData"
      :loading="tableLoading"
      :show-default-btn="false"
      :show-operation-column="false"
      no-padding
      :max-height="tableHeight"
      :highlight-current-row="false"
      isStripe
      :headerStyle="{}"
    >
      <template #createTime="{ row }">
        <div class="whitespace-normal break-all">{{ row.createTime }}</div>
      </template>

      <template #poLink="{ row }">
        <div v-if="row.poLink" class="flex">
          <el-link
            class="linkText whitespace-normal break-all"
            :underline="false"
            :href="row.poLink"
            target="blank"
            >{{ row.poLink }}</el-link
          >
        </div>
        <span v-else>-</span>
      </template>

      <template #netizenNickname="{ row }">
        <NetizenColumn
          :nickname="row.netizenNickname"
          :account-id="row.netizenAccountId"
        />
      </template>

      <!-- 内容可能为 文本/图片/带换行聚合体 -->
      <template #poContent="{ row }">
        <ImagePreview
          v-if="contentIsImg(row.poContent)"
          :src="row.poContent"
          :width="100"
          :height="100"
        />
        <div v-else class="poContentWrapper whitespace-pre-wrap!">
          {{ poContentF(row.poContent) }}
        </div>
      </template>

      <template #poImg="{ row }">
        <ImagePreview
          v-if="Array.isArray(row.poImg)"
          :src="row.poImg[0]"
          :width="100"
          :height="100"
        />
        <span v-else>-</span>
      </template>

      <template #workUnit="{ row }">
        <div>
          <el-dropdown
            trigger="click"
            v-if="
              row.workUnit !== '-' &&
              row.linkUrlCount &&
              Number(row.linkUrlCount) > 0
            "
            popper-class="detailWorkUnitDropdown"
            @visible-change="(visible) => handleClickDropdown(visible, row)"
          >
            <template #default>
              <div class="workUnitWrapper">
                {{ row.workUnit }}<el-icon><CaretBottom /></el-icon>
              </div>
            </template>

            <template #dropdown>
              <el-scrollbar max-height="320px">
                <div class="workUnitDropdownWrapper flex flex-col">
                  <section
                    class="dropdownItemWrapper flex flex-col"
                    v-for="(item, index) in linkReportRecords"
                    :key="index"
                  >
                    <div class="subDeptName">
                      {{ item.deptName }}
                    </div>
                    <div class="createTime">{{ item.reporterTime }}</div>
                  </section>
                </div>
              </el-scrollbar>
            </template>
          </el-dropdown>
          <span v-else class="mr-[4px]">{{ row.workUnit }} </span>
        </div>
      </template>

      <template #isSensitive="{ row }">
        {{ isSensitiveF(row.isSensitive) }}
      </template>

      <template #articleStatus="{ row }">
        <span
          class="truncate"
          :style="articleStatusF(row.articleStatus).style"
          >{{ articleStatusF(row.articleStatus).text }}</span
        >
      </template>
    </CommonTable>

    <!-- 分页 -->
    <pagination
      v-model:page="pageObj.pageNum"
      v-model:limit="pageObj.pageSize"
      :total="pageObj.total"
      @pagination="refreshData"
    />
  </div>
</template>

<script setup>
import { CaretBottom } from "@element-plus/icons-vue";
import CommonTable from "@/components/commonTable/index.vue";
import NetizenColumn from "@/views/publicOpinionManage/components/NetizenColumn.vue";
import { PO_MANAGE_DETAIL_COLUMNS } from "./config/tableColumns";
import { useFetchTableData } from "@/hooks/useFetchTableData";
import { useTableHeight } from "@/hooks/useTableHeight";
import { getScreenOpinionAll } from "@/api/eventOpinion/index";
import { getLinkReportRecords } from "@/api/poManage/poInfo";
import {
  SENSITIVE_STATUS_LIST,
  ARCTICLE_STATUS_LIST,
} from "@/views/publicOpinionManage/config/constant.js";

console.log("history.state", history.state);

const { tableData, tableLoading, pageObj, getTableData } = useFetchTableData({
  api: getScreenOpinionAll,
  params:
    history.state.type === "sensitive"
      ? { sensitiveTag: 1 }
      : history.state.type === "category"
        ? { categoryTag: history.state.categoryId }
        : {},
  getDataMap,
});

const tableContainerRef = ref(null);
const { tableHeight } = useTableHeight(tableContainerRef, 102 + 39 + 47);

const linkReportRecords = ref([]);

const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 内容是否为图片
const poContentF = computed(() => (val) => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter((i) => !contentIsImg.value(i));
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});
const isSensitiveF = computed(
  () => (val) =>
    SENSITIVE_STATUS_LIST.find((i) => i.value === val)?.label || "-"
);
const articleStatusF = computed(() => (val) => {
  const obj = ARCTICLE_STATUS_LIST.find((i) => i.value === val);
  return { text: obj?.label || "-", style: { color: "#ffffff" } };
});

/**
 * 数据整理
 */
function getDataMap(data) {
  return data.map((ele) => {
    return {
      id: ele.id,
      createTime: ele.createTime || "-",
      platformType: ele.platformTag || "-",
      poLink: ele.linkUrl,
      netizenNickname: ele.netizenNickname || "-", // 网名昵称
      netizenAccountId: ele.netizenAccountId, // 网名id
      poContent: ele.content,
      poImg: ele.photoUrl ? ele.photoUrl.split(",") : "-",
      workUnit: ele.workUnit || "-", // 上报单位
      happenLocation: ele.involvedArea || "-",
      isSensitive: ele.sensitiveTag + "", // 0否 1是
      articleStatus: ele.sourceStatus, // 0未删除 1已删除
      linkUrlCount: ele.linkUrlCount,
    };
  });
}

/**
 * 报送单位下拉点击事件
 */
async function handleClickDropdown(visible, row) {
  if (visible) {
    const res = await getLinkReportRecords(row?.id);
    if (res?.code === 200) {
      linkReportRecords.value = res?.data;
    }
  }
}

/**
 * 刷新表格数据
 */
function refreshData() {
  const params = history.state.type === "sensitive" ? { sensitiveTag: 1 } : {};
  getTableData(params);
}
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.detail-container {
  padding: px2vw(95) px2vw(42) px2vw(7) px2vw(50);

  .backBtn {
    width: px2vw(96);
    height: px2vw(32);
    background: rgba(0, 112, 255, 0.7);
    border-radius: px2vw(6);
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: px2vw(14);
    color: #ffffff;
    cursor: pointer;
    margin-bottom: px2vw(10);
  }

  // 通用表格样式修改
  :deep(.table-container) {
    .el-loading-mask {
      --el-mask-color: rgba(0, 0, 0, 0.9);
    }
    .tableRefWrapper {
      background-color: transparent;
      .el-table__row:hover {
        cursor: default;
      }
      tr {
        background-color: transparent;
      }
      th.el-table__cell {
        background-color: unset;
      }
    }

    .el-table__inner-wrapper::before {
      background-color: transparent;
    }

    // 表头行
    .el-table__header-wrapper tr {
      height: px2vw(62);
      background: linear-gradient(
        90deg,
        rgba(0, 93, 181, 0.42) 0%,
        #005db5 47%,
        rgba(0, 93, 181, 0.42) 100%
      );
      box-shadow: 0 px2vw(4) px2vw(4) 0 rgba(0, 0, 0, 0.25);
      border-radius: 0;

      .cell {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 800;
        font-size: px2vw(14);
        color: #60a4ff;
      }
    }

    // 表格内容区
    .el-table__body-wrapper .el-table__cell {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: px2vw(14);
      color: #ffffff !important;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }

    // 表头行单元格
    .el-table th.el-table__cell.is-leaf {
      border-top: px2vw(1) solid rgba(20, 135, 244, 0.7);
      border-bottom: px2vw(1) solid rgba(20, 135, 244, 0.7);
    }
    // 表格内容区单元格的边框
    .el-table td.el-table__cell {
      border-bottom: none;
    }

    // 斑马纹
    .el-table--striped
      .el-table__body
      tr.el-table__row--striped
      td.el-table__cell {
      background: rgba(11, 43, 84, 0.7);
      border-radius: 0;
    }
  }

  .poContentWrapper {
    white-space: normal;
    word-break: break-all;

    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4; /* 控制几行打点 */
    line-clamp: 4;
    overflow: hidden;
  }

  // 舆情内容链接
  :deep(.linkText) {
    display: inline;
    color: #ffffff;

    &:visited {
      color: #ffffff;
    }
    .el-link__inner {
      display: inline;
    }
  }

  .workUnitWrapper {
    font-size: px2vw(14);
    color: #ffffff;
    margin-right: px2vw(4);
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  // 分页
  :deep(.pagination-container) {
    margin-top: 0;
    background: transparent;

    .el-pagination__total,
    .el-pagination__jump {
      --el-text-color-regular: #ffffff;
    }

    // 筛选
    .el-select__wrapper {
      --el-fill-color-blank: rgba(30, 35, 103, 0.59);
      --el-border-color: #0070ff;
      &:hover {
        --el-text-color: #0070ff;
      }
      &.is-hovering:not(.is-focused) {
        --el-border-color-hover: #0070ff;
      }

      .el-select__placeholder {
        --el-input-text-color: #0070ff;
        &.is-transparent {
          --el-text-color-placeholder: #0070ff;
        }
      }

      .el-select__prefix,
      .el-select__suffix {
        --el-input-icon-color: #0070ff;
      }
    }

    // 页码
    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
      --el-pagination-button-bg-color: transparent;
      --el-pagination-button-color: #ffffff;
      border-radius: px2vw(4);
      border: px2vw(1) solid #1e2696;
    }

    .el-pagination.is-background .btn-next.is-disabled,
    .el-pagination.is-background .btn-next:disabled,
    .el-pagination.is-background .btn-prev.is-disabled,
    .el-pagination.is-background .btn-prev:disabled,
    .el-pagination.is-background .el-pager li.is-disabled,
    .el-pagination.is-background .el-pager li:disabled {
      --el-disabled-bg-color: transparent;
      --el-text-color-placeholder: #ffffff;
    }
    .el-pagination .btn-next .el-icon,
    .el-pagination .btn-prev .el-icon {
      font-size: 14px;
    }

    .el-pagination__jump {
      .el-input__wrapper {
        --el-input-bg-color: rgba(30, 35, 103, 0.59);
        --el-input-border-radius: 5px;
        --el-input-border-color: #0070ff;
        --el-input-hover-border-color: #0070ff;
        --el-input-text-color: #0070ff;
      }
    }
  }
}
</style>
