<template>
  <div class="handling-of-sensitive-posts w-full h-full flex flex-col">
    <item-title class="w-full" :title="title" />
    <el-carousel
      trigger="click"
      class="carousel"
      arrow="never"
      style="flex-shrink: 0"
      @change="handleCarouselChange"
    >
      <el-carousel-item v-for="item in sensitiveList" :key="item">
        <div class="info-item">
          <div class="info-item-title">
            {{ parseTime(item.publishTime) }}
          </div>
          <el-tooltip :content="item.content" placement="top">
            <div class="text-ellipsis-2 item-content">{{ item.content }}</div>
          </el-tooltip>
        </div>
      </el-carousel-item>
    </el-carousel>
    <!--        <div class="h-[300px]">-->
    <large-screen-table
      class="flex-1"
      :table-data="activeList"
      :columns="handingColumns"
      :row-height="60"
      content-color="#D2E4ED"
      :use-seamless-scroll="true"
      :header-font-size="12"
      header-background="transparent"
      header-color="#759ABF"
      intervalOpacity
      odd-row-background="linear-gradient(270deg, rgba(40,194,255,0) 0%, rgba(40,194,255,0.22) 100%)"
    >
      <template #commandCategorySlot="{ row }">
        <span class="table-font">{{
          parseCommandCategory(row.commandCategory) || ""
        }}</span>
      </template>

      <template #processStepSlot="{ row }">
        <span
          class="table-font"
          :style="{ color: parseStatus(row.processStep).color }"
          >{{ parseStatus(row.processStep).label || "" }}</span
        >
      </template>

      <template #deadlineTimeSlot="{ row }">
        <span class="table-font">{{
          row.deadlineTime
            ? dayjs(row.deadlineTime).format("YYYY/MM/DD HH:mm")
            : ""
        }}</span>
      </template>
    </large-screen-table>
  </div>
</template>

<script setup>
import ItemTitle from "@/views/eventManage/screen/component/ItemTitle.vue";
import { parseTime } from "@/utils/ruoyi.js";
import { singleOpinionSensitive } from "@/api/eventManagement/index.js";
import { useRoute } from "vue-router";
import LargeScreenTable from "@/views/eventManage/screen/component/LargeScreenTable.vue";
import {
  handingColumns,
  HANDLE2_STATUS,
} from "@/views/eventManage/screen/content.js";
import dayjs from "dayjs";
import { PO_PROCESS_LIST } from "@/views/publicOpinionManage/config/mapRel.js";

defineProps({
  title: {
    type: String,
    default: "",
  },
});

const route = useRoute();

const sensitiveList = ref([]);
const activeList = ref([]);

function parseStatus(processStep) {
  const status = processStep;
  const statusList = [
    {
      label: "待处理",
      list: HANDLE2_STATUS.WAIT_HANDLE,
      color: "#FFB769",
    },
    {
      label: "跟进中",
      list: HANDLE2_STATUS.ING,
      color: "#02E8FF",
    },
    {
      label: "已处理",
      list: HANDLE2_STATUS.FINISH_HANDLE,
      color: "#D2E4ED",
    },
    {
      label: "待查看",
      list: HANDLE2_STATUS.WAIT_VIEW,
      color: "#D2E4ED",
    },
    {
      label: "已查看",
      list: HANDLE2_STATUS.FINISH_VIEW,
      color: "#D2E4ED",
    },
  ];
  for (const item of statusList) {
    if (item.list.includes(status)) {
      return {
        label: item.label,
        color: item.color,
      };
    }
  }
}

function parseCommandCategory(commandCategory) {
  return PO_PROCESS_LIST.find((item) => item.categoriesCode === commandCategory)
    .fontLabel;
}

async function getSensitive() {
  const res = await singleOpinionSensitive(route.query.eventId);
  sensitiveList.value = res.data;
  if (sensitiveList.value.length) {
    activeList.value = sensitiveList.value[0].stepInfos;
  }
}

function handleCarouselChange(cur, prev) {
  activeList.value = sensitiveList.value[cur].stepInfos;
}
function handleRowStyle(data) {
  let res = {
    backgroundColor: "transparent !important",
    color: "#D2E4ED !important",
  };
  if (data.rowIndex % 2 === 0) {
    res.backgroundImage =
      "linear-gradient(270deg, rgba(40,194,255,0.00) 1%, rgba(40,194,255,0.22) 100%";
  }
  return res;
}

onMounted(() => {
  getSensitive();
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.handling-of-sensitive-posts {
  gap: px2vw(12);
}

.carousel {
  //height="94px"
  height: px2vw(94);
}

.item-content {
  // h-[36px]
  height: px2vw(36);
  font-size: px2vw(14);
}

.table-font {
  font-size: px2vw(14) !important;
}

:deep(.el-carousel__indicator.is-active button) {
  opacity: 1;
  background-color: #1991e8 !important;
}
:deep(.el-carousel__button) {
  background-color: #093f6c !important;
  border: none;
  cursor: pointer;
  display: block;
  height: var(--el-carousel-indicator-height);
  margin: 0;
  opacity: 0.48;
  outline: none;
  padding: 0;
  transition: var(--el-transition-duration);
  width: var(--el-carousel-indicator-width);
}

:deep(.el-carousel__indicator--horizontal) {
  padding: 0 !important;
}

:deep(.el-carousel__indicators--horizontal) {
  bottom: px2vw(-10) !important;
}
:deep(.el-carousel--horizontal, .el-carousel--vertical) {
  overflow: visible !important;
}

:deep(.el-carousel__button) {
  width: px2vw(30) !important;
}

.table {
  height: px2vw(40) !important;
}

:deep(.el-table--default) {
  font-size: px2vw(14) !important;
}

:deep(.el-table--default .cell) {
  padding: 0 px2vw(12) !important;
}

:deep(
    .el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th
  ) {
  font-size: px2vw(13) !important;
  height: px2vw(40) !important;
}
:deep(.el-table--default .el-table__cell) {
  padding: px2vw(8) 0 !important;
}

:deep(.el-table__header-wrapper) {
  table {
    colgroup {
      col {
        width: px2vw(100) !important;
      }
    }
  }
}

:deep(.el-table__body) {
  colgroup {
    col {
      width: px2vw(100) !important;
    }
  }
}
:deep(.el-table__body) {
  width: 100% !important;
}

:deep(.el-table__empty-block) {
  width: 100% !important;
}

.info-item {
  // h-[94px]
  height: px2vw(88);
  margin-bottom: 12px;
  padding: px2vw(16);
  background: url(@/assets/screen/info-back.png) no-repeat;
  background-size: 100% 100%;
  color: #a3c1d1;
  font-size: px2vw(13);
  position: relative;
  .info-item-title {
    margin-bottom: px2vw(8);
    font-size: px2vw(14);
    color: #feffff;
  }
  .info-item-tag {
    width: px2vw(62);
    height: px2vw(25);
    line-height: px2vw(25);
    text-align: center;
    font-size: px2vw(15);
    color: #feffff;
    position: absolute;
    right: 0;
    top: 0;
    background: url(@/assets/screen/info-tag.png) no-repeat;
    background-size: 100% 100%;
  }
}
</style>
