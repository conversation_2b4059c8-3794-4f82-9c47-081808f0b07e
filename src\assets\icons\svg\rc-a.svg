<?xml version="1.0" encoding="UTF-8"?>
<svg width="59px" height="48px" viewBox="0 0 59 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63 (92445) - https://sketch.com -->
    <title>日程（选中）</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="27.4679886%" y1="26.9293219%" x2="69.9544178%" y2="70.9226115%" id="linearGradient-1">
            <stop stop-color="#FF8D31" offset="0%"></stop>
            <stop stop-color="#F8902D" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FD7807" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FE5A00" stop-opacity="0.419143357" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="27.4679886%" y1="26.9293219%" x2="69.4376779%" y2="70.3875444%" id="linearGradient-3">
            <stop stop-color="#FFA94A" offset="0%"></stop>
            <stop stop-color="#FFA531" offset="100%"></stop>
        </linearGradient>
        <filter x="-7.0%" y="-7.0%" width="113.9%" height="114.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <path d="M15.6842287,0.780836777 C16.3685262,0.780836777 16.9238981,1.21885331 16.9238981,1.75769628 L16.9238981,4.4543905 C16.9238981,4.99323347 16.3685262,5.43125 15.6842287,5.43125 C14.9999311,5.43125 14.4445592,4.99323347 14.4445592,4.4543905 L14.4445592,1.75769628 C14.4445592,1.21802686 14.9999311,0.780836777 15.6842287,0.780836777 Z M6.6619146,1.75769628 C6.6619146,1.21802686 6.10571625,0.780836777 5.42224518,0.780836777 C4.73794766,0.780836777 4.18257576,1.21885331 4.18257576,1.75769628 L4.18257576,4.4543905 C4.18257576,4.99323347 4.73794766,5.43125 5.42224518,5.43125 C6.1065427,5.43125 6.6619146,4.99323347 6.6619146,4.4543905 L6.6619146,1.75769628 Z M11.8495179,1.75769628 C11.8495179,1.21802686 11.294146,0.780836777 10.6098485,0.780836777 C9.92637741,0.780836777 9.37017906,1.21885331 9.37017906,1.75769628 L9.37017906,4.4543905 C9.37017906,4.99323347 9.92637741,5.43125 10.6098485,5.43125 C11.294146,5.43125 11.8495179,4.99323347 11.8495179,4.4543905 L11.8495179,1.75769628 Z M17.7916667,3.50232438 L19.501584,3.50232438 C20.0685262,3.50232438 20.5272039,3.8990186 20.5272039,4.38744835 L20.5272039,19.7304236 C20.5272039,20.2188533 20.0685262,20.6155475 19.501584,20.6155475 L1.7172865,20.6155475 C1.15034435,20.6155475 0.691666667,20.2188533 0.691666667,19.7304236 L0.691666667,4.38744835 C0.691666667,3.8990186 1.15034435,3.50315083 1.7172865,3.50315083 L3.4280303,3.50315083 L3.4280303,4.38744835 C3.4280303,5.36596074 4.34621212,6.15769628 5.47926997,6.15769628 C6.61315427,6.15769628 7.53133609,5.36596074 7.53133609,4.38744835 L7.53133609,3.50315083 L8.55695592,3.50315083 L8.55695592,4.38744835 C8.55695592,5.36596074 9.47596419,6.15769628 10.609022,6.15769628 C11.7420799,6.15769628 12.6610882,5.36596074 12.6610882,4.38744835 L12.6610882,3.50315083 L13.686708,3.50315083 L13.686708,4.38744835 C13.686708,5.36596074 14.6057163,6.15769628 15.7387741,6.15769628 C16.871832,6.15769628 17.7908402,5.36596074 17.7908402,4.38744835 L17.7908402,3.50315083 L17.7916667,3.50232438 Z M5.4834022,18.5370351 C5.95530303,18.5370351 6.3387741,18.2014979 6.3387741,17.7866219 L6.3387741,15.7866219 C6.3387741,15.3717459 5.95530303,15.0362087 5.4834022,15.0362087 C5.01067493,15.0362087 4.6280303,15.3717459 4.6280303,15.7857955 L4.6280303,17.7857955 C4.6280303,18.2014979 5.01067493,18.5370351 5.4834022,18.5370351 L5.4834022,18.5370351 Z M8.90571625,18.5370351 C9.37844353,18.5370351 9.76108815,18.2014979 9.76108815,17.7866219 L9.76108815,13.7857955 C9.76108815,13.3709194 9.37844353,13.0353822 8.90571625,13.0353822 C8.43298898,13.0353822 8.05034435,13.3709194 8.05034435,13.784969 L8.05034435,17.7866219 C8.05034435,18.2014979 8.43298898,18.5370351 8.90571625,18.5370351 L8.90571625,18.5370351 Z M12.3280303,18.5370351 C12.8007576,18.5370351 13.1834022,18.2014979 13.1834022,17.7866219 L13.1834022,11.784969 C13.1834022,11.370093 12.8007576,11.0345558 12.3280303,11.0345558 C11.855303,11.0345558 11.4726584,11.370093 11.4726584,11.784969 L11.4726584,17.7866219 C11.4726584,18.2014979 11.855303,18.5370351 12.3280303,18.5370351 Z M15.7503444,18.5370351 C16.2238981,18.5370351 16.6065427,18.2014979 16.6065427,17.7866219 L16.6065427,9.78331612 C16.6065427,9.37009298 16.2230716,9.03372934 15.7503444,9.03372934 C15.27927,9.03372934 14.8949725,9.36926653 14.8949725,9.78331612 L14.8949725,17.7866219 C14.8949725,18.2014979 15.2784435,18.5370351 15.7503444,18.5370351 Z" id="path-5"></path>
        <filter x="-30.2%" y="-30.3%" width="160.5%" height="160.5%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.976470588   0 0 0 0 0.533333333   0 0 0 0 0.0431372549  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="统一工作平台" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-251.000000, -162.000000)">
            <g id="日程（选中）" transform="translate(247.666667, 159.000000)">
                <path d="M27.1378375,27.0401513 L50.6937227,17.4468494 C51.7167098,17.0302307 52.8837409,17.5217893 53.3003597,18.5447764 C53.4975615,19.0289958 53.4973077,19.5711856 53.2996527,20.0552202 L43.6582875,43.6658214 C42.5385167,46.408012 40.3601682,48.5827282 37.6161127,49.6979211 L14.1070386,59.2520838 C13.0837466,59.667953 11.9170759,59.1755394 11.5012068,58.1522474 C11.3047947,57.6689538 11.3049929,57.1280442 11.5017589,56.6448947 L21.0992393,33.0787496 C22.2167353,30.3347926 24.3938805,28.1576474 27.1378375,27.0401513 Z" id="矩形" fill="url(#linearGradient-1)" opacity="0.657366071" transform="translate(32.404856, 38.345768) rotate(-315.000000) translate(-32.404856, -38.345768) "></path>
                <polygon id="路径-5" fill="url(#linearGradient-2)" points="17.931077 31.8831242 4.33333333 3.25 59.6206897 3.25 47.8951028 31.8831242"></polygon>
                <path d="M27.1751938,21.1365956 L50.731079,11.5432937 C51.7540661,11.1266749 52.9210972,11.6182335 53.337716,12.6412206 C53.5349178,13.1254401 53.534664,13.6676298 53.337009,14.1516644 L43.6956438,37.7622656 C42.575873,40.5044562 40.3975245,42.6791724 37.653469,43.7943653 L14.144395,53.3485281 C13.1211029,53.7643972 11.9544322,53.2719837 11.5385631,52.2486916 C11.342151,51.7653981 11.3423492,51.2244884 11.5391153,50.7413389 L21.1365956,27.1751938 C22.2540916,24.4312368 24.4312368,22.2540916 27.1751938,21.1365956 Z" id="矩形" fill="url(#linearGradient-3)" opacity="0.674339658" transform="translate(32.442212, 32.442212) rotate(-315.000000) translate(-32.442212, -32.442212) "></path>
                <path d="M32.6048888,26.5301911 L37.7141543,24.4494064 C38.7371414,24.0327876 39.9041725,24.5243462 40.3207913,25.5473333 C40.5179931,26.0315528 40.5177393,26.5737425 40.3200842,27.0577771 L38.2230974,32.1930581 C37.1033267,34.9352487 34.9249782,37.1099649 32.1809227,38.2251578 L27.0866071,40.2955039 C26.063315,40.7113731 24.8966443,40.2189595 24.4807752,39.1956675 C24.2843631,38.7123739 24.2845613,38.1714643 24.4813274,37.6883147 L26.5662905,32.5687894 C27.6837865,29.8248324 29.8609318,27.6476871 32.6048888,26.5301911 Z" id="矩形" fill="#FF8F34" opacity="0.767694382" filter="url(#filter-4)" transform="translate(32.404856, 32.368756) rotate(-315.000000) translate(-32.404856, -32.368756) "></path>
                <g id="编组-24" transform="translate(22.000000, 12.000000)">
                    <g id="tracking" transform="translate(0.666667, 0.750000)"></g>
                    <g id="形状" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use fill="#FFF8F3" xlink:href="#path-5"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>