<template>
  <div class="app-container">
    <HeaderInfo />
    <div class="contentWrapper flex justify-between">
      <LeftInfo />
      <RightInfo />
    </div>
  </div>
</template>

<script setup>
import HeaderInfo from "./components/HeaderInfo";
import LeftInfo from "./components/LeftInfo";
import RightInfo from "./components/RightInfo.vue";

const arrowHeight = ref("");
const leftArrowTop = ref("");
const rightArrowTop = ref("");

onMounted(() => {
  setArrowTop();
  window.addEventListener("resize", setArrowTop);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", setArrowTop);
});

function setArrowTop() {
  const heightScale = document.body.getBoundingClientRect().height / 1080;
  arrowHeight.value = 34 * heightScale + "px";
  leftArrowTop.value = 485 * heightScale + "px";
  rightArrowTop.value = 465 * heightScale + "px";
}
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.app-container {
  user-select: none;
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: absolute; // 撑开高度
  z-index: 10;
  padding: 0;
  background:
    url("@/assets/images/home/<USER>") no-repeat top/100% px2vw(119),
    url("@/assets/images/home/<USER>") no-repeat left center/px2vw(52) 100%,
    url("@/assets/images/home/<USER>") no-repeat right center/px2vw(52) 100%,
    url("@/assets/images/home/<USER>") no-repeat bottom/100% px2vw(51),
    url("@/assets/images/home/<USER>") no-repeat center/cover;

  &::before,
  &::after {
    content: "";
    display: inline-block;
    width: px2vw(10);
    height: v-bind(arrowHeight);
    background: url("@/assets/images/home/<USER>") no-repeat left/cover;
    position: absolute;
    animation: breathe-arrow 2s infinite ease-in-out;
  }
  &::before {
    top: v-bind(leftArrowTop);
    left: px2vw(18);
  }
  &::after {
    transform: rotate(180deg);
    top: v-bind(rightArrowTop);
    right: px2vw(18);
  }

  @keyframes breathe-arrow {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }

  .headerInfo-container {
    height: px2vw(119);
  }

  .contentWrapper {
    width: 100%;
    height: calc(100% - px2vw(119));
    padding: 0 px2vw(78) px2vw(68) px2vw(92);
    overflow: hidden;

    .leftInfo-container {
      width: px2vw(960);
    }

    .rightInfo-container {
      width: px2vw(703);
    }
  }
}
</style>
