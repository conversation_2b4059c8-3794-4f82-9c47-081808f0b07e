<template>
  <div>
    <el-dialog
      v-model="feedbackVisible"
      title="反馈审核"
      :width="'700'"
      @close="onCancel"
    >
      <el-form
        ref="feedbackFormRef"
        label-position="top"
        :model="feedbackForm"
        :rules="{
          handler: [
            {
              required: true,
              message: '请选择新处置单位',
              trigger: 'blur',
            },
          ],
        }"
        status-icon
        class="custom-form !block"
      >
        <el-row>
          <el-col :span="24" v-if="!isAgree">
            <el-form-item label="原处置单位" prop="originHandleUnit">
              <el-input v-model="feedbackForm.originHandleUnit" disabled />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="新处置单位" prop="newHandleUnit">
              <el-input v-model="feedbackForm.newHandleUnit" disabled />
            </el-form-item>
          </el-col>

          <el-col :span="24" v-if="isAgree">
            <el-form-item label="处理人" prop="handler">
              <!-- 树形处理人 换成 根据 新处置单位查询的下拉框处理人 -->
              <!-- <el-tree-select
                v-model="feedbackForm.handler"
                lazy
                :load="loadTreeData"
                :props="{
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  isLeaf: 'isLeaf',
                }"
                node-key="value"
                multiple
                :render-after-expand="false"
                show-checkbox
                searchable
                check-strictly
              >
              </el-tree-select> -->
              <el-select
                clearable
                multiple
                v-model="feedbackForm.handler"
                placeholder="请选择处理人"
              >
                <el-option
                  v-for="item in handlerOptions"
                  :key="item.value"
                  :label="item?.label"
                  :value="item?.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <section v-if="isAgree">
          <el-button @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onHandle" :loading="submitLoading">
            提交
          </el-button>
        </section>

        <section v-else>
          <el-button @click="onRefuse" :loading="submitLoading">拒绝</el-button>
          <el-button type="primary" @click="onAgree"> 同意变更 </el-button>
        </section>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { FEEDBACK_RULES } from "@/views/publicOpinionManage/config/formConfig.js";
// import { HANDLE_RESULT_CODE } from "../config/constant.js";
import {
  getDeptUserList,
  createProcess,
  getHandlerList,
  feedbackAudit,
} from "@/api/poManage/poInfo.js";
import { PO_TRANSFER_LIST, TRANSFER_TYPE } from "../../../config/mapRel";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
});

const handlerOptions = ref([]);

const isAgree = ref(false);

const feedbackVisible = ref(null);

const feedbackFormRef = ref(null);
const feedbackForm = ref({
  originHandleUnit: "",
  newHandleUnit: "",
  // auditUnit: "",
  handler: [],
});

const { proxy } = getCurrentInstance();

const feedbackId = ref("");

const emit = defineEmits(["refreshData"]);

const opinionId = ref("");

const submitLoading = ref(false);

/**
 * 加载部门人员树结构数据
 */
// async function loadTreeData(node, resolve) {
//   try {
//     const parentId =
//       JSON.stringify(node.data) !== "{}" ? node.data.value : null; // Root node has deptId as '0'
//     const isDept = JSON.stringify(node.data) !== "{}" ? node.data.isDept : null;

//     const response = await getDeptUserList({
//       deptId: parentId,
//       isDept: isDept,
//     });

//     const { data } = response;

//     const nodes = data.map((user) => ({
//       value: user.id,
//       label: user.name,
//       isDept: user.isDept,
//       isLeaf: !user.isDept,
//       // disabled: user.isDept,
//       // deptId: parentId,
//       children: [],
//     }));

//     resolve(nodes);
//   } catch (error) {
//     console.error("Failed to load data:", error);
//     resolve([]);
//   }
// }

const onHandle = () => {
  feedbackFormRef.value.validate(async (val) => {
    if (val) {
      submitLoading.value = true;

      await onAudit("1", {
        userInCharges: feedbackForm.value.handler,
      });
      emit("refreshData");
      onCancel();
      isAgree.value = false;
    }
  });
};

/**
 * 取消
 */
const onCancel = () => {
  feedbackFormRef.value.resetFields(); // 重置表单
  feedbackVisible.value = false;
};

/**
 * 拒绝操作
 */
const onRefuse = async () => {
  submitLoading.value = true;
  await onAudit("0");
  emit("refreshData");
  onCancel();
};

/**
 * 同意操作
 */
const onAgree = async () => {
  // onAudit("1");
  isAgree.value = true;
};

/**
 * 审核操作(同意、拒绝变更)
 */
const onAudit = async (auditStatus, otherParams) => {
  const params = {
    feedBackId: feedbackId.value,
    isAgree: auditStatus,
    ...otherParams,
  };

  const res = await feedbackAudit(params);
  if (res.code === 200) {
    proxy.$message.success("处理成功");
    submitLoading.value = false;

    // isAgree.value = auditStatus === "1" ? true : false;
  }
};

/**
 * 打开弹框
 */
const openDialog = async (detailInfo) => {
  console.log("detailInfo", detailInfo);

  // 弹框数据回显准备
  feedbackId.value = detailInfo?.feedBackId;
  const workUnit = detailInfo?.workUnit === "-" ? "" : detailInfo?.workUnit;
  opinionId.value = detailInfo?.id;

  feedbackForm.value.newHandleUnit = detailInfo?.newWorkUnit;
  feedbackForm.value.originHandleUnit = workUnit;

  // 处理人下拉框数据
  const res = await getHandlerList({ deptId: detailInfo?.newWorkUnitId });
  handlerOptions.value = res.data?.map((item) => {
    return {
      label: item.nickName,
      value: item.userId,
    };
  });

  feedbackVisible.value = true;
};

defineExpose({
  openDialog,
  feedbackFormRef,
});
</script>

<style lang="scss" scoped>
:deep(.custom-form) {
  .el-form-item--default {
    margin-bottom: 12px;
  }

  .el-form-item__label,
  .el-radio__label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
  }
}

:deep(.el-dialog) {
  padding: 24px;
  .el-dialog__header {
    padding-left: 0;
  }
  .el-dialog__title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
  }
}
</style>
