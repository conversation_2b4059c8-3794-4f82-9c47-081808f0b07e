<template>
  <div class="app-container">
    <div class="appTitle">本地疑似水军</div>
    <div class="flex justify-between flex-wrap">
      <div class="flex items-center mb-[10px]">
        <FilterSearch
          :search-condition="searchCondition"
          :platform-list="water_army_platform"
          :tag-type-list="water_army_tags"
          @search="refreshData"
        />
      </div>
    </div>

    <!-- 舆情表格 -->
    <WaterArmyTable
      ref="WaterArmyTableRef"
      :interface-info="interfaceInfo"
      :table-columns="WATER_ARMY_TABLE_COLUMNS"
      :tag-type-list="water_army_tags"
      @removeWaterArmy="removeWaterArmy"
      @refreshData="refreshData"
    />
  </div>
</template>

<script setup>
import WaterArmyTable from "./components/WaterArmyTable.vue";
import FilterSearch from "./components/FilterSearch.vue";
import { WATER_ARMY_TABLE_COLUMNS } from "./config/tableColumns";
import { getWaterArmyList, delWaterArmy } from "@/api/networkuser/user";

const { proxy } = getCurrentInstance();

const { water_army_tags, water_army_platform } = proxy.useDict(
  "water_army_tags",
  "water_army_platform"
);

// 搜索条件
const searchCondition = ref({
  accountName: "",
  platformList: [],
  tagType: "all",
});
const WaterArmyTableRef = ref(); // 水军表格ref

/**
 * 获取表格数据
 * @param data 表格数据
 */
const getDataMap = (data) => {
  return data.map((item) => ({
    id: item.id,
    accountName: item.accountName,
    accountId: item.accountId,
    platformName: item.platform,
    homePageLink: item.linkUrl,
    ipAddress: item.ipAddress,
    judgeLabel: item.tagsList,
    eventInfoList: item.eventList?.map((el) => ({
      eventName: el.name,
      id: el.id,
    })),
  }));
};

const interfaceInfo = computed(() => ({
  api: getWaterArmyList,
  params: {
    accountName: searchCondition.value.accountName,
    platformList: searchCondition.value.platformList,
    tags:
      searchCondition.value.tagType === "all"
        ? ""
        : searchCondition.value.tagType,
  },
  getDataMap,
}));

/**
 * 移除水军
 * @param row 水军信息
 */
const removeWaterArmy = (row) => {
  // 二次确认
  proxy
    .$confirm("确定移除该水军吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(async () => {
      const res = await delWaterArmy([row.id]);
      if (res.code === 200) {
        proxy.$message.success("移除水军成功");
        refreshData();
      }
    });
};

/**
 * 刷新表格数据
 */
function refreshData() {
  const params = {
    accountName: searchCondition.value.accountName,
    platformList: searchCondition.value.platformList,
    tags:
      searchCondition.value.tagType === "all"
        ? ""
        : searchCondition.value.tagType,
  };
  WaterArmyTableRef.value.getTableData(params);
}
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: absolute; // 撑开高度
  padding: 10px 20px;

  .appTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
    margin: 12px 0 20px;
  }
}

.WaterArmyTable-container {
  height: calc(100% - 42px - 54px);
}
</style>
