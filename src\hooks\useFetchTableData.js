/**
 * 请求表格数据
 * @param {Function} api 请求接口
 * @param {Object} params 接口参数
 * @param {Function} getDataMap 数据结构转换
 */
export function useFetchTableData({
  api,
  params = {},
  getDataMap = (data) => data,
  isPage = true,
}) {
  const tableData = ref([]); // 表格数据
  const tableLoading = ref(false); // 表格加载状态
  const pageObj = ref({ pageNum: 1, pageSize: 15, total: 0 }); // 分页数据

  onMounted(() => {
    pageObj.value.pageNum = params.pageNum || 1;
    pageObj.value.pageSize = params.pageSize || 15;
    getTableData();
  });

  /**
   * 获取数据
   */
  async function getTableData(p = params) {
    tableLoading.value = true;
    const newParams = { ...unref(p) };
    if (isPage) {
      newParams.pageNum = pageObj.value.pageNum;
      newParams.pageSize = pageObj.value.pageSize;
    }

    const res = await api(newParams);

    if (res?.code === 200) {
      tableData.value = getDataMap(
        res?.data?.records || res?.rows || res?.data
      );
      if (isPage) {
        console.log(res);
        pageObj.value.total = Number(res?.total);
      }
    }
    tableLoading.value = false;
  }

  return {
    tableData,
    tableLoading,
    pageObj,
    getTableData,
  };
}
