<template>
  <div class="app-container">
    <!-- 标题 -->
    <div class="page-title">{{ pageTitle }}</div>
    <!-- 表单与表格 -->
    <CommonTable
      :columns="publicOpinionInfoColumns"
      :data="tableData"
      :loading="tableLoading"
      :show-default-btn="false"
      :show-operation-column="false"
      :operation-column="100"
      no-padding
      :max-height="650"
      :highlight-current-row="false"
      @handle-selection-change="handleSelectionChange"
      @handleRowClick="handleRowClick"
    >
      <!-- 表格列 -->
      <!-- 舆情id -->
      <template #reportNumber="{ row }">
        <!-- <div class="flex flex-center"> -->
        <div class="whitespace-normal break-all">
          {{ row.reportNumber || "-" }}
        </div>
        <!-- </div> -->
        <img
          v-if="row.isRumor"
          src="@/assets/images/rumor-bg.svg"
          class="w-[57px] absolute top-[10px] right-[-0px]"
        />
      </template>

      <!-- 是否为首保 -->
      <template #isFirst="{ row }">
        <span>{{ row.isFirst === IS_FIRST_STATUS.YES ? "是" : "否" }}</span>
      </template>

      <!-- 标题 -->
      <template #poName="{ row }">
        <div class="contentSpaceControl">
          {{ row?.poName || "-" }}
        </div>
      </template>

      <!-- 内容可能为文本/图片 -->
      <template #poContent="{ row }">
        <ImagePreview
          v-if="contentIsImg(row.poContent)"
          :src="row.poContent"
          :width="100"
          :height="100"
          @click.stop
        />
        <div v-else class="contentSpaceControl whitespace-pre-wrap!">{{ poContentF(row.poContent) }}</div>
      </template>

      <!-- 贴文链接 -->
      <template #poLink="{ row }">
        <div v-if="row.poLink" class="flex">
          <div class="tagWrapper truncate">
            <img
              src="@/assets/images/poManage/link.png"
              alt="link"
              class="w-[10px] h-[10px] mr-[4px]"
            />
            <el-link
              class="linkText truncate"
              :underline="false"
              :href="row.poLink"
              target="blank"
              @click.stop
              >{{ row.poLink }}</el-link
            >
          </div>
        </div>
        <span v-else>-</span>
      </template>

      <!-- 事件 -->
      <template #poEvent="{ row }">
        <PoEvent :poEvent="row.poEvent" />
      </template>

      <!-- 来源 -->
      <template #reportSource="{ row }">
        <dict-tag
          v-if="row.reportSource"
          :options="opinion_source_type"
          :value="row.reportSource"
        />
        <span v-else>-</span>
      </template>

      <!-- 昵称：网安网端需要跳转到网名库详情，互联网端不需要 -->
      <template #netizenNickname="{ row }">
        <span class="text-[12px] text-[#0052D9]">{{
          row.netizenNickname
        }}</span>
      </template>

      <!-- 图片 -->
      <template #poImg="{ row }">
        <ImagePreview
          v-if="row.poImg?.length > 0"
          :src="row.poImg[0]"
          :width="100"
          :height="100"
          @click.stop
        />
        <span v-else>-</span>
      </template>

      <!-- 贴文状态 -->
      <template #articleStatus="{ row }">
        <span>{{ articleStatusT(row.articleStatus).text }}</span>
      </template>

      <!-- 处置状态 -->
      <template #handleStatus="{ row }">
        <div
          class="handleStatusWrapper"
          :style="{ backgroundColor: handleStatusT(row.handleStatus).bgColor }"
        >
          <span
            class="truncate"
            :style="{ color: handleStatusT(row.handleStatus).color }"
            >{{ handleStatusT(row.handleStatus).label }}</span
          >
        </div>
      </template>

      <!-- 表单 -->
      <template #search>
        <el-form :model="queryParams" :inline="true" class="custom-form">
          <el-form-item label="" prop="reportNumber">
            <el-input
              prefix-icon="Search"
              v-model="queryParams.reportNumber"
              placeholder="请输入舆情编号"
              clearable
              style="width: 200px"
              @keyup.enter="getTableData"
              @clear="getTableData"
            />
          </el-form-item>

          <el-form-item label="" prop="event">
            <el-input
              prefix-icon="Search"
              v-model="queryParams.event"
              placeholder="请输入舆情事件"
              clearable
              style="width: 200px"
              @keyup.enter="getTableData"
              @clear="getTableData"
            />
          </el-form-item>

          <el-form-item label="" prop="targetGroup">
            <el-select v-model="queryParams.targetGroup" placeholder="请选择目标群" clearable style="width: 200px" @change="getTableData">
              <template #prefix>目标群</template>
              <el-option label="全部" value="all" />
              <el-option v-for="item in targetGroupList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>

          <el-form-item label="" prop="reportSource">
            <el-select
              v-model="queryParams.reportSource"
              placeholder="请选择消息来源"
              clearable
              style="width: 200px"
              @change="getTableData"
              @clear="getTableData"
            >
              <template #prefix>来源</template>
              <el-option label="全部" value="all" />
              <el-option
                v-for="dict in opinion_source_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <!-- <el-button type="primary" icon="Search" @click="getTableData"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button> -->
        </el-form>
      </template>

      <!-- 操作列 -->
      <!-- <template #operation="{ row }">
        <el-button link type="primary" @click.stop="onAppraisal(row)"
          >谣言鉴定</el-button
        >
      </template> -->
    </CommonTable>

    <div class="h-[30px] flex justify-end items-center">
      <!-- 分页 -->
      <pagination
        v-model:page="pageObj.pageNum"
        v-model:limit="pageObj.pageSize"
        :total="pageObj.total"
        @pagination="getTableData"
      />
    </div>

    <!-- 谣言鉴定弹框 -->
    <ConfirmDialog ref="confirmDialog" @refresh-data="getTableData" />
  </div>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
import ConfirmDialog from "./components/confirmDialog.vue";
import { publicOpinionInfoColumns } from "../config/tableColumns.js";
import { getRumorOpinionList } from "@/api/rumorCenter/appraisal.js";
import { useFetchTableData } from "@/views/rumorCenter/hooks/useFetchTableData";
import { ARTICLE_STATUS_LIST, HANDLE_STATUS_LIST } from "../config/constant.js";
const { proxy } = getCurrentInstance();
import { IS_FIRST_STATUS } from "@/views/rumorCenter/config/constant.js";
import PoEvent from "@/views/publicOpinionManage/components/PoEvent.vue";
import { getTargetGroup } from "@/api/poManage/poInfo.js";

const { opinion_source_type, media_type } = proxy.useDict("opinion_source_type", "media_type");

const router = useRouter();
const route = useRoute();

const queryParams = ref({
  reportNumber: "",
  event: "",
  reportSource: "all",
  targetGroup: "all",
});

const confirmDialog = ref(null);

const pageTitle = ref(route.meta.title);

// 贴文、处置状态 label-status转化
const articleStatusT = computed(() => (val) => {
  const obj = ARTICLE_STATUS_LIST.find((ele) => ele.value === val);
  return { text: obj?.label || "-" };
});
const handleStatusT = computed(() => (val) => {
  return HANDLE_STATUS_LIST.find((i) => i.value === val);
});

/**
 * 谣言鉴定
 */
const onAppraisal = (row) => {
  confirmDialog.value.openDialog(row);
};

/**
 * 重置
 */
const resetQuery = () => {
  queryParams.value = {
    reportNumber: "",
    event: "",
    reportSource: "all",
    targetGroup: "",
  };
};

const targetGroupList = ref([]);

/**
 * 获取目标群下拉项
 */
async function getTargetGroupList() {
  const res = await getTargetGroup();
  if (res.code === 200) {
    targetGroupList.value = res.data;
  }
}

getTargetGroupList();

const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 内容是否为图片
const poContentF = computed(() => val => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter(i => !contentIsImg.value(i));
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});

/**
 * 表格数据整理
 */
function getDataMap(data) {
  return data.map((ele) => {
    return {
      id: ele.id,
      isRumor: ele.rumorOpinion?.rumorFlag === "1" ? true : false,
      reportNumber: ele.reportNumber,
      isFirst: ele.firstReportTag,
      poName: ele.title,
      poContent: ele.content,
      poLink: ele.linkUrl,
      poEvent: ele.event,
      workUnit: ele.workUnit, // 上报单位
      mediaType: media_type.value.find(i => Number(i.value) === ele.mediaType)?.label || "-", // 媒体类型
      reportSource: ele.reportSource,
      platformType: ele.platformTag,
      wechatNickname: ele.wechatNickname, // 微信报送昵称
      netizenNickname: ele.netizenNickname,
      netizenAccount: ele.netizenAccount, // 网名账号
      publicTime: ele.publishTime,
      poImg: ele.photoUrl && ele.photoUrl?.split(","),
      articleStatus: ele.sourceStatus,
      handleStatus: ele.reportProcess,
      createTime: ele.createTime,
      targetGroup: ele.targetGroup,
      rumorOpinion: ele.rumorOpinion,
    };
  });
}

// 表格相关数据
const { tableData, tableLoading, pageObj, getTableData } = useFetchTableData({
  api: getRumorOpinionList,
  params: queryParams,
  getDataMap,
});

const handleRowClick = (row) => {
  // 跳转详情页
  router.push({
    name: "RumorDetail",
    state: { id: row.rumorOpinion?.id },
  });
};
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: absolute; // 撑开高度
  padding: 22px 0 22px 23px;

  .page-title {
    // margin-left: 23px;
    // margin-top: 22px;
    margin-bottom: 22px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }

  .firstBtn {
    width: 38px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #e6ecff;
    border-radius: 3px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #1749e0;
  }

  .contentSpaceControl {
    white-space: normal;
    word-break: break-all;

    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4; /* 控制几行打点 */
    line-clamp: 4;
    overflow: hidden;
  }

  .tagWrapper {
    height: 22px;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding-left: 8px;
    padding-right: 5px;
    background: #ffffff;
    box-shadow: 0px 1px 3px 0px rgba(222, 224, 232, 0.5);
    border-radius: 14px;
    border: 1px solid #e6e6e9;
  }

  .handleStatusWrapper {
    width: 50px;
    height: 19px;
    border-radius: 3px;

    display: flex;
    justify-content: center;
    align-items: center;

    span {
      width: 36px;
      height: 19px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      line-height: 18px;
      text-align: left;
      font-style: normal;
    }
  }

  .appraisalBtn {
    width: 90px;
    padding: 0px 12px;
    background-color: #0052d9;
    font-size: 12px;
    color: #ffffff;
    cursor: pointer;
  }

  :deep(.custom-form.el-form) {
    display: flex;
    // justify-content: space-between;

    .el-form-item {
      margin-right: 8px;
      margin-bottom: 10px;

      .el-input__wrapper {
        border-radius: 6px;
      }
    }
  }

  // 舆情内容链接
  :deep(.linkText) {
    display: inline;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #0070ff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &:visited {
      color: #0070ff;
    }
    .el-link__inner {
      display: inline;
    }
  }
}
</style>

<style></style>
