<template>
  <div class="table-container" :class="[{ noPadding: noPadding }]">
    <div class="p-b-[15px]" :class="[{ noPadding: noPadding }]">
      <slot name="search"></slot>
      <slot v-if="props.showDefaultBtn" name="button">
        <el-button type="primary" :icon="Edit" @click="handleAdd">
          新增
        </el-button>
        <el-button type="danger" :icon="Delete" @click="handleBatchDel">
          批量删除
        </el-button>
      </slot>
    </div>
    <el-table
      ref="tableRef"
      v-loading="loading"
      highlight-current-row
      row-key="id"
      :reserve-selection="true"
      :data="data"
      :header-cell-style="calcHeaderStyles"
      :style="style"
      @sortChange="handleSort"
      @selection-change="handleSelectionChange"
      @current-change="handleCurrentChange"
    >
      <el-table-column
        v-for="col in columns"
        :prop="col.prop"
        :key="col.prop"
        :label="col.label"
        :width="col.width"
        :fixed="col.fixed"
        :align="calcAlign(col)"
        :sortable="col.sortable"
        :type="col.type"
        :show-overflow-tooltip="true"
      >
        <template v-if="col.slotLabel" #header>
          <slot :name="col.slotLabel"></slot>
        </template>
        <!-- 自定义动态渲染 -->
        <template v-if="col.slotName" #default="scope">
          <slot
            :name="col.slotName"
            :row="scope.row"
            :index="scope.$index"
          ></slot>
        </template>
        <!-- 默认渲染 -->
        <template
          v-else-if="
            !col.slotName && col.type !== 'index' && col.type !== 'selection'
          "
          #default="scope"
        >
          <span>{{ scope.row[col.prop] || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="showOperationColumn"
        label="操作"
        :width="operationColumn"
        fixed="right"
        align="center"
      >
        <template #default="scope">
          <slot name="operation" :row="scope.row">
            <el-button link type="primary" @click="handleEdit(scope.row)">
              <span class="text-[#1a96ff] text-[12px]">
                {{ scope.row?.isEdit ? '保存' : '编辑' }}
              </span>
            </el-button>
            <el-popconfirm
              :width="200"
              confirm-button-text="是"
              cancel-button-text="否"
              :icon="InfoFilled"
              icon-color="#626AEF"
              title="你是否删除这条记录吗?"
              @confirm="confirmEvent(scope.row)"
              @cancel="cancelEvent(scope.row)"
            >
              <template #reference>
                <el-button link type="danger">
                  <span class="text-[#1a96ff] text-[12px]">删除</span>
                </el-button>
              </template>
            </el-popconfirm>
          </slot>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="showPagination" class="flex flex-row justify-end pt-[15px]">
      <el-pagination
        v-model:current-page="pageParams.pageNum"
        v-model:page-size="pageParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="Number(total)"
        @size-change="handlePageSizeChange"
        @current-change="handleCurrentPageChange"
      />
    </div>
  </div>
</template>

<script setup name="listTable">
import { Delete, Edit, InfoFilled } from '@element-plus/icons-vue'

/**
 * 定义组件发出的事件类型。
 *
 * @returns {Object} 返回一个对象，包含组件可能发出的所有事件类型。
 */
const emits = defineEmits([
  'sortChange', // 排序变化事件
  'pageChange', // 页码变化事件
  'handleAdd', // 添加操作事件
  'handleBatchDel', // 批量删除操作事件
  'handleEdit', // 编辑操作事件
  'confirmEvent', // 确认操作事件
  'cancelEvent', // 取消操作事件
  'handlePageSizeChange', // 页面大小变化事件
  'handleCurrentPageChange', // 当前页码变化事件
  'update:pageNum', // 更新页码事件
  'update:pageSize', // 更新页面大小事件
  'handleSelectionChange', // 选择项变化事件
  'handleCurrentChange', //选择项变化事件（单选）
])
const props = defineProps({
  /**
   * 定义数据属性。
   *
   * @description 表格所需的数据。
   * @property {Array} type 数据类型，此处指定为数组。
   * @property {Function} default 默认值回调函数，返回一个空数组作为默认值。
   */
  data: {
    type: Array,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
  // 定义组件属性：当前页码
  // 类型为Number，默认值为1
  initialCurrentPage: {
    type: Number,
    default: 1,
  },
  // 设置初始页面大小的属性
  initialPageSize: {
    type: Number, // 属性类型为数字
    default: 10, // 默认页面大小为10
  },
  loading: {
    type: Boolean,
    default: false,
  },
  //表格渲染的列
  columns: {
    type: Array,
    default: () => [],
  },
  style: {
    type: [Object, String],
    default: () => {},
  },
  // 定义头部样式的配置项
  headerStyle: {
    type: [Object, String], // 可以是对象或字符串类型
    default: () => {}, // 默认值为空函数，返回一个空对象
  },
  // 控制分页显示的配置项
  showPagination: {
    type: Boolean, // 数据类型为布尔值
    default: true, // 默认值为true，即在组件初始化时，分页器是可见的
  },
  // 控制是否显示默认操作列
  showDefaultBtn: {
    type: Boolean,
    default: true,
  },
  /**
   * showOperationColumn 属性配置
   * 用于控制是否显示操作列
   *
   * @type {Boolean} 是否显示操作列
   * @default {true} 默认为 true，即显示操作列
   */
  showOperationColumn: {
    type: Boolean,
    default: true,
  },
  operationColumn: {
    type: Number,
    default: 150,
  },
  pageParams: {
    type: Object,
    default: () => {
      return {
        pageNum: 1,
        pageSize: 10,
      }
    },
  },
  noPadding: {
    type: Boolean,
    default: false,
  },
})

const tableRef = ref(null)
const pageSize = ref(props.initialPageSize)
const pageNum = ref(props.initialCurrentPage)
const pageParams = ref(props.pageParams)

watch(
  () => [pageNum.value, pageSize.value],
  (newV) => {
    console.log(newV)
  },
)

watch(
  () => pageParams.value,
  (newV) => {
    console.log(newV)
  },
  { deep: true },
)

/**
 * 监听 pageSize 和 pageNum 的变化。
 * 当这两个值发生变化时，会触发 'update:pageSize' 和 'update:pageNum' 两个自定义事件，
 * 分别携带新的 pageSize 和 pageNum 值。
 * 可以在父组件中使用v-model:pageSize 和 v-model:pageNum 来监听和修改这两个值。
 *
 * @param newValue 新的 pageSize 和 pageNum 值组成的数组
 */
watch(
  () => [pageParams.value?.pageNum, pageParams.value?.pageSize], // 监听对象为 pageSize 和 pageNum 的值
  (newValue) => {
    console.log(newValue)
    emits('update:pageSize', newValue[0]) // 发出 'update:pageSize' 事件，更新 pageSize 值
    emits('update:pageNum', newValue[1]) // 发出 'update:pageNum' 事件，更新 pageNum 值
  },
)

const headerStyles = ref({
  backgroundColor: '#F2F3F6', // 使用淡雅的浅灰色背景，保持简洁
  color: '#303133', // 使用深灰色文字，确保清晰可读
  fontSize: '14px', // 合适的字体大小，兼顾阅读性和空间效率
  fontWeight: '550', // 稍微加重字体，但不过于显眼
  borderBottom: '1px solid #eaeef5', // 细实线下边框，清晰划分表头与内容
})

/**
 * 计算并返回头部样式的对象。这个计算属性会根据传入的props中的headerStyle来决定返回的样式，
 * 如果props中没有提供headerStyle，则返回外部定义的headerStyles的值。
 *
 * @return {Object} 返回一个包含头部样式的对象。
 */
const calcHeaderStyles = computed(() => {
  // 根据props中的headerStyle决定返回值
  return props.headerStyle ? props.headerStyle : headerStyles.value
})

// 处理是左对齐还是右对齐
function calcAlign(col) {
  if (['selection'].includes(col.type)) {
    return 'left'
  } else if (col.align) {
    return col.align
  }
  return 'center'
}

/**
 * 处理排序操作
 * @param {any} data - 排序数据或信息
 * @emits 'sortChange' - 发出排序变更事件，携带排序数据或信息
 */
function handleSort(data) {
  emits('sortChange', data) // 触发排序变更事件
}

/**
 * 处理添加操作的函数。
 * 该函数没有参数。
 * 没有返回值。
 * 通过触发名为'handleAdd'的自定义事件来响应操作。
 * @emits 'handleAdd' 当调用此函数时，会发出此事件。
 */
function handleAdd() {
  emits('handleAdd') // 触发'handleAdd'事件
}

/**
 * 处理批量删除操作的函数。
 * 该函数会触发一个名为 'handleBatchDel' 的自定义事件。
 *
 * @emits 'handleBatchDel' 当调用此函数时，会发出此事件。
 */
function handleBatchDel() {
  emits('handleBatchDel') // 触发 'handleBatchDel' 事件
}

/**
 * 处理编辑操作的函数。
 * 该函数会触发一个名为 'handleEdit' 的事件。
 *
 * @emits 'handleEdit' 当调用此函数时，会发出 'handleEdit' 事件。
 */
function handleEdit(row) {
  emits('handleEdit', row) // 触发 'handleEdit' 事件
}

/**
 * 确认事件操作
 * @param {Object} row - 行对象，代表需要操作的数据行
 * @emits 'confirmEvent' - 触发名为'confirmEvent'的自定义事件，将行对象作为参数传递给事件监听器
 */
function confirmEvent(row) {
  emits('confirmEvent', row)
}

/**
 * 取消事件操作
 * @param {Object} row - 代表一行数据的对象，此参数在取消事件时会传递给监听此事件的父组件。
 * @emits 'cancelEvent' - 发出一个名为'cancelEvent'的自定义事件，携带参数row。
 */
function cancelEvent(row) {
  emits('cancelEvent', row) // 触发'cancelEvent'事件，通知父组件取消操作的发生。
}

/**
 * 处理分页大小更改事件
 * @param {number} pageSize - 新的每页记录数
 * @emits 'handlePageSizeChange' - 触发分页大小更改事件，传递新的每页记录数
 */
function handlePageSizeChange(pageSize) {
  emits('handlePageSizeChange', pageSize)
}

/**
 * 处理当前页码变化的事件
 * @param pageNum {number} 新的页码
 * @emits 'handleCurrentPageChange' 触发当前页码变化的自定义事件，将新的页码作为参数传递
 */
function handleCurrentPageChange(pageNum) {
  emits('handleCurrentPageChange', pageNum)
}

/**
 * 处理选择列表的变化
 * @param selectedList 选择后的列表
 * @emits 'handleSelectionChange' 触发选择列表变化的事件，携带当前选择的列表作为参数
 */
function handleSelectionChange(selectedList) {
  emits('handleSelectionChange', selectedList)
}
/**
 * 处理选择项的变化
 * @param selectedRow 选择后的列表
 * @emits 'handleCurrentChange' 触发选择变化的事件，携带当前选择的项作为参数
 */
function handleCurrentChange(selectedRow) {
  emits('handleCurrentChange', selectedRow)
}

</script>
<style scoped lang="scss">
.table-container {
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
}

.noPadding {
  padding: 0px;
}
</style>
