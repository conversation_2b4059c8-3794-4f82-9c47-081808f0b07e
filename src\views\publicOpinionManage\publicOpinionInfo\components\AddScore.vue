<template>
  <el-dialog title="赋分" v-model="open" width="820px" append-to-body class="dialog-score">
    <h2 style="text-align: center; margin-top: 0">{{ currentRow.poName }}</h2>
    <el-divider />
    <div class="score-static flex">
      <el-statistic title="基础分值" :value="scoreAll.base || 0" />
      <el-statistic title="赋分值" :value="scoreAll.value || 0" />
      <el-button type="danger" size="small" @click="resetScore">重置赋分</el-button>
    </div>

    <el-form ref="scoreFormRef" label-position="left" :model="form" :rules="rules" label-width="160px" class="" status-icon>
      <el-form-item v-for="(value, key) in scoreObj" :key="key" :label="key" :prop="key">
        <el-radio-group v-model="form[key]">
          <el-radio-button v-for="i in value" :label="i.scorePointName + `(${i.scorePointValue})`" :value="i.scorePointName"></el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="'赋分备注'" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 5 }"
          clearable
          placeholder="请输入赋分备注"
          :maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button class="cancelDialogBtn" @click="cancel">取消</el-button>
      <el-button class="addDialogBtn" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { scoreList, scoreSave, scoreDetail } from "@/api/poManage/score";

const emit = defineEmits(["refresh"]);

const open = ref(false); // 弹窗显隐
const scoreFormRef = ref(null);
const form = ref({});
const currentRow = ref({});
const scoreObj = ref({}); // 赋分项列表
const rules = {
  description: [{ min: 1, max: 200, message: "输入长度在200个字符以内", trigger: ["change", "blur"] }]
};

const scoreAll = computed(() => {
  // 循环遍历对象scoreObj，计算基础分值
  let base = 0;
  let value = 0;
  let list = [];
  try {
    for (const key in scoreObj.value) {
      if (form.value[key]) {
        if (scoreObj.value[key]) {
          scoreObj.value[key].forEach(element => {
            if (element.scorePointName === form.value[key]) {
              base += Number(element.baseScore);
              value += Number(element.scorePointValue);
              let info = { ...element };
              delete info.id;
              list.push(info);
            }
          });
        }
      }
    }
    return { base, value, list };
  } catch (error) {
    console.warn("计算错误", error);
    return { base: 0, value: 0, list: [] };
  }
});

/**
 * 重置赋分
 */
const resetScore = () => {
  for (const key in scoreObj.value) {
    form.value[key] = "";
  }

  scoreFormRef.value?.clearValidate();
};

/**
 * 确定
 */
const submitForm = () => {
  scoreFormRef.value.validate(async valid => {
    if (valid) {
      try {
        let params = {
          reportId: currentRow.value.id,
          baseScore: scoreAll.value.base,
          scorePointValue: scoreAll.value.value,
          description: form.value.description,
          scoreDetailList: scoreAll.value.list
        };
        const { msg } = await scoreSave(params);
        ElMessage.success("赋分成功");
        open.value = false;
        emit("refresh");
      } catch (error) {
        console.error("赋分失败：", error);
      }
    }
  });
};

/**
 * 打开弹窗
 */
const openDialog = async row => {
  open.value = true;
  resetScore();
  currentRow.value = { ...row };
  await getScoreList();
  await getScoreDetail();
};

/**
 * 获取配置的赋分项
 */
const getScoreList = () => {
  return scoreList().then(res => {
    scoreObj.value = res.data;
  });
};

/**
 * 获取分数详情
 */
const getScoreDetail = async () => {
  let { data } = await scoreDetail({ reportId: currentRow.value.id });
  if (!data) return;
  let list = data.scoreDetailList || [];
  list.forEach(i => {
    form.value[i.itemName] = i.scorePointName;
  });
  form.value.remark = data.remark;
};

/**
 * 取消
 */
const cancel = () => {
  resetScore();
  open.value = false;
};

defineExpose({ openDialog });
</script>

<style lang="scss">
.dialog-score {
  .el-dialog__body {
    padding-top: 0;
  }
  .el-radio-button__inner {
    border-left: var(--el-border);
  }
  .score-static {
    margin: 10px 0;
    gap: 40px;
    justify-content: center;
    align-items: center;
    .el-statistic {
      justify-content: center;
      align-items: center;
      display: flex;
      gap: 10px;
      .el-statistic__head {
        margin-bottom: 0;
      }
      .el-statistic__number {
        font-weight: 600;
      }
    }
  }

  .cancelDialogBtn {
    width: 80px;
    height: 32px;
    line-height: 32px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #cfd2d6;
    font-size: 14px;
    color: #1f2329;
  }

  .addDialogBtn {
    width: 80px;
    height: 32px;
    line-height: 32px;
    background: #0052d9;
    border-radius: 5px;
    font-size: 13px;
    color: #ffffff;
  }
}
</style>
