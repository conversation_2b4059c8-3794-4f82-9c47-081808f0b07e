<template>
  <div class="flex items-center">
    <section class="mr-[6px]" v-if="showTaskStatus">
      <!-- <span class="text-[14px]">任务状态：</span> -->
      <el-select v-model="searchCondition.taskStatus" placeholder="请选择任务状态" clearable style="width: 200px" @change="$emit('search')">
        <template #prefix>任务状态</template>
        <el-option label="全部" value="all" />
        <el-option v-for="item in SELF_TASK_STATUS_LIST" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </section>
    <section class="mr-[6px]">
      <!-- <span class="text-[14px]">舆情编号：</span> -->
      <el-input
        v-model="searchCondition.poId"
        placeholder="请输入舆情编号"
        :prefix-icon="Search"
        style="width: 200px"
        clearable
        @change="$emit('search')"
      />
    </section>
    <section class="mr-[6px]">
      <!-- <span class="text-[14px]">舆情事件：</span> -->
      <el-input
        v-model="searchCondition.poEvent"
        placeholder="请输入舆情事件"
        :prefix-icon="Search"
        style="width: 200px"
        clearable
        @change="$emit('search')"
      />
    </section>
    <section class="mr-[6px]">
      <!-- <span class="text-[14px]">目标群：</span> -->
      <el-select v-model="searchCondition.targetGroup" placeholder="请选择目标群" clearable style="width: 224px" @change="$emit('search')">
        <template #prefix>目标群</template>
        <el-option label="全部" value="all" />
        <el-option v-for="item in targetGroupList" :key="item" :label="item" :value="item" />
      </el-select>
    </section>
    <section>
      <!-- <span class="text-[14px]">来源：</span> -->
      <el-select v-model="searchCondition.msgFrom" placeholder="请选择来源" clearable style="width: 220px" @change="$emit('search')">
        <template #prefix>来源</template>
        <el-option label="全部" value="all" />
        <el-option v-for="item in msgFromOption" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </section>
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";
import { SELF_TASK_STATUS_LIST } from "../config/constant";
import { getTargetGroup } from "@/api/poManage/poInfo.js";

defineProps({
  // 搜索条件
  searchCondition: {
    type: Object,
    required: true
  },
  // 舆情来源字典
  msgFromOption: {
    type: Array,
    required: true
  },
  showTaskStatus: {
    type: Boolean,
    default: false
  }
});

defineEmits(["search"]);

const targetGroupList = ref([]);

/**
 * 获取目标群下拉项
 */
async function getTargetGroupList() {
  const res = await getTargetGroup();
  if (res.code === 200) {
    targetGroupList.value = res.data;
  }
}

getTargetGroupList();
</script>

<style lang="scss" scoped></style>
