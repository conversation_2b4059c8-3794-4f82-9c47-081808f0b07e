<template>
  <div class="dialog-container">
    <el-dialog
      :model-value="modelValue"
      width="800px"
      @close="onCancel"
      append-to-body
    >
      <CommonTable
        :columns="currentTableColumns"
        :data="tableDataF"
        :loading="tableLoading"
        :show-default-btn="false"
        :operation-column="160"
        no-padding
        :max-height="400"
        :highlight-current-row="false"
        :cellStyle="{ 'vertical-align': 'middle' }"
      >
        <template #poType="{ row }">
          <el-input
            v-if="row.isNew || row.isEdit"
            v-model="row.newPoType"
            placeholder="请填写舆情类型"
            clearable
          />
          <span v-else>{{ row.poType }}</span>
        </template>

        <template #platformName="{ row }">
          <el-input
            v-if="row.isNew || row.isEdit"
            v-model="row.newPlatformName"
            placeholder="请填写平台类型"
            clearable
          />
          <span v-else>{{ row.platformName }}</span>
        </template>

        <template #operation="{ row }">
          <template v-if="row.isEdit">
            <el-button type="primary" link @click="onEdit(row)">确认</el-button>
            <el-button type="primary" link @click="onCancelEdit(row)"
              >取消</el-button
            >
          </template>
          <template v-else>
            <template v-if="row.isNew">
              <el-button type="primary" link @click="onConfirm(row)"
                >确定</el-button
              >
              <el-button type="primary" link @click="onAdd(row)"
                >添加</el-button
              >
            </template>
            <el-button v-else type="primary" link @click="row.isEdit = true"
              >编辑</el-button
            >

            <el-popconfirm title="确定删除吗？" @confirm="onDel(row)">
              <template #reference>
                <el-button
                  type="primary"
                  link
                  :disabled="row.isNew && addTableData.length === 1"
                  >删除</el-button
                >
              </template>
            </el-popconfirm>
          </template>
        </template>
      </CommonTable>

      <!-- 关闭按钮 -->
      <template #footer>
        <div class="flex-center">
          <el-button class="addDialogBtn" @click="onCancel">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
import {
  PUBLIC_OPINION_TYPE_ADD_COLUMNS,
  PLATFORM_TYPE_ADD_COLUMNS,
} from "@/views/publicOpinionManage/config/tableColumns.js";
import { v4 as uuidv4 } from "uuid";
import { listData, addData, updateData, delData } from "@/api/system/dict/data";
import {
  getPlatformList,
  addPlatform,
  delPlatform,
  updatePlatform,
} from "@/api/poManage/poInfo";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  addType: {
    type: String,
    default: "",
    required: true,
  },
});

const dialogTitle = computed(() => {
  return props.addType === "publicOpinion" ? "添加舆情类型" : "添加平台类型";
});

const emit = defineEmits(["update:modelValue", "refreshSelectList"]);

const { proxy } = getCurrentInstance();

const tableData = ref([]); // 表格数据
const tableLoading = ref(false); // 表格加载状态
const addTableData = ref([]);

const tableDataF = computed(() => [...tableData.value, ...addTableData.value]);

const currentTableColumns = computed(() => {
  return props.addType === "publicOpinion"
    ? PUBLIC_OPINION_TYPE_ADD_COLUMNS
    : PLATFORM_TYPE_ADD_COLUMNS;
});

// 展示弹窗时，获取已有的舆情类型/平台类型，同时新增一行
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      if (props.addType === "publicOpinion") {
        getPoTypeList();
      } else {
        getPlatformTypes();
      }

      onAdd();
    }
  }
);

async function getPoTypeList() {
  tableLoading.value = true;

  listData({ dictType: "opinion_type" }).then((response) => {
    tableData.value = response.rows.map((ele) => ({
      id: ele.dictCode,
      poType: ele.dictLabel || "",
      newPoType: ele.dictLabel || "",
      dictValue: ele.dictValue || "",
      isNew: false, // 是否是新增数据
      isEdit: false, // 是否是编辑状态
    }));
    tableLoading.value = false;
  });

  emit("refreshSelectList");
}

async function getPlatformTypes() {
  tableLoading.value = true;

  getPlatformList().then((response) => {
    tableData.value = response.data.map((label) => ({
      platformName: label || "",
      newPlatformName: label || "",
      isNew: false, // 是否是新增数据
      isEdit: false, // 是否是编辑状态
    }));
    tableLoading.value = false;

    console.log("tableData.value", tableData.value);
  });

  emit("refreshSelectList");
}

/**
 * 添加舆情类型/平台类型
 */
async function onConfirm({ id, newPoType, newPlatformName }) {
  if (props.addType === "publicOpinion") {
    const res = await addData({
      dictType: "opinion_type",
      dictLabel: newPoType,
      dictValue: tableData.value.length + 1,
      dictSort: tableData.value.length + 1,
    });
    if (res.code === 200) {
      proxy.$message.success("添加成功");
      getPoTypeList();
      addTableData.value = addTableData.value.filter((i) => i.id !== id);
      if (!addTableData.value.length) {
        onAdd();
      }
    }
  } else {
    const res = await addPlatform({
      name: newPlatformName,
    });
    if (res.code === 200) {
      proxy.$message.success("添加成功");
      getPlatformTypes();
      addTableData.value = addTableData.value.filter((i) => i.id !== id);
      if (!addTableData.value.length) {
        onAdd();
      }
    }
  }
}

/**
 * 添加
 */
function onAdd() {
  if (props.addType === "publicOpinion") {
    addTableData.value.push({
      id: uuidv4(),
      poType: "",
      newPoType: "",
      isNew: true, // 是否是新增数据
    });
  } else {
    addTableData.value.push({
      id: uuidv4(),
      platformName: "",
      newPlatformName: "",
      isNew: true, // 是否是新增数据
    });
  }
}

/**
 * 编辑
 */
async function onEdit({
  id,
  newPoType,
  dictValue,
  platformName,
  newPlatformName,
}) {
  if (props.addType === "publicOpinion") {
    const res = await updateData({
      dictCode: id,
      dictType: "opinion_type",
      dictValue,
      dictLabel: newPoType,
    });
    if (res.code === 200) {
      proxy.$message.success("编辑成功");
      getPoTypeList();
    }
  } else {
    const res = await updatePlatform({
      oldName: platformName,
      name: newPlatformName,
    });
    if (res.code === 200) {
      proxy.$message.success("编辑成功");
      getPlatformTypes();
    }
  }
}

/**
 * 取消编辑
 */
function onCancelEdit(row) {
  row.isEdit = false;
  if (props.addType === "publicOpinion") {
    row.newPoType = row.poType;
  } else {
    row.newPlatformName = row.platformName;
  }
}

/**
 * 删除
 */
async function onDel({ id, isNew, platformName }) {
  if (isNew) {
    addTableData.value = addTableData.value.filter((i) => i.id !== id);
  } else {
    if (props.addType === "publicOpinion") {
      const res = await delData(id);
      if (res.code === 200) {
        proxy.$message.success("删除成功");
        getPoTypeList();
      }
    } else {
      const res = await delPlatform({ name: platformName });
      if (res.code === 200) {
        proxy.$message.success("删除成功");
        getPlatformTypes();
      }
    }
  }
}

function onCancel() {
  reset();
  emit("update:modelValue", false);
}

/**
 * 重置数据
 */
function reset() {
  tableData.value = [];
  tableLoading.value = false;
  addTableData.value = [];
}
</script>

<style lang="scss" scoped>
.dialog-container {
  :deep(.el-dialog) {
    padding: 24px 0;

    .el-dialog__header {
      padding-left: 24px;

      .dialogTitle {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #1f2329;
      }
    }

    .el-dialog__body {
      padding: 0 24px;
    }

    .el-dialog__footer {
      padding-top: 24px;
    }
  }

  .addDialogBtn {
    width: 80px;
    height: 32px;
    line-height: 32px;
    border-radius: 6px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;

    background: #0070ff;
    color: #ffffff;
  }
}
</style>
