function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
    // console.log('h: ', h)
    // 计算
    const midRatio = (startRatio + endRatio) / 2

    const startRadian = startRatio * Math.PI * 2
    const endRadian = endRatio * Math.PI * 2
    const midRadian = midRatio * Math.PI * 2

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
        // eslint-disable-next-line no-param-reassign
        isSelected = false
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    // eslint-disable-next-line no-param-reassign
    k = typeof k !== 'undefined' ? k : 1 / 3

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
    const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    const hoverRate = isHovered ? 1.05 : 1 // 可以做为调整外环大小

    // 返回曲面参数方程
    return {
        u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32,
        },

        v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
        },

        x(u, v) {
            if (u < startRadian) {
                return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            if (u > endRadian) {
                return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
        },

        y(u, v) {
            if (u < startRadian) {
                return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            if (u > endRadian) {
                return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
        },

        z(u, v) {
            if (u < -Math.PI * 0.5) {
                return Math.sin(u)
            }
            if (u > Math.PI * 2.5) {
                return Math.sin(u) * h * 0.1
            }
            // 当前图形的高度为固定的 只有高亮时会更搞
            // 如果高度需要跟value相关 使用下面的
            // return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
            if (h) {
                return Math.sin(v) > 0 ? 1 * 25 * 0.1 : -1
            }
            return Math.sin(v) > 0 ? 1 * 10 * 0.1 : -1
            // return 5;
        },
    }
}
export function getPie3D(pieData, internalDiameterRatio) {
    const series = []
    const colorList = ['#3054EC', '#1FE7E8', '#1A90FF', '#FCB560', '#1890FF']

    // 总和
    let sumValue = 0
    let startValue = 0
    let endValue = 0
    const legendData = []
    const k =
        typeof internalDiameterRatio !== 'undefined'
            ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
            : 1 / 3

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i += 1) {
        sumValue += pieData[i].value

        // const colorGradient = new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
        //   { offset: 0, color: i === 0 ? '#ff7f50' : '#87cefa' }, // 第一段渐变起始颜色
        //   { offset: 1, color: i === 0 ? '#ff6347' : '#4682b4' }, // 第一段渐变结束颜色
        // ])

        const seriesItem = {
            name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
            type: 'surface',
            parametric: true,
            wireframe: {
                show: false,
            },
            pieData: pieData[i],
            pieStatus: {
                selected: false,
                hovered: false,
                k,
            },
            itemStyle: {
                color: colorList[i], // 自定义颜色
                opacity: '0.5',
            },
        }
        if (typeof pieData[i].itemStyle != 'undefined') {
            let itemStyle = {}

            typeof pieData[i].itemStyle.color != 'undefined'
                ? (itemStyle.color = pieData[i].itemStyle.color)
                : null
            typeof pieData[i].itemStyle.opacity != 'undefined'
                ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
                : null

            seriesItem.itemStyle = itemStyle
        }
        series.push(seriesItem)
    }

    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i += 1) {
        endValue = startValue + series[i].pieData.value

        series[i].pieData.startRatio = startValue / sumValue
        series[i].pieData.endRatio = endValue / sumValue
        series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            false,
            k,
            // 我这里做了一个处理，使除了第一个之外的值都是10
            series[i].pieData.value === series[0].pieData.value ? 35 : 10
        )

        startValue = endValue

        legendData.push(series[i].name)
    }

    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    const option = {
        legend: {
            show: true, // 不显示图例
            bottom: 0,
            itemGap: 20,
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                fontSize: 12,
                color: '#dcdedf',
            },
            // rich: {
            //     name: {
            //         fontSize: 8,
            //         color: '#E3F3FC',
            //     },
            //     value: {
            //         fontSize: 14,
            //         color: '#E3F3FC',
            //     },
            // },
            // formatter(name) {
            //   let tarValue
            //   for (var i = 0; i < this.pieData.length; i++) {
            //     if (this.pieData[i].name == name) {
            //       tarValue = this.pieData[i].value
            //     }
            //   }
            //   var v = tarValue
            //   return [`${name} `, ` ${v}`].join('')
            // },
        },

        animation: true,
        title: {
            text: '',
            left: "center",
            top: "22%",
            textStyle: {
                color: '#fff',
                fontSize: 18,
                fontWeight: 600,

                rich: {
                    a: {
                        color: '#fff',
                        fontWeight: 'bold',
                        fontSize: 20,
                        fontFamily: "PangMenZhengDao",
                    },
                    b: {
                        color: '#fff',
                        fontSize: 18,
                        fontFamily: "PangMenZhengDao",
                    },
                    c: {
                        color: '#fff',
                        fontSize: 14,
                        lineHeight: 20, // 控制行高
                        padding: [10, 0, 0, 0], // 增加顶部 padding 增大与上行的距离
                    },
                },
            },
        },
        xAxis3D: {
            min: -1,
            max: 1,
        },
        yAxis3D: {
            min: -1,
            max: 1,
        },
        zAxis3D: {
            min: -1,
            max: 1,
        },
        grid3D: {
            show: false,
            // boxWidth: 100,
            boxHeight: 6,
            // boxDepth: 100,
            backgroundColor: 'rgba(140,40,30,0.8)',
            top: '-20%',
            left: 'center',
            // environment: 'transparent',

            viewControl: {
                // 3d效果可以放大、旋转等，请自己去查看官方配置
                alpha: 35,
                // beta: 30,
                rotateSensitivity: 0,
                zoomSensitivity: 0,
                panSensitivity: 0,
                // autoRotate: true,
                distance: 120,
            },
            // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
            postEffect: {
                // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
                enable: false,
                bloom: {
                    enable: true,
                    bloomIntensity: 0.1,
                },
                SSAO: {
                    enable: true,
                    quality: 'medium',
                    radius: 2,
                },
                // temporalSuperSampling: {
                //   enable: true,
                // },
            },
        },
        series,
    }
    return option
}
function handleOver(params) {
    // console.log('params: ', params)
    // 准备重新渲染扇形所需的参数
    let isSelected
    let isHovered
    let startRatio
    let endRatio
    let k
    let i

    // 如果触发 mouseover 的扇形当前已高亮，则不做操作
    if (this.hoveredIndex === params.seriesIndex) {
        return

        // 否则进行高亮及必要的取消高亮操作
    } else {
        // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
        if (this.hoveredIndex !== '') {
            // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
            isSelected = this.option.series[this.hoveredIndex].pieStatus.selected
            isHovered = false
            startRatio = this.option.series[this.hoveredIndex].pieData.startRatio
            endRatio = this.option.series[this.hoveredIndex].pieData.endRatio
            k = this.option.series[this.hoveredIndex].pieStatus.k
            i =
                this.option.series[this.hoveredIndex].pieData.value ===
                this.option.series[0].pieData.value
                    ? 35
                    : 10
            // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
            this.option.series[this.hoveredIndex].parametricEquation = this.getParametricEquation(
                startRatio,
                endRatio,
                isSelected,
                isHovered,
                k,
                i
            )
            this.option.series[this.hoveredIndex].pieStatus.hovered = isHovered

            // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
            this.hoveredIndex = ''
        }

        // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
        if (params.seriesName !== 'mouseoutSeries') {
            const seriesData = this.option.series[this.seriesIndex].pieData
            this.option.title.text = [
                '{a|' + seriesData.value + '%}', // 第一行展示 value 和 %
                '{c|' + seriesData.name + '}', // 第二行展示 name
            ].join('\n') // 使用 \n 实现换行
            // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
            isSelected = this.option.series[params.seriesIndex].pieStatus.selected
            isHovered = true
            startRatio = this.option.series[params.seriesIndex].pieData.startRatio
            endRatio = this.option.series[params.seriesIndex].pieData.endRatio
            k = this.option.series[params.seriesIndex].pieStatus.k

            // 对当前点击的扇形，执行高亮操作（对 option 更新）
            this.option.series[params.seriesIndex].parametricEquation = this.getParametricEquation(
                startRatio,
                endRatio,
                isSelected,
                isHovered,
                k,
                // 设置高度
                // this.option.series[params.seriesIndex].pieData.value
                35
            )
            this.option.series[params.seriesIndex].pieStatus.hovered = isHovered

            // 记录上次高亮的扇形对应的系列号 seriesIndex
            this.hoveredIndex = params.seriesIndex
        }
        // 使用更新后的 option，渲染图表
        this.pieInstance.setOption(this.option)
    }
}
function getData() {
    this.option = this.getPie3D(
        this.pieData,
        0.89 // 可做为调整内环大小
    )

    if (this.option.series.length > 0) {
        const seriesData = this.option.series[0].pieData
        this.option.title.text = [
            '{a|' + seriesData.value + '%}', // 第一行展示 value 和 %
            '{c|' + seriesData.name + '}', // 第二行展示 name
        ].join('\n') // 使用 \n 实现换行
    }
    // pieInstance = echarts.init(pieRef.value, 'macarons')
    this.pieInstance = this.$echarts.init(this.$refs.pie_self, 'macarons')
    this.pieInstance.setOption(this.option)
    // if (this.timer) {
    //     clearInterval(this.timer)
    // }
    // this.timer = setInterval(() => {
    //     // console.log('this.seriesIndex: ', this.seriesIndex)
    //     this.handleOver({ seriesIndex: this.seriesIndex })
    //     this.seriesIndex++
    //     if (this.seriesIndex === this.option.series.length) {
    //         this.seriesIndex = 0
    //     }
    // }, 3000)
}