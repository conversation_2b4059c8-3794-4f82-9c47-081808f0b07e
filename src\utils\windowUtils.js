import { getToken } from '@/utils/auth';
import { encryptTokenAES, decryptTokenAES } from '@/utils/crypto';

// 第三版：从 localStorage 加载已打开窗口的记录（仅保存 URL）
const openedWindows = JSON.parse(localStorage.getItem('openedWindows') || '{}');

export function openApp(data, requiresToken = true) {
  console.log('data: ', data);
  let url = '';

  // 根据输入类型提取 URL
  if (typeof data === 'object' && data.appUrl) {
    url = data.appUrl;
  } else if (typeof data === 'string') {
    url = data;
  } else {
    console.error('传入URL不符合预期类型');
    return;
  }
  const windowName = url; // 使用 URL 作为窗口的唯一标识，不能带token

  if (requiresToken) {
    const token = getToken(); // 获取 token
    const encryptedToken = encryptTokenAES(token); // 对 token 进行加密
    url += `?token=${encryptedToken}`;
  }

  // const appUrlWithToken = `${data.appUrl}?token=${encryptedToken}`;
  // const windowName = data.appUrl; // 使用 URL 作为窗口的唯一标识

  // console.log('openedWindows: ', openedWindows);

  // 尝试查找已存在的窗口，检查 localStorage 中记录的 URL，尝试使用已有窗口名
  let existingWindow = window.open('', windowName);
  console.log('existingWindow: ', existingWindow);
  console.log('上一句执行了');

  // 从 localStorage 检查该 URL 是否已同源
  const sameOriginKey = `sameOrigin_${windowName}`;
  const sameOrigin = JSON.parse(localStorage.getItem(sameOriginKey) || 'false');
  console.log('sameOrigin: ', sameOrigin);
  // 如果该 URL 已同源，直接返回，不继续执行
  // if (sameOrigin) {
  //   console.log(`URL ${windowName} 已同源，跳过打开新窗口`);
  //   return;
  // }

  console.log('existingWindow.location.href: ', existingWindow.location.href);
  console.log('existingWindow.', existingWindow.location.href.startsWith(url.split('?')[0]));

  if (
    existingWindow &&
    !existingWindow.closed &&
    existingWindow.location.href.startsWith(url.split('?')[0])
  ) {
    console.log(1111111);
    // 如果窗口存在且未关闭，则聚焦
    existingWindow.focus();
  } else if (sameOrigin && existingWindow) {
    existingWindow.focus();
  } else {
    console.log(2222222);
    // 如果没有，则打开新窗口并记录 URL
    // 创建一个 <a> 元素用于解析 URL 以获取 origin
    const urlAnchor = document.createElement('a');
    urlAnchor.href = url;

    console.log('同源吗', urlAnchor.origin === window.location.origin);
    // 检查是否同源
    if (urlAnchor.origin === window.location.origin) {
      localStorage.setItem(sameOriginKey, JSON.stringify(true)); // 为当前 URL 存储同源标志
    }
    const newWindow = window.open(url, windowName);
    if (newWindow) {
      openedWindows[windowName] = url;
      // 更新 localStorage 中的已打开窗口记录
      localStorage.setItem('openedWindows', JSON.stringify(openedWindows));
    }
  }
}
