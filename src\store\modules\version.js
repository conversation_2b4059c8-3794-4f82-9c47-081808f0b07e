const useVersionStore = defineStore("versionStore", {
  state: () => ({
    isMobile:false,
    isLowBrowser: false,
  }),
  getters: {
    // 是否是低版本浏览器
    getIsLowBrowser(state) {
      return state.isLowBrowser;
    },
    // 是否是移动端
    getIsMobile(state) {
        return state.isMobile;
      },
  },
  actions: {
    setLowBrowser(status) {
      this.isLowBrowser = status;
    },
    setIsMobile(status) {
        this.isMobile = status;
      },
  },
});
export default useVersionStore;
