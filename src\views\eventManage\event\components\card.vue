<template>
  <div class="card-container flex justify-between text-[12px]">
    <div class="h-[125px] flex flex-col justify-between">
      <div class="flex flex-col gap-y-[10px]">
        <div class="flex gap-x-[10px]">
          <div>
            <!-- <svg-icon icon-class="" /> -->
            图标
          </div>
          <div class="text-[#333333]">
            {{ content?.title || "2024 特大交通事故事件" }}
          </div>
        </div>
        <span
          class="w-[50px] p-[2px] flex flex-center"
          :style="{
            backgroundColor: content?.status?.bgColor,
            color: content?.status?.color,
          }"
          >{{ content?.status?.label || "进行中" }}</span
        >
      </div>

      <div class="flex flex-col gap-y-[10px] text-[#333333]">
        <span>创建时间</span>
        <span>{{ content?.createTime || "2024-01-07 18:20" }}</span>
      </div>
    </div>

    <div class="h-[125px] flex flex-col justify-between">
      <div class="flex flex-col gap-y-[10px]">
        <div class="flex justify-end">
          <el-dropdown
            trigger="click"
            placement="bottom"
            @command="handleCommand"
          >
            <el-icon class="cursor-pointer"><MoreFilled /></el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="'pause'">暂停事件</el-dropdown-item>
                <el-dropdown-item :command="'delete'"
                  >删除事件</el-dropdown-item
                >
                <el-dropdown-item :command="'edit'">编辑事件</el-dropdown-item>
                <el-dropdown-item :command="'finish'"
                  >结束事件</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <el-button
          type="primary"
          link
          @click="toSituationAware"
          class="text-[12px]"
          >态势感知</el-button
        >
      </div>

      <div class="flex flex-col gap-y-[10px] text-[#333333]">
        <span>创建单位</span>
        <span>{{ content?.createUnit || "xxx支队" }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
// import { deleteEvent, pauseEvent, finishEvent } from "@/api/eventManagement";

const props = defineProps({
  content: {
    type: Object,
    default: () => {},
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(["refreshList", "handleEdit"]);

const confirm = (api, params, txt) => {
  proxy.$modal
    .confirm(`是否确认${txt}该事件?`)
    .then(function () {
      return api(params);
    })
    .then(() => {
      emit("refreshList");
      proxy.$modal.msgSuccess(`${txt}成功`);
    })
    .catch(() => {});
};

const handlePause = () => {
  // confirm(pauseEvent, props.content?.id, "暂停");
};

const handleDelete = () => {
  // confirm(deleteEvent, props.content?.id, "删除");
};

const handleEdit = () => {
  emit("handleEdit", props?.content);
};

const handleFinish = () => {
  // confirm(finishEvent, props.content?.id, "结束");
};

function handleCommand(command) {
  switch (command) {
    case "pause":
      handlePause();
      break;
    case "delete":
      handleDelete();
      break;
    case "edit":
      handleEdit();
      break;
    case "finish":
      handleFinish();
      break;
    default:
      break;
  }
}

const toSituationAware = () => {
  console.log("---->toSituationAware");
  proxy.$router.push({
    path: "/situationAware",
    query: { eventId: props.content?.id },
  });
};
</script>

<style lang="scss" scoped>
.card-container {
  width: 100%;
  height: 180px;
  border: #f2f2f2 solid 1px;
  margin: 2px;
  border-radius: 5px;
  padding: 20px 10px;
}
</style>
