<template>
  <div class="app-container">
    <!-- 表单 -->
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="帐号昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入帐号昵称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="微信群" prop="groupId">
        <el-select
          v-model="queryParams.groupId"
          placeholder="微信群"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in wechatGroupList"
            :key="dict.id"
            :label="dict.groupName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="工作号" prop="workAccountId">
        <el-select
          v-model="queryParams.workAccountId"
          placeholder="工作号"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in workAccountList"
            :key="dict.id"
            :label="dict.workAccount"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="open = true"
          :disabled="!ids.length"
        >
          批量分配</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="resetQuery"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="accountList"
      row-key="accoundId"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        prop="nickName"
        label="帐号昵称"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column prop="groupName" label="所属微信群"></el-table-column>
      <el-table-column
        label="选择单位"
        :show-overflow-tooltip="true"
        align="left"
      >
        <template #default="scope">
          <EditWorkUnit
            v-model="scope.row.deptId"
            @handleSelectChange="
              handleUnitChange(scope.row.id, scope.row.deptId)
            "
            style="width: 40%"
          ></EditWorkUnit>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 批量分配的弹框 -->
    <el-dialog title="选择单位" v-model="open" width="500px" append-to-body>
      <div class="dialog-wapper">
        <EditWorkUnit
          v-model="batchSelectedDepyId"
          style="width: 100%"
        ></EditWorkUnit>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Menu">
import {
  getWechatGroupInfo,
  getWorkAccountInfo,
  getAccountList,
  bindWechatDept,
} from "@/api/system/submitAccount";
import EditWorkUnit from "@/views/publicOpinionManage/components/EditWorkUnit.vue";

const { proxy } = getCurrentInstance();

const wechatGroupList = ref([]);
const workAccountList = ref([]);
const accountList = ref([]);
const ids = ref([]);
const total = ref(0);
const open = ref(false);

const batchSelectedDepyId = ref(undefined);

const loading = ref(true);
const showSearch = ref(true);

const data = reactive({
  queryParams: {
    nickName: undefined,
    workAccountId: undefined,
    groupId: undefined,
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);

/** 微信群列表 */
async function getWechatGroupList() {
  const res = await getWechatGroupInfo();
  wechatGroupList.value = res.data;
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
}

/** 工作号列表 */
async function getWorkAccountList() {
  const res = await getWorkAccountInfo();
  workAccountList.value = res.data;
  queryParams.value.workAccountId = workAccountList.value[0].id;
}

/** 查询报送账号列表 */
async function getList() {
  loading.value = true;
  const res = await getAccountList(queryParams.value);
  accountList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 分配单位操作 */
async function handleUnitChange(id, deptId) {
  const res = await bindWechatDept({
    ids: [id],
    deptId: deptId,
  });

  if (res.code === 200) {
    proxy.$modal.msgSuccess("操作成功");
    handleQuery();
  }
}

const cancel = () => {
  open.value = false;
  batchSelectedDepyId.value = undefined;
};

/** 批量分配单位 */
async function submitForm() {
  const res = await bindWechatDept({
    ids: ids.value,
    deptId: batchSelectedDepyId.value,
  });

  if (res.code === 200) {
    proxy.$modal.msgSuccess("操作成功");
    handleQuery();
    open.value = false;
    batchSelectedDepyId.value = undefined;
  }
}

onMounted(async () => {
  await getWechatGroupList();
  await getWorkAccountList();
  await getList();
});
</script>

<style lang="scss" scoped>
.dialog-wapper {
  width: 100%;
  height: 100%;

  padding-left: 10px;

  display: flex;
  justify-content: start;
  align-items: center;
}
.dialog-footer {
}
</style>
