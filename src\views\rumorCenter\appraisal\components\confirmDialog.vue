<template>
  <div>
    <el-dialog
      v-model="appraisalVisible"
      title="谣言鉴定"
      :width="'700'"
      @close="closeDialog"
    >
      <el-form
        ref="appraisalFormRef"
        label-position="top"
        :label-width="'150px'"
        :model="appraisalForm"
        :rules="APPRAISAL_RULES"
        status-icon
        class="custom-form !block"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="贴文情况是否属实" prop="isTrue">
              <el-radio-group v-model="appraisalForm.isTrue">
                <el-radio
                  v-for="i in APPRAISAL_IS_TRUE_OPTIONS"
                  :key="i.value"
                  :label="i.label"
                  :value="i.value"
                >
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="事情真相" prop="truthDescription">
              <el-input
                v-model="appraisalForm.truthDescription"
                show-word-limit
                maxlength="500"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 6 }"
                placeholder="请输入事情真相"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="confirmAppraisal"> 确定 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  APPRAISAL_RULES,
  APPRAISAL_IS_TRUE_OPTIONS,
} from "../../config/index.js";
import { appraisalRumor } from "@/api/rumorCenter/appraisal.js";

const { proxy } = getCurrentInstance();
const appraisalVisible = ref(false);
const appraisalFormRef = ref(null);
const currentAppraisalRow = ref(null);
const appraisalForm = reactive({
  isTrue: "0",
  truthDescription: "",
});

const openDialog = (row) => {
  currentAppraisalRow.value = row;
  appraisalVisible.value = true;
};

const closeDialog = () => {
  appraisalFormRef.value.resetFields();
  appraisalVisible.value = false;
};

const confirmAppraisal = () => {
  appraisalFormRef.value.validate(async (valid) => {
    if (valid) {
      const params = {
        id: currentAppraisalRow.value.rumorOpinion?.id,
        opinionId: currentAppraisalRow.value.id,
        rumorFlag: appraisalForm.isTrue,
        truthContent: appraisalForm.truthDescription,
      };
      const res = await appraisalRumor(params);
      if (res.code === 200) {
        proxy.$modal.msgSuccess(res.msg);
        proxy.$emit("refreshData");
        closeDialog();
      }
    }
  });
};
defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
:deep(.custom-form) {
  .el-form-item--default {
    margin-bottom: 12px;
  }

  .el-form-item__label,
  .el-radio__label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
  }
}

:deep(.el-dialog) {
  .el-dialog__title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
  }
}
</style>
