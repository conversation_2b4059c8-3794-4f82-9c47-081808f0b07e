<!-- 应用框封装 -->
<template lang="">
  <div class="box">
    <div class="title">{{ title }}</div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
});
</script>
<style lang="scss" scoped>
.box {
  height: fit-content;
  background-image: url("@/assets/home/<USER>");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 0px 9px;
  height: 25%;
  min-height: 180px;
  .title {
    width: 100%;
    font-size: 16px;
    height: 55px;
    line-height: 55px;
    text-indent: 35px;
    text-align: left;
    font-family: "PangMenZhengDao";
  }
  .content {
    // 应该减50px 但是图片有些padding 所以加了10px
    height: calc(100% - 60px);
    display: flex;
    align-items: center;
    gap: 30px;
    max-width: 830px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 6px !important;
      height: 6px !important;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #003366;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
      background-color: #01030823;
    }
  }
}
</style>
