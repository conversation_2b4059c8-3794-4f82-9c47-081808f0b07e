<template>
  <div
    v-loading="loading"
    v-infinite-scroll="loadData"
    :infinite-scroll-disabled="loading || finished"
    class="infiniteScrollList-container flex flex-col gap-[5px] pr-[10px]"
  >
    <template v-if="tableData.length">
      <template v-for="item in tableData" :key="item.id">
        <slot :row="item"></slot>
      </template>
    </template>
    <div v-else class="h-full flex-center">
      <img class="w-[300px]" src="@/assets/images/no-data.svg" alt="无数据" />
    </div>
  </div>
</template>

<script setup name="InfiniteScrollList">
import { debounce } from "lodash";

const props = defineProps({
  // 获取数据的接口函数
  getList: {
    type: Function,
    default: () => {},
  },
  // 获取数据的入参
  params: {
    type: Object,
    default: () => ({}),
  },
  // 处理接口返回数据的结构
  getListAfter: {
    type: Function,
    default: () => {},
  },
});

const currentPage = ref(1); // 当前页
const pageSize = ref(10); // 页大小
const loading = ref(false); // 加载状态
const tableData = ref([]); // 表格数据
const total = ref(0); // 总条数
const finished = ref(false); // 数据是否加载完

// 参数信息
const paramsInfo = computed(() => ({
  ...props.params,
  pageSize: pageSize.value,
}));

/**
 * 加载数据
 */
const loadData = async () => {
  loading.value = true;
  try {
    const param = {
      ...props.params,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };
    const res = await props.getList(param);
    let t = res.total || res.data.total;
    let records = res.rows || res.records || res.data.records || res.data.rows;
    records = props.getListAfter(records) || records;
    tableData.value = [...tableData.value, ...records];
    total.value = t;

    if (tableData.value.length >= total.value) {
      finished.value = true;
    } else {
      currentPage.value++;
    }
    console.log("是否加载完：", finished.value);
  } catch (error) {
    console.error("加载时出错：", error);
    finished.value = true;
  } finally {
    loading.value = false;
  }
};

/**
 * 刷新数据，同时切到第一页
 */
const onRefresh = () => {
  currentPage.value = 1;
  tableData.value = [];
  total.value = 0;
  finished.value = false;
  loadData();
};

/**
 * 刷新当前页数据
 */
const onRefreshCurrent = () => {
  loading.value = true;
  const param = {
    ...props.params,
    pageNum: currentPage.value,
    pageSize: pageSize.value,
  };
  props.getList(param).then(res => {
    tableData.value.forEach((item) => {
      res.rows.forEach(resItem => {
        if (item.id === resItem.id) {
          item = resItem
        }
      })
    })
  }).finally(() => {
    loading.value = false;
  })
};

/**
 * 组件处于激活状态，重新加载数据
 */
onActivated(() => loadData());

/**
 * 参数变化，跳到第一页
 */
const onRefreshDebounce = debounce(onRefresh, 300);
watch(paramsInfo, () => {
  onRefreshDebounce();
});

onMounted(() => {
  loadData();
});
// 定义暴露出去的数据与函数
defineExpose({
  tableData,
  onRefresh,
  onRefreshCurrent
});
</script>

<style lang="scss" scoped>
.infiniteScrollList-container {
  width: 100%;
  height: 100%;
  // overflow: auto;
  overflow-y: scroll;
  overflow-x: hidden;
}
</style>
