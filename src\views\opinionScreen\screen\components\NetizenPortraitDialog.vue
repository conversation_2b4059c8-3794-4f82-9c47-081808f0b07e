<template>
  <el-dialog
    class="dialog-container"
    v-model="portraitVisible"
    width="60vw"
    @close="onClose"
  >
    <template #header>
      <span class="addTitle">重点网民画像</span>
    </template>

    <div class="netizen-portrait-container">
      <!-- 头像、姓名、性别 -->
      <section class="portrait-info">
        <img
          :src="userBasicInfo[0].value"
          alt=""
          class="portrait-info-avatar"
        />
        <div class="portrait-info-desc">
          <OverflowTooltip :content="userBasicInfo[1].value">
            <div class="info-desc-name">{{ userBasicInfo[1].value }}</div>
          </OverflowTooltip>
          <div class="info-desc-gender">
            性别：{{ getDict(userBasicInfo[2].value, sys_user_sex) }}
          </div>
        </div>
      </section>
      <!-- 人员上网活动 -->
      <section class="personnel-online-activities">
        <div class="netizen-portrait-label">人员上网活动</div>

        <PersonnelOnlineActivitiesTable
          :table-columns="PERSONNEL_ONLINE_ACTIVITIES_TABLE_COLUMNS"
          :table-data="tableData"
          :table-max-height="pxToVw(200)"
        />
      </section>

      <!-- 数据统计 -->
      <section class="data-statistics">
        <div class="netizen-portrait-label">数据统计</div>
        <div class="data-statistics-list">
          <div
            v-for="(item, index) in dataStatistics"
            :key="index"
            class="data-statistics-item"
          >
            <div class="statistics-item-label">{{ item.label }}</div>
            <div class="statistics-item-value">{{ item.value }}</div>
          </div>
        </div>
      </section>

      <!-- 虚拟身份信息 -->
      <section class="virtual-identity-info">
        <div class="netizen-portrait-label">虚拟身份信息</div>
        <div class="virtual-identity-info-list">
          <div
            v-for="(item, index) in virtualIdentityInfo"
            :key="index"
            class="virtual-identity-info-item"
          >
            <img :src="item.icon" alt="" class="identity-info-item-icon" />
            <OverflowTooltip :content="item.label">
              <div class="info-item-label">{{ item.label }}</div>
            </OverflowTooltip>
            <OverflowTooltip :content="item.value">
              <div class="info-item-value">{{ item.value }}</div>
            </OverflowTooltip>
          </div>
        </div>
      </section>
    </div>
  </el-dialog>
</template>

<script setup>
import avatarIcon from "@/assets/images/avatar.svg";
import PersonnelOnlineActivitiesTable from "./PersonnelOnlineActivitiesTable.vue";
import { PERSONNEL_ONLINE_ACTIVITIES_TABLE_COLUMNS } from "../config/tableColumns";
import {
  getPostList,
  getPostRecord,
  getPersonDetailInfo,
} from "@/api/networkuser/user";
import { pxToVw } from "@/utils/index.js";
import { getDict } from "@/utils/dict";

const props = defineProps({});

const { proxy } = getCurrentInstance();
const portraitVisible = ref(false);
const { sys_user_sex } = proxy.useDict("sys_user_sex");
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const userBasicInfo = ref([
  {
    key: "avatar",
    value: undefined,
  },
  {
    key: "name",
    value: undefined,
  },
  {
    key: "sex",
    value: undefined,
  },
]); // 用户基本信息
const tableData = ref([]); // 人员上网活动
const dataStatistics = ref([
  {
    label: "累计发帖",
    key: "total",
    value: 0,
  },
  {
    label: "本年发帖",
    key: "thisYear",
    value: 0,
  },

  {
    label: "本月发帖",
    key: "thisMonth",
    value: 0,
  },
]); // 数据统计
const virtualIdentityInfo = ref([]); // 虚拟身份信息

/**
 * 获取人员上网活动
 * @param userId 用户id
 */
const getTableData = async (userId) => {
  const res = await getPostList({ userId });
  tableData.value = res.rows;
};

/**
 * 获取人员详情
 * @param userId 用户id
 */
const getPersonDetailInfoData = async (userId) => {
  const res = await getPersonDetailInfo(userId);
  // console.log("getPersonDetailInfoData", res);
  userBasicInfo.value.forEach((item) => {
    item.value = res.data[item.key];
  });
  virtualIdentityInfo.value = res.data?.internetUserAccountList.map((item) => {
    return {
      icon: avatarIcon,
      label: item.nickName,
      value: item.type,
    };
  });
};
/**
 * 获取数据统计
 * @param userId 用户id
 */
const getPostRecordData = async (userId) => {
  const res = await getPostRecord(userId);
  dataStatistics.value.forEach((item) => {
    item.value = res.data[item.key];
  });
};

// 弹框打开事件
const openDialog = async (row) => {
  // console.log("row", row);

  await getTableData(row.internetUserId);
  await getPostRecordData(row.internetUserId);
  await getPersonDetailInfoData(row.internetUserId);
  portraitVisible.value = true;
};

/**
 * 取消
 */
function onClose() {
  portraitVisible.value = false;
}

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;

.dialog-container {
  .addTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: px2vw(30);
    color: #feffff;
    line-height: px2vw(17);
    text-align: center;
    font-style: normal;
  }

  .netizen-portrait-container {
    padding: px2vw(15) px2vw(40);

    .netizen-portrait-label {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: px2vw(23);
      color: #2fb1cc;
      line-height: px2vw(17);

      margin-bottom: px2vw(25);
    }

    .portrait-info {
      width: 100%;
      display: flex;
      gap: px2vw(20);

      margin-bottom: px2vw(40);

      .portrait-info-avatar {
        width: px2vw(60);
        height: px2vw(60);
        border-radius: 50%;
      }

      .portrait-info-desc {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: start;

        .info-desc-name {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: px2vw(18);
          color: #feffff;
          line-height: px2vw(17);
          text-align: left;

          width: px2vw(100);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .info-desc-gender {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: px2vw(18);
          color: #feffff;
          line-height: px2vw(17);
          text-align: center;
        }
      }
    }

    .personnel-online-activities {
      width: 100%;

      margin-bottom: px2vw(25);
    }

    .data-statistics {
      width: 100%;

      margin-bottom: px2vw(25);

      .data-statistics-list {
        display: flex;
        gap: px2vw(280);

        .data-statistics-item {
          display: flex;
          gap: px2vw(20);
          flex-direction: column;
          align-items: center;

          .statistics-item-label {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: px2vw(18);
            color: #feffff;
          }

          .statistics-item-value {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: px2vw(15);
            color: #feffff;
          }
        }
      }
    }

    .virtual-identity-info {
      width: 100%;

      margin-bottom: px2vw(25);

      .virtual-identity-info-list {
        display: flex;
        flex-wrap: wrap;
        gap: px2vw(80);

        .virtual-identity-info-item {
          display: flex;
          flex-direction: column;
          gap: px2vw(10);
          align-items: center;

          .identity-info-item-icon {
            width: px2vw(50);
            height: px2vw(50);
          }

          .info-item-label {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: px2vw(18);
            color: #feffff;
            text-align: center;

            width: px2vw(100);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .info-item-value {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: px2vw(15);
            color: #68818f;
            text-align: center;

            width: px2vw(100);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
@use "@/assets/styles/func.scss" as *;

.el-dialog {
  background: transparent;
  background-image: url("@/assets/screen/screen-portrait-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  // height: px2vw(825);

  .el-dialog__header {
    text-align: center;
    padding-top: px2vw(30);
    margin-left: px2vw(10);

    .el-dialog__headerbtn {
      top: px2vw(10);
      right: px2vw(10);
    }
  }

  .el-dialog__body {
    padding-right: 10px;

    // 浏览器滚动条
    &::-webkit-scrollbar {
      width: 0;
    }
  }
}
</style>
