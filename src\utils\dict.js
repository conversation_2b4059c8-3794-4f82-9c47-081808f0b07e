import useDictStore from "@/store/modules/dict";
import { getDicts } from "@/api/system/dict/data";

/**
 * 获取字典数据
 */
export function useDict(...args) {
  const res = ref({});
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = [];
      const dicts = useDictStore().getDict(dictType);
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        getDicts(dictType).then((resp) => {
          res.value[dictType] = resp.data.map((p) => {
            let other = {};
            if (isJSONParseable(p.remark)) {
              other = JSON.parse(p.remark);
            } else {
              other = { remark: p.remark };
            }
            return {
              label: p.dictLabel,
              value: p.dictValue,
              elTagType: p.listClass,
              elTagClass: p.cssClass,
              ...other,
            };
          });
          useDictStore().setDict(dictType, res.value[dictType]);
        });
      }
    });
    return toRefs(res.value);
  })();
}
function isJSONParseable(str) {
  try {
    let res = JSON.parse(str);
    if (typeof res == "object") {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

/**
 * @description 解析字典
 * @param {*} dictValue 字典值
 * @param {*} dictList
 * @return {string}
 * */
export function getDict(dictValue, dictList) {
  return dictList?.find((item) => item.value == dictValue)?.label || "-";
}
