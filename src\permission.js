import router from './router';
import { ElMessage } from 'element-plus';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken,removeToken } from '@/utils/auth';
import { isHttp } from '@/utils/validate';
import { isRelogin } from '@/utils/request';
import useUserStore from '@/store/modules/user';
import useSettingsStore from '@/store/modules/settings';
import usePermissionStore from '@/store/modules/permission';
import useVersionStore from '@/store/modules/version';
import { usePoManageStore } from "@/store/modules/poManage.js";

NProgress.configure({ showSpinner: false });

const whiteList = ['/login', '/register','/mobile-not-supported','/low-browser'];

router.beforeEach((to, from, next) => {
  NProgress.start();
  const versionStore = useVersionStore();

  // 检查是否是移动端或低版本浏览器，如果是则跳转到相应的页面
  if (versionStore.isMobile && to.path !== '/mobile-not-supported') {
    next('/mobile-not-supported');
    NProgress.done();
    return;
  }
  if (versionStore.isLowBrowser && to.path !== '/low-browser') {
    next('/low-browser');
    NProgress.done();
    return;
  }

  const otherSystem = to.query.otherSystem === 'true' ?? false;
  // console.log('otherSystem: ', otherSystem);

  if (otherSystem) {
    removeToken()
    router.replace('/login');
    NProgress.done();
    return;
  } else {
    if (getToken()) {
      to.meta.title && useSettingsStore().setTitle(to.meta.title);
      /* has token*/
      if (to.path === '/login') {
        next({ path: '/' });
        NProgress.done();
      } else if (whiteList.indexOf(to.path) !== -1) {
        next();
      } else {
        if (useUserStore().roles.length === 0) {
          isRelogin.show = true;
          // 判断当前用户是否已拉取完user_info信息
          useUserStore()
            .getInfo()
            .then(async () => {
              isRelogin.show = false;
              await usePoManageStore().judgeIsWangAn() // 判断是否是网安端
              usePermissionStore()
                .generateRoutes()
                .then((accessRoutes) => {
                  // 根据roles权限生成可访问的路由表
                  accessRoutes.forEach((route) => {
                    if (!isHttp(route.path)) {
                      router.addRoute(route); // 动态添加可访问路由表
                    }
                  });
                  next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
                });
            })
            .catch((err) => {
              useUserStore()
                .logOut()
                .then(() => {
                  ElMessage.error(err);
                  next({ path: '/' });
                });
            });
        } else {
          next();
        }
      }
    } else {
      // 没有token
      if (whiteList.indexOf(to.path) !== -1) {
        // 在免登录白名单，直接进入
        next();
      } else {
        next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
        NProgress.done();
      }
    }
  }

  // console.log('getToken(): ', getToken());
});

router.afterEach(() => {
  NProgress.done();
});
