<template>
  <div class="w-full flex flex-col" ref="tableContainerRef">
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :loading="tableLoading"
      :show-default-btn="false"
      :show-operation-column="true"
      :operation-column="operationColumnWidth"
      no-padding
      :max-height="tableHeight"
      :highlight-current-row="false"
      :row-class-name="rowClassName"
      @handle-selection-change="handleSelectionChange"
      @handle-row-click="handleRowClick"
    >
      <template #poId="{ row }">
        <img
          v-if="articleIsInvalid(row.isInvalid)"
          src="@/assets/images/poManage/invalid.svg"
          alt=""
          class="invalidItemWrapper"
        />
        <img
          v-if="
            articleIsRumor(row.rumorStatus) && !articleIsInvalid(row.isInvalid)
          "
          src="@/assets/images/rumor-bg.svg"
          alt=""
          class="invalidItemWrapper"
        />
        <!--        <div v-if="articleIsInvalid(row.isInvalid)" class="invalidBtn mb-[6px]">无效</div>-->
        <div class="whitespace-normal break-all">{{ row.poId }}</div>
      </template>

      <template #isFirst="{ row }">
        <div>{{ firstLabel(row.isFirst) }}</div>
      </template>

      <template #poName="{ row }">
        <div class="poContentWrapper">{{ row.poName || "-" }}</div>
      </template>

      <!-- 内容可能为文本/图片 -->
      <template #poContent="{ row }">
        <ImagePreview
          v-if="contentIsImg(row.poContent)"
          :src="row.poContent"
          :width="100"
          :height="100"
          @click.stop
        />
        <div v-else class="poContentWrapper">{{ row.poContent || "-" }}</div>
      </template>

      <template #poLink="{ row }">
        <div v-if="row.poLink" class="tagWrapper">
          <img
            src="@/assets/images/poManage/link.png"
            alt="link"
            class="w-[10px] h-[10px] mr-[4px]"
          />
          <el-link
            class="linkText"
            :underline="false"
            :href="row.poLink"
            target="blank"
            @click.stop
            >{{ row.poLink }}</el-link
          >
        </div>
        <span v-else>-</span>
      </template>

      <template #poEvent="{ row }">
        <div v-if="Array.isArray(row.poEvent)" class="flex gap-[5px] flex-wrap">
          <div v-for="item in row.poEvent" :key="item.id" class="tagWrapper">
            <img
              src="@/assets/images/poManage/event.png"
              alt="event"
              class="w-[10px] h-[10px] mr-[4px]"
            />
            <div class="tagText">
              <div class="text-flex">{{ item.name }}</div>
            </div>
          </div>
        </div>
        <span v-else>-</span>
      </template>

      <template #isSensitive="{ row }">
        {{ isSensitiveF(row.isSensitive) }}
      </template>

      <template #workUnit="{ row }">
        <div @click.stop>
          <el-dropdown
            trigger="click"
            v-if="
              row.workUnit !== '-' &&
              row.linkUrlCount &&
              Number(row.linkUrlCount) > 0
            "
            @visible-change="(visible) => handleClickDropdown(visible, row)"
          >
            <template #default>
              <div class="mr-[4px] text-[14px] flex items-center">
                {{ row.workUnit }}<el-icon><CaretBottom /></el-icon>
              </div>
            </template>

            <template #dropdown>
              <el-scrollbar max-height="320px">
                <div class="p-[20px] flex flex-col gap-y-[18px]">
                  <section
                    class="flex flex-col gap-y-[4px]"
                    v-for="(item, index) in linkReportRecords"
                    :key="index"
                  >
                    <div class="subDeptName">
                      {{ item.deptName }}
                    </div>
                    <div class="createTime">{{ item.reporterTime }}</div>
                  </section>
                </div>
              </el-scrollbar>
            </template>
          </el-dropdown>
          <span v-else class="mr-[4px]">{{ row.workUnit }} </span>
        </div>
      </template>

      <!-- 媒体类型 -->
      <template #poMediaType="{ row }">
        <span>{{ getDict(row.poMediaType?.toString(), media_type) }}</span>
      </template>

      <!-- 网安网端需要跳转到网名库详情，互联网端不需要 -->
      <template #netizenNickname="{ row }">
        <span
          v-if="poManageStore.isWangAn && row.netizenAccountId"
          class="text-[#0052D9]"
          @click.stop="goNetPersonDetail(row)"
        >
          {{ row.netizenNickname }}
        </span>
        <span v-else>{{ row.netizenNickname }}</span>
      </template>
      <template #publicTime="{ row }">
        <div class="whitespace-normal break-all">{{ row.publicTime }}</div>
      </template>

      <template #poImg="{ row }">
        <ImagePreview
          v-if="Array.isArray(row.poImg)"
          :src="row.poImg[0]"
          :width="100"
          :height="100"
          @click.stop
        />
        <span v-else>-</span>
      </template>

      <template #articleStatus="{ row }">
        <span
          class="truncate"
          :style="articleStatusF(row.articleStatus).style"
          >{{ articleStatusF(row.articleStatus).text }}</span
        >
      </template>
      <template #handleStatus="{ row }">
        <div class="flex-center">
          <div
            class="truncate statusClass"
            :style="handleStatusF(row.handleStatus).style"
          >
            {{ handleStatusF(row.handleStatus).text }}
          </div>
        </div>
      </template>

      <template #createTime="{ row }">
        <div class="whitespace-normal break-all">{{ row.createTime }}</div>
      </template>

      <!-- 赋分 -->
      <template #score="{ row }">
        <span class="text-[#03B615] truncate">{{ row.score }}</span>
      </template>

      <!-- 处理状态 -->
      <template #handleStatus2="{ row }">
        <span class="truncate" :style="handleStatus2F(row.handleStatus2).style">
          {{ handleStatus2F(row.handleStatus2).text }}
        </span>
      </template>

      <template #operation="{ row }">
        <slot name="operation" :row="row">
          <div
            class="operationWrapper"
            v-if="
              !articleIsDel(row.articleStatus) &&
              !articleIsInvalid(row.isInvalid)
            "
          >
            <el-button
              style="color: #0052d9"
              link
              @click.stop="$emit('showDialog', TRANSFER_TYPE.VIEW, row)"
              >{{ TRANSFER_TYPE.VIEW }}</el-button
            >
            <el-button
              style="color: #0052d9"
              link
              @click.stop="$emit('showDialog', TRANSFER_TYPE.CHECK, row)"
              >{{ TRANSFER_TYPE.CHECK }}</el-button
            >
            <el-button
              style="color: #0052d9"
              link
              @click.stop="$emit('showDialog', TRANSFER_TYPE.REPORT, row)"
              >{{ TRANSFER_TYPE.REPORT }}</el-button
            >
            <el-button
              style="color: #0052d9"
              link
              @click.stop="$emit('showDialog', TRANSFER_TYPE.RESOLVE, row)"
              >{{ TRANSFER_TYPE.RESOLVE }}</el-button
            >
            <el-button
              style="color: #0052d9"
              link
              @click.stop="$emit('showDialog', TRANSFER_TYPE.REMOVE, row)"
              >{{ TRANSFER_TYPE.REMOVE }}</el-button
            >
          </div>
          <div v-else></div>
        </slot>
      </template>
    </CommonTable>

    <div
      class="flex justify-between items-center py-[20px]"
      style="margin: 10px"
    >
      <!-- 批量操作 -->
      <div class="flex items-center text-[14px]">
        <span class="mr-[30px]" v-if="showMulSelect"
          >已选择{{ selectedMemberCount }}项</span
        >
      </div>
      <!-- 分页 -->
      <pagination
        v-model:page="pageObj.pageNum"
        v-model:limit="pageObj.pageSize"
        :total="pageObj.total"
        :page-sizes="[15, 20, 30, 50]"
        @pagination="handlePagination"
      />
    </div>
  </div>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
import { useFetchTableData } from "@/hooks/useFetchTableData";
import {
  IS_FIRST_STATUS,
  SENSITIVE_STATUS_LIST,
  INVALID_STATUS,
  RUMOR_STATUS,
  ARCTICLE_STATUS,
  ARCTICLE_STATUS_LIST,
  HANDLE_STATUS_LIST,
  HANDLE2_STATUS_LIST,
} from "../config/constant.js";
import { TRANSFER_TYPE } from "../config/mapRel";
import { disablePoInfo, getLinkReportRecords } from "@/api/poManage/poInfo";
import { usePoManageStore } from "@/store/modules/poManage.js";
import { IS_FIRST_STATUS_LIST } from "../../publicOpinionManage/config/constant.js";
import { getDict } from "@/utils/dict";
// import {onMounted, onUnmounted} from "@vue/runtime-core";

const props = defineProps({
  // 获取数据的接口相关信息
  interfaceInfo: {
    type: Object,
    default: () => ({}),
  },
  // 表格列配置
  tableColumns: {
    type: Array,
    default: () => [],
  },
  // 操作栏宽度
  operationColumnWidth: {
    type: Number,
    default: 200,
  },
  // 是否展示多选列
  showMulSelect: {
    type: Boolean,
    default: true,
  },
  // 表格最大高度
  tableMaxHeight: {
    type: Number,
    default: 580,
  },
});

const linkReportRecords = ref([]);
const emit = defineEmits([
  "mulSelect",
  "showDrawer",
  "showDialog",
  "refreshData",
]);

const { proxy } = getCurrentInstance();
const { media_type } = proxy.useDict("media_type"); // 媒体类型

const router = useRouter();
const { tableData, tableLoading, pageObj, getTableData } = useFetchTableData(
  props.interfaceInfo
);
const poManageStore = usePoManageStore();

const selectedMemberList = ref([]); // 已选人员信息
const selectedMemberCount = computed(() => selectedMemberList.value.length); // 已选个数
const tableContainerRef = ref();
const tableHeight = ref(0); // 表格高度
const tableHeightF = computed(() =>
  props.tableMaxHeight ? props.tableMaxHeight : tableHeight.value
); // 兼容固定高度和自适应高度

const firstLabel = computed(
  () => (val) =>
    IS_FIRST_STATUS_LIST.find((ele) => ele.value === val)?.label || "-"
);
const isSensitiveF = computed(
  () => (val) =>
    SENSITIVE_STATUS_LIST.find((i) => i.value === val)?.label || "-"
);
const articleStatusF = computed(() => (val) => {
  const obj = ARCTICLE_STATUS_LIST.find((i) => i.value === val);
  return { text: obj?.label || "-", style: { color: obj?.color || "#333333" } };
});
const handleStatusF = computed(() => (val) => {
  const obj = HANDLE_STATUS_LIST.find((i) => i.value === val);
  return {
    text: obj?.label || "-",
    style: { color: obj?.color || "#333333", background: obj?.bgColor },
  };
});
const articleIsDel = computed(
  () => (val) => val === ARCTICLE_STATUS.FINISH_DEL
); // 当前帖子是否已删除
const articleIsInvalid = computed(() => (val) => val === INVALID_STATUS.YES); // 当前帖子是否无效
const articleIsRumor = computed(() => (val) => val === RUMOR_STATUS.YES); // 当前帖子是否是谣言
const showAddEventF = computed(
  () => (row) =>
    props.showAddEvent &&
    !articleIsDel.value(row.articleStatus) &&
    !articleIsRumor.value(row.rumorStatus) &&
    !articleIsInvalid.value(row.isInvalid)
);

const handleStatus2F = computed(() => (val) => {
  const obj = HANDLE2_STATUS_LIST.find((i) => i.value.includes(val));
  return { text: obj?.label || "-", style: { color: obj?.color || "#333333" } };
});
const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 内容是否为图片

onMounted(() => {
  getTableHeight();
  window.addEventListener("resize", getTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

/**
 * 窗口大小改变时，重新计算表格高度
 */
function getTableHeight() {
  // tableHeight.value = tableContainerRef.value.clientHeight - 200;
  tableHeight.value = tableContainerRef.value.clientHeight - 32 - 15;
}

/**
 * 报送单位下拉点击事件
 */
async function handleClickDropdown(visible, row) {
  if (visible) {
    const res = await getLinkReportRecords(row?.id);
    if (res?.code === 200) {
      linkReportRecords.value = res?.data;
    }
  }
}

/**
 * 给特定表格行加 class
 */
function rowClassName({ row }) {
  return articleIsDel.value(row.articleStatus) ? "delItemWrapper" : "";
}

// sxbdlnmlgcp nzmbbnmbds
function handlePagination(params) {
  pageObj.value.pageNum = params.page;
  pageObj.value.pageSize = params.limit;
  proxy.$emit("refreshData", pageObj.value);
}

/**
 * 表格多选
 */
function handleSelectionChange(selectedList) {
  selectedMemberList.value = selectedList;
  emit("mulSelect", selectedList);
}

/**
 * 点击表格行
 */
function handleRowClick(row) {
  console.log(row.rumorStatus);
  if (
    articleIsDel.value(row.articleStatus) ||
    articleIsInvalid.value(row.isInvalid)
  )
    return; // 已删除、无效 的帖子不可点击
  emit("showDrawer", "view", row);
}

/**
 * 无效数据
 */
async function disableData(id) {
  const res = await disablePoInfo(id);
  if (res.code === 200) {
    proxy.$message.success("操作成功");
    emit("refreshData");
  }
}

/**
 * 跳转网名库
 */
function goNetPersonDetail({ netizenAccountId }) {
  router.push({
    name: "addNetPerson",
    params: { id: netizenAccountId, type: true },
  });
}

defineExpose({
  tableData,
  getTableData,
});
</script>

<style lang="scss" scoped>
.invalidBtn {
  width: 48px;
  height: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0052d9;
  border-radius: 5px;
  font-size: 13px;
  color: #ffffff;
}

.firstBtn {
  width: 53px;
  height: 19px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #169bd5;
  border-radius: 5px;
  font-size: 13px;
  color: #ffffff;
}

.poContentWrapper {
  white-space: normal;
  word-break: break-all;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4; /* 控制几行打点 */
  line-clamp: 4;
  overflow: hidden;
}

// 舆情内容链接
:deep(.linkText) {
  display: inline;
  color: #0052d9;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
  &:visited {
    color: #0052d9;
  }
  .el-link__inner {
    display: inline;
  }
}

// 已删除的表格行
:deep(.delItemWrapper) {
  /*background-color: #f2f2f2;*/
  opacity: 0.4;
}

.operationWrapper {
  display: flex;
  align-items: center;
  :deep(.el-dropdown) {
    margin-left: 12px;
  }
}

.tagWrapper {
  max-width: 200px;
  height: 22px;
  line-height: 22px;
  display: flex;
  align-items: center;
  padding-left: 8px;
  padding-right: 8px;
  background: #ffffff;
  box-shadow: 0px 1px 3px 0px rgba(222, 224, 232, 0.5);
  border-radius: 14px;
  border: 1px solid #e6e6e9;
}
.tagText {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #666f80;
  flex: 1;
  white-space: nowrap;
  min-width: 0;
}

.text-flex {
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.statusClass {
  width: 50px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  border-radius: 3px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
}

:deep(.pagination-container) {
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.invalidItemWrapper) {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  width: 57px;
}
</style>
