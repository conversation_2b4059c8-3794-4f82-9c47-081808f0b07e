import CryptoJS from 'crypto-js';

const secretKey = 'd4f8931bba8c9e2f4d6b5d6c8d7a9c2e345a6f7b8c9d123e567890abcdef1234';  // 密钥

// AES 加密
export function encryptTokenAES(token) {
  const encrypted = CryptoJS.AES.encrypt(token, secretKey).toString();
  return encrypted;
}

// AES 解密
export function decryptTokenAES(encryptedToken) {
  const decrypted = CryptoJS.AES.decrypt(encryptedToken, secretKey);
  const originalText = decrypted.toString(CryptoJS.enc.Utf8);
  return originalText;
}