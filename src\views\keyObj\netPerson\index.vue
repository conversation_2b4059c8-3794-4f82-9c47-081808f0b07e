<template>
  <div class="app-container">
    <div class="app-title">网民清单</div>
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名查询"
          clearable
          style="width: 240px"
          @input="debouncedHandleQuery"
          :prefix-icon="Search"
        />
      </el-form-item>
      <el-form-item prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="请输入身份证号查询"
          clearable
          style="width: 240px"
          @input="debouncedHandleQuery"
          :prefix-icon="Search"
        />
      </el-form-item>
      <!-- <el-form-item prop="company">
        <el-input
          v-model="queryParams.company"
          placeholder="请输入工作单位查询"
          clearable
          style="width: 240px"
          @input="debouncedHandleQuery"
          :prefix-icon="Search"
        />
      </el-form-item>
      <el-form-item prop="property">
        <el-input
          v-model="queryParams.property"
          placeholder="请输入人员属性查询"
          clearable
          style="width: 240px"
          @input="debouncedHandleQuery"
          :prefix-icon="Search"
        />
      </el-form-item> -->
      <!--      <el-form-item prop="examineeName">
        <el-input
            v-model="queryParams.examineeName"
            placeholder="按照风险等级查询"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>-->

      <!--      <el-form-item>-->
      <!--        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>-->
      <!--        <el-button icon="Refresh" @click="resetQuery">重置</el-button>-->
      <!--      </el-form-item>-->
    </el-form>
    <el-row :gutter="8" class="mb12" style="margin-bottom: 13px">
      <el-col :span="1.5">
        <el-button icon="Plus" @click="handleAdd" type="primary"
          >网民登记</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button plain icon="Upload" @click="handleImport">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button plain icon="Refresh" @click="handleAdd">数据同步</el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="codeRecordList"
      ref="listRef"
      size="large"
      table-layout="auto"
      style="flex-grow: 1"
      row-key="id"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="序号" width="80" fixed="left" align="center">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <template v-for="(column, index) in columns" :key="index">
        <el-table-column
          v-if="column.visible"
          :label="column.label"
          :key="column.key"
          :prop="column.key"
          :fixed="column.fixed"
          :width="column.width"
          :min-width="column.minWidth"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showTooltip"
        >
          <template #default="scope">
            <!-- 性别列特殊处理 -->
            <template v-if="column.key === 'sex'">
              {{ getSex(scope.row.sex) || "-" }}
            </template>
            <!-- 身份证号列特殊处理 -->
            <template v-else-if="column.key === 'idCard'">
              <div v-if="scope.row?.idCardEncrypted">
                <div
                  v-if="!scope.row._showIdCardFull"
                  class="flex items-center gap-[8px]"
                >
                  <span>
                    {{
                      scope.row?.idCardEncrypted?.idCardHead +
                      "********" +
                      scope.row?.idCardEncrypted?.idCardTail
                    }}</span
                  >
                  <el-icon
                    style="cursor: pointer; margin-bottom: 3px"
                    @click="showIdCardFull(scope.row)"
                  >
                    <Hide />
                  </el-icon>
                </div>
                <div v-else class="flex items-center gap-[8px]">
                  <span>{{ scope.row._idCardFull || "-" }}</span>
                  <el-icon
                    style="cursor: pointer; margin-bottom: 3px"
                    @click="hideIdCardFull(scope.row)"
                  >
                    <View />
                  </el-icon>
                </div>
              </div>
              <span v-else> - </span>
            </template>
            <!-- 其他列默认处理 -->
            <template v-else>
              {{ scope.row[column.key] || "-" }}
            </template>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" fixed="right" width="180">
        <template #default="scope">
          <el-button link type="primary" @click="handleCheck(scope.row)"
            >查看</el-button
          >
          <!--          <el-button link type="primary" @click="handleEdit(scope.row)">修改</el-button>-->
          <el-button link type="primary" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[15, 20, 30, 50, 200]"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    >
      <template #right>
        <div style="line-height: 32px; font-size: 14px">
          已选择 {{ selectionCount }} 条数据
        </div>
      </template>
    </pagination>
  </div>
</template>

<script setup>
import { Search, View, Hide } from "@element-plus/icons-vue";
import {
  getDataList,
  delNetPerson,
  decryptIdCard,
} from "@/api/networkuser/user.js";
import { debounce } from "lodash";

const router = useRouter();
const { proxy } = getCurrentInstance();
const loading = ref(false);
// 列显隐信息
const columns = ref([
  {
    key: "name",
    label: `姓名`,
    visible: true,
    fixed: true,
    minWidth: 100, // 姓名
    // width: 100,
    showTooltip: true,
  },
  {
    key: "sex",
    label: `性别`,
    align: "center",
    visible: true,
    fixed: true,
    minWidth: 100, // 性别
    // width: 100,
    showTooltip: true,
  },
  {
    key: "idCard",
    label: `身份证号`,
    visible: true,
    minWidth: 220,
    width: 400,
  }, // 身份证号最大
  {
    key: "phone",
    label: `联系方式`,
    visible: true,
    minWidth: 140, // 联系方式
    // width: 200,
    showTooltip: true,
  },
  {
    key: "createTime",
    label: `创建时间`,
    visible: true,
    minWidth: 220, // 创建时间最大
    // width: 220,
    showTooltip: true,
  },
]);
const data = reactive({
  codeRecordList: [],
  total: 0,
  queryParams: {
    name: "",
    company: "",
    property: "",
    idCard: "",
    pageNum: 1,
    pageSize: 15,
  },
});
const { codeRecordList, total, queryParams } = toRefs(data);

const debouncedHandleQuery = debounce(handleQuery, 500);

function handleAdd() {
  // router.push("/business/detail/searchDetail/" + id + '/' + name + '/1/' + item.searchContent)
  router.push("/keyObj/add/addNetPerson");
}

function handleImport() {}

function handleCheck(row) {
  router.push(`/keyObj/add/addNetPerson/${row.id}/${true}`);
  // router.push({name: "addNetPerson", params: {id: row.id, type: true},})
}
function handleEdit(row) {
  router.push(`/keyObj/add/addNetPerson/${row.id}`);
  // router.push({name: "addNetPerson", params: {id: row.id, type: null},})
}

function handleDelete(row) {
  let ids = [];
  ids.push(row.id);
  ElMessageBox.confirm("确定要删除该记录吗", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delNetPerson(ids).then((res) => {
        if (res.code === 200) {
          handleQuery();
          ElMessage({
            type: "success",
            message: res.msg,
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "danger",
        message: "删除失败",
      });
    });
}

function getList() {
  // console.log("queryParams.value", queryParams.value);
  getDataList(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        codeRecordList.value = res.rows;
        total.value = res.total;
      } else {
      }
    })
    .catch((err) => {});
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  // 清除防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }
  queryParams.value.name = "";
  queryParams.value.company = "";
  queryParams.value.property = "";
  queryParams.value.idCard = "";
  queryParams.value.pageNum = 1;
  queryParams.value.pageSize = 15;
  getList();
}

function getSex(sex) {
  if (sex === 1 || sex === "1") {
    return "男";
  } else if (sex === 0 || sex === "0") {
    return "女";
  }
  return "-";
}

function showIdCardFull(row) {
  row._showIdCardFull = true;
  if (!row._idCardFull) {
    const params = {
      idCardHead: row?.idCardEncrypted?.idCardHead,
      idCardMiddleEncrypted: row?.idCardEncrypted?.idCardMiddleEncrypted,
      idCardTail: row?.idCardEncrypted?.idCardTail,
    };
    decryptIdCard(params)
      .then((res) => {
        if (res.code === 200) {
          row._idCardFull = res.data?.idCard;
        } else {
          row._idCardFull = "解密失败";
        }
      })
      .catch(() => {
        row._idCardFull = "解密失败";
      });
  }
}
function hideIdCardFull(row) {
  row._showIdCardFull = false;
}

handleQuery();
</script>

<style scoped lang="scss">
:deep(.el-form-item) {
  margin-right: 0.564rem;
}
</style>
