<template>
  <div>
    <el-dialog
      v-model="TaskDispatchVisible"
      width="600px"
      @close="onCancel"
      destroy-on-close
    >
      <template #header>
        <span class="dialogTitle">{{ processName }}任务下发</span>
      </template>

      <el-form
        ref="dispatchFormRef"
        label-position="top"
        :model="dispatchForm"
        status-icon
        class="dispatchForm"
        :rules="TASK_DISPATCH_RULES(processName)"
      >
        <el-form-item :label="`${processName}单位`" prop="processUnit">
          <!-- <el-select
            v-model="dispatchForm.processUnit"
            :placeholder="`请选择${processName}单位`"
            style="width: 100%"
            multiple
            clearable
            filterable
          >
            <el-option
              v-for="item in unitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
          <el-tree-select
            v-model="dispatchForm.processUnit"
            :placeholder="`请选择${processName}单位`"
            lazy
            :load="loadTreeData"
            :props="{
              value: 'value',
              label: 'label',
              children: 'children',
              isLeaf: 'isLeaf',
            }"
            node-key="value"
            multiple
            :render-after-expand="false"
            show-checkbox
            searchable
          ></el-tree-select>
        </el-form-item>
        <el-form-item label="截止时间" prop="deadlineDate">
          <el-date-picker
            v-model="dispatchForm.deadlineDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择截止时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button class="cancleBtn" @click="onCancel">取消</el-button>
        <el-button class="submitBtn" type="primary" @click="onSubmit"
          >提交</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { createProcess } from "@/api/poManage/poInfo";
import { deepClone } from "@/utils";
import { TASK_DISPATCH_RULES } from "@/views/publicOpinionManage/config/formConfig";
import { getPoliceDeptUserList } from "@/api/poManage/poInfo";
import { PO_TRANSFER_LIST } from "@/views/eventManage/config/mapRel";

const emit = defineEmits(["refreshData"]);

const { proxy } = getCurrentInstance();

const dispatchFormRef = ref(null);
const dispatchForm = ref({
  processUnit: [],
  deadlineDate: "",
});
const TaskDispatchVisible = ref(false);
const processName = ref("");
const poInfo = ref({});
const unitOptions = ref([]); // 单位下拉选项

/**
 * 取消
 */
const onCancel = () => {
  dispatchFormRef.value?.resetFields(); // 重置表单
  TaskDispatchVisible.value = false;
};

// 提交实际逻辑
const onSubmit = () => {
  dispatchFormRef.value.validate(async (val) => {
    if (val) {
      // console.log("dispatchForm.value", dispatchForm.value);
      // console.log("poInfo.value", poInfo.value);

      const processObj = PO_TRANSFER_LIST.find(
        (i) => i.fontLabel === processName.value
      );

      const params = {
        reportIds: [poInfo.value.id],
        workId: poInfo.value.taskId,
        command: processObj.commandsCode,
        commandOpt: "DISPATCH",
        commandCategory: processObj.categoriesCode,
        processStep: processObj.processStepCode,
        charges: dispatchForm.value.processUnit?.map((id) => {
          return {
            userInCharge: id,
            deptInCharge: "100",
          };
        }),
        deadlineTime: dispatchForm.value.deadlineDate,
      };

      console.log("params", params);

      const res = await createProcess(params);
      if (res.code === 200) {
        proxy.$modal.msgSuccess("操作成功");
        emit("refreshData");
        onCancel();
      }
    }
  });
};

/**
 * 加载部门人员树结构数据
 */
async function loadTreeData(node, resolve) {
  try {
    const parentId =
      JSON.stringify(node.data) !== "{}" ? node.data.value : null; // Root node has deptId as '0'
    const isDept = JSON.stringify(node.data) !== "{}" ? node.data.isDept : null;

    const response = await getPoliceDeptUserList({
      deptId: parentId,
      isDept: isDept,
      deptType: "3",
    });

    const { data } = response;

    const nodes = data.map((user) => ({
      value: user.id,
      label: user.name,
      isDept: user.isDept,
      isLeaf: !user.isDept,
      // disabled: user.isDept,
      deptId: parentId,
      // children: [],
    }));

    resolve(nodes);
  } catch (error) {
    console.error("Failed to load data:", error);
    resolve([]);
  }
}

// const getPoliceStationOptions = async () => {
//   const res = await listDept({
//     deptType: "3",
//   });
//   unitOptions.value = res.data.map((item) => ({
//     label: item.deptName,
//     value: item.deptId,
//   }));
// };

/**
 * 打开弹窗
 * @param poInfo 舆情信息
 */
const openDialog = async (data) => {
  processName.value = data.processName;
  poInfo.value = deepClone(data.poInfo);
  // await getPoliceStationOptions();

  TaskDispatchVisible.value = true;
};

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;
:deep(.el-dialog) {
  padding: 24px;
  .el-dialog__header {
    padding-left: 0;
  }

  .el-dialog__body {
    min-height: px2vw(165);
  }
}

.dialogTitle {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: #1f2329;
}

:deep(.dispatchForm) {
  .el-form-item--default {
    margin-bottom: 12px;
  }

  .el-form-item__label,
  .el-radio__label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
  }
}

.cancleBtn,
.submitBtn {
  width: 80px;
  height: 32px;
  line-height: 32px;
  border-radius: 6px;

  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
}
.cancleBtn {
  border: 1px solid #cfd2d6;
  color: #1f2329;
}
.submitBtn {
  background: #0070ff;
  color: #ffffff;
}
</style>
