<template>
  <div class="app-container">
    <div v-if="addForm.id" class="back-btn" @click="backBtn">
      <img
        src="@/assets/images/back-icon.png"
        alt=""
        style="width: 42px; height: 42px; margin-right: 5px"
      />
      返回
    </div>
    <div class="body-container">
      <div class="model-container">
        <div class="app-title">
          基础信息
          <div class="btn-box">
            <el-button @click="type = false" v-if="type">修 改</el-button>
            <el-button v-if="!type" @click="cancel('basic')">取 消</el-button>
            <el-button
              v-if="!type"
              type="primary"
              @click="handleSubmit('basic')"
              >确 定</el-button
            >
          </div>
        </div>
        <div class="form-container">
          <el-form
            :model="addForm"
            :rules="addRules"
            ref="addFormRef"
            label-width="80px"
            label-position="top"
            hide-required-asterisk
            style="width: 70vw"
          >
            <el-row :gutter="38">
              <el-col :span="6" class="col-bottom">
                <el-form-item label="姓名" prop="name">
                  <el-input
                    v-model="addForm.name"
                    placeholder="请输入姓名"
                    :disabled="type"
                    v-if="!type"
                    show-word-limit
                    maxlength="10"
                  />
                  <div v-else>{{ addForm.name }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6" class="col-bottom">
                <el-form-item label="身份证号" prop="idCard">
                  <el-input
                    v-model="addForm.idCard"
                    placeholder="请输入身份证号"
                    :disabled="type"
                    v-if="!type"
                    show-word-limit
                    maxlength="18"
                  />
                  <div v-else>{{ addForm.idCard }}</div>
                  <!--                  <el-button @click="viewCode()" plain style="margin-left: 5px" v-if="type">{{ !isShowCode ? '查看' : '隐藏' }}-->
                  <!--                  </el-button>-->
                </el-form-item>
              </el-col>
              <el-col :span="6" class="col-bottom">
                <el-form-item label="性别" prop="sex">
                  <el-select
                    v-model="addForm.sex"
                    placeholder="请选择性别"
                    size="large"
                    :disabled="type"
                    v-if="!type"
                  >
                    <el-option
                      v-for="dict in sys_user_sex"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                  <div v-else>
                    {{ getDict(addForm.sex, sys_user_sex) }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6" class="col-bottom">
                <el-form-item label="联系方式" prop="phone">
                  <el-input
                    v-model="addForm.phone"
                    placeholder="请输入联系方式"
                    :disabled="type"
                    v-if="!type"
                    show-word-limit
                    maxlength="11"
                  />
                  <div v-else>{{ addForm.phone }}</div>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="4" style="float: right">
                 <el-form-item label="人脸照片" prop="avatar">
                   <div class="avatar" v-if="addForm.avatar">
                     <img :src="addForm.avatar" class="avatar"/>
                     <el-icon class="close-icon" @click="handleRemove(addForm.file)" v-if="!type">
                       <Close/>
                     </el-icon>
                   </div>
                   <el-upload
                       v-else
                       class="avatar-uploader"
                       :action="uploadFileUrl"
                       :show-file-list="false"
                       :limit="1"
                       :headers="headers"
                       :on-success="handleAvatarSuccess"
                       :before-upload="beforeAvatarUpload"
                       :on-remove="handleRemove"
                       :before-remove="beforeRemove"
                       :disabled="type"
                       accept=".png, .jpg, .jpeg, .svg"
                   >
                     <el-icon class="avatar-uploader-icon">
                       <Plus/>
                     </el-icon>
                     <template #tip>
                       <div class="el-upload__tip">
                         上传图片不超过1张，大小限制2M以内
                       </div>
                     </template>
                   </el-upload>

                 </el-form-item>
               </el-col>-->

              <el-col :span="6">
                <el-form-item label="住址信息" prop="address">
                  <el-input
                    v-model="addForm.address"
                    placeholder="请输入住址信息"
                    :disabled="type"
                    v-if="!type"
                    show-word-limit
                    maxlength="100"
                  />
                  <div v-else>{{ addForm.address }}</div>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="6">
                <el-form-item label="人员属性" prop="property">
                  <el-input
                    v-model="addForm.property"
                    placeholder="请输入人员属性"
                    :disabled="type"
                    v-if="!type"
                  />
                  <div v-else>{{ addForm.property }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="户籍" prop="hukou">
                  <el-input
                    v-model="addForm.hukou"
                    placeholder="请输入户籍"
                    :disabled="type"
                    v-if="!type"
                  />
                  <div v-else>{{ addForm.property }}</div>
                </el-form-item>
              </el-col> -->
            </el-row>
          </el-form>
          <!-- <el-form
            :model="addForm"
            :rules="addRules"
            ref="addFormRef"
            label-width="120px"
            label-position="top"
            hide-required-asterisk
            style="width: 10vw; margin-left: 38px"
          >
            <el-row :gutter="38">
              <el-col :span="24"> -->
          <!-- <el-form-item label="人脸照片" prop="avatar">
                  <div class="avatar" v-if="addForm.avatar">
                    <img :src="addForm.avatar" class="avatar" />
                    <el-icon
                      class="close-icon"
                      @click="handleRemove(addForm.file)"
                      v-if="!type"
                    >
                      <Close />
                    </el-icon>
                  </div>
                  <el-upload
                    v-else
                    class="avatar-uploader"
                    :action="uploadFileUrl"
                    :show-file-list="false"
                    :limit="1"
                    :headers="headers"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="handleRemove"
                    :before-remove="beforeRemove"
                    :disabled="type"
                    accept=".png, .jpg, .jpeg, .svg"
                  >
                    <el-icon class="avatar-uploader-icon">
                      <Plus />
                    </el-icon>
                    <template #tip>
                      <div class="el-upload__tip">
                      </div>
                    </template>
                  </el-upload>
                </el-form-item> -->
          <!-- </el-col>
            </el-row>
          </el-form> -->
          <!--<el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="80px" label-position="top" hide-required-asterisk>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="人脸照片" prop="avatar">
                  <div class="avatar" v-if="addForm.avatar">
                    <img :src="addForm.avatar" class="avatar"/>
                    <el-icon class="close-icon" @click="handleRemove(addForm.file)" v-if="!type">
                      <Close/>
                    </el-icon>
                  </div>
                  <el-upload
                      v-else
                      class="avatar-uploader"
                      :action="uploadFileUrl"
                      :show-file-list="false"
                      :limit="1"
                      :headers="headers"
                      :on-success="handleAvatarSuccess"
                      :before-upload="beforeAvatarUpload"
                      :on-remove="handleRemove"
                      :before-remove="beforeRemove"
                      :disabled="type"
                      accept=".png, .jpg, .jpeg, .svg"
                  >
                    <el-icon class="avatar-uploader-icon">
                      <Plus/>
                    </el-icon>
                    <template #tip>
                      <div class="el-upload__tip">
                        上传图片不超过1张，大小限制2M以内
                      </div>
                    </template>
                  </el-upload>

                </el-form-item>
              </el-col>

            </el-row>
          </el-form>-->
        </div>
      </div>
      <div class="model-container">
        <div class="app-title">
          虚拟身份信息
          <el-button type="primary" @click="addTableData">新建</el-button>
        </div>
        <el-table
          :data="addForm.internetUserAccountList"
          ref="listRef"
          size="large"
          table-layout="auto"
          style="flex-grow: 1"
          row-key="id"
        >
          <el-table-column label="序号" width="100" fixed="left" align="center">
            <template #default="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <template v-for="(column, index) in columns">
            <el-table-column
              v-if="column.visible"
              :label="column.label"
              :key="column.key"
              :prop="column.key"
              :fixed="column.fixed"
              :min-width="column.minWidth"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showTooltip"
            >
              <template v-if="column.key === 'status'" #default="scope">
                <div
                  :class="
                    scope.row.status === '0' ? 'open-status' : 'stop-status'
                  "
                >
                  {{ scope.row.status === "0" ? "启用" : "停用" }}
                </div>
              </template>
            </el-table-column>
          </template>
          <el-table-column label="操作" fixed="right" width="180">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope)"
                >编辑</el-button
              >
              <el-button link type="primary" @click="handleDelete(scope)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="model-container" v-if="type">
        <div class="app-title">人员网上活动</div>
        <div class="post-container">
          <div class="post-item">
            <div class="post-info">
              <img
                class="post-img"
                src="@/assets/images/post-total.png"
                alt=""
              />

              <div class="post-label">累计发帖</div>
            </div>
            <div class="post-num">{{ postNum.total }}</div>
          </div>

          <div class="post-item">
            <div class="post-info">
              <img
                class="post-img"
                src="@/assets/images/post-year.png"
                alt=""
              />
              <div class="post-label">今年发帖</div>
            </div>
            <div class="post-num">{{ postNum.thisYear }}</div>
          </div>

          <div class="post-item">
            <div class="post-info">
              <img
                class="post-img"
                src="@/assets/images/post-month.png"
                alt=""
              />
              <div class="post-label">本月发帖</div>
            </div>
            <div class="post-num">{{ postNum.thisMonth }}</div>
          </div>
        </div>
      </div>
      <div class="model-container" v-if="type">
        <div class="app-title">帖文列表</div>
        <div
          style="
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100%;
          "
        >
          <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            label-width="68px"
          >
            <el-form-item prop="date" style="margin-right: 0.564rem">
              <!--<el-input
                  v-model="queryParams.date"
                  placeholder="按照发帖时间查询"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
              />-->

              <el-date-picker
                v-model="queryParams.date"
                prefix-icon="Calendar"
                type="daterange"
                range-separator=""
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY/MM/DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :size="size"
                clearable
                @keyup.enter="handleQuery"
                @change="handleQuery"
              >
                <template #suffix>是否删除</template>
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="title">
              <el-input
                v-model="queryParams.title"
                placeholder="请输入标题"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
                @change="handleQuery"
                :prefix-icon="Search"
              />
            </el-form-item>
            <el-form-item prop="platform">
              <el-input
                v-model="queryParams.platform"
                placeholder="请输入发帖平台"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
                @change="handleQuery"
                :prefix-icon="Search"
              />
              <!--<el-select
                  v-model="queryParams.platform"
                  placeholder="按照发帖平台查询"
                  style="width: 240px"
              >
                <el-option
                    v-for="item in platform_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>-->
            </el-form-item>
            <el-form-item prop="isDeletes">
              <el-select
                v-model="queryParams.isDeletes"
                placeholder="按照是否删除查询"
                style="width: 240px"
                clearable
                @change="handleQuery"
              >
                <template #prefix>是否删除</template>
                <el-option label="全部" value="全部" />
                <el-option
                  v-for="item in source_status"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <!--<div style="line-height: 32px; font-size: 14px">
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </div>-->
        </div>

        <el-table
          v-loading="loading"
          :data="postList"
          ref="listRef"
          size="large"
          table-layout="auto"
          style="flex-grow: 1; margin-top: 1.08rem"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            fixed="left"
            align="center"
            reserve-selection
          />
          <el-table-column label="序号" width="100" fixed="left" align="center">
            <template #default="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <template v-for="(column, index) in postColumns">
            <el-table-column
              v-if="column.visible"
              :label="column.label"
              :key="column.key"
              :prop="column.key"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showTooltip"
            >
              <template v-if="column.key === 'isDelete'" #default="scope">
                <dict-tag
                  listClass="primary"
                  :options="source_status"
                  :value="scope.row.isDelete"
                />
              </template>
            </el-table-column>
          </template>
          <el-table-column label="操作" fixed="right" width="180">
            <template #default="scope">
              <el-button link type="primary" @click="handleCheck(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>

          <template #empty>
            <img src="@/assets/images/empty-img.png" alt="" />
          </template>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page-sizes="[10, 20, 30, 50, 200]"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        >
          <template #right>
            <div style="line-height: 32px; font-size: 14px">
              已选择 {{ selectionCount }} 条数据
            </div>
          </template>
        </pagination>
      </div>
    </div>

    <AddVirDlg ref="virRef" @submitForm="submitForm"></AddVirDlg>

    <el-dialog :title="postObj.title" v-model="postOpen" :center="true">
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
        "
      >
        <span>发帖时间：{{ postObj.createTime }}</span>
        <span>发帖平台：{{ postObj.platform }}</span>
        <span style="display: flex; align-items: center">
          当前状态：<span
            :style="{ color: postObj.isDelete === '0' ? '#409EFF' : '#F56C6C' }"
            >{{ postObj.isDelete === "0" ? "未删除" : "已删除" }}</span
          >
        </span>
      </div>
      <div style="margin-top: 20px">{{ postObj.content }}</div>
    </el-dialog>
  </div>
</template>

<script setup name="AddNetPerson">
import { Search } from "@element-plus/icons-vue";
import { getToken } from "@/utils/auth";
import {
  addNetPerson,
  editNetPerson,
  getPersonDetailInfo,
  getPostList,
  getPostRecord,
} from "@/api/networkuser/user.js";
import { deepClone } from "../../../utils";
import AddVirDlg from "./components/AddVirDlg";
import { getDict } from "@/utils/dict";

const { proxy } = getCurrentInstance();
const { platform_type, source_status, sys_user_sex } = proxy.useDict(
  "platform_type",
  "source_status",
  "sys_user_sex"
);
const uploadFileUrl = ref(
  import.meta.env.VITE_APP_PORTAL_API + "/common/upload"
);
const headers = ref({ Authorization: "Bearer " + getToken() });
const router = useRouter();

function backBtn() {
  addForm.value = {};
  router.back();
}

const typeOptions = ref([
  {
    label: "启用",
    value: "0",
  },
  {
    label: "停用",
    value: "1",
  },
]);

// 身份证号格式校验（严格校验出生日期和校验码）
const checkIdCard = (rule, value, callback) => {
  if (!value) {
    return callback(new Error("身份证号不能为空"));
  }
  if (value && value.length > 0) {
    const idCardReg =
      /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardReg.test(value)) {
      callback(new Error("请输入正确的身份证号"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

const checkPhone = (rule, value, callback) => {
  if (!value) {
    return callback(new Error("联系方式不能为空"));
  }
  // 手机号正则表达式
  const regex = /^[1][3-9]\d{9}$/;
  if (!regex.test(value)) {
    callback(new Error("请输入正确的联系方式"));
  } else {
    callback();
  }
};
const data = reactive({
  addForm: {
    id: proxy.$route.params.id || "",
    address: "",
    idCard: "",
    property: "",
    sex: "",
    phone: "",
    name: "",
    internetUserAccountList: [],
  },
  type: proxy.$route.params.type ? true : null,
  addRules: {
    name: [{ required: true, message: "姓名不可为空", trigger: "blur" }],
    address: [{ required: true, message: "住址信息不可为空", trigger: "blur" }],
    idCard: [{ required: true, validator: checkIdCard, trigger: "blur" }],
    sex: [
      { required: true, message: "性别不可为空", trigger: ["blur", "change"] },
    ],
    phone: [{ required: true, validator: checkPhone, trigger: "blur" }],
  },
  columns: [
    {
      key: "type",
      label: `所属平台`,
      visible: true,
      fixed: true,
      width: 250,
      showTooltip: true,
    },
    {
      key: "account",
      label: `网民ID`,
      visible: true,
      fixed: true,
      width: 250,
      showTooltip: true,
    },
    {
      key: "nickName",
      label: `昵称`,
      visible: true,
      fixed: true,
      width: 250,
      showTooltip: true,
    },
    { key: "status", label: `状态`, visible: true, width: 120 },
  ],
  postColumns: [
    {
      key: "createTime",
      label: `发帖时间`,
      visible: true,
      fixed: true,
      width: 250,
      showTooltip: true,
    },
    {
      key: "title",
      label: `标题`,
      visible: true,
      fixed: true,
      width: 250,
      showTooltip: true,
    },
    {
      key: "content",
      label: `帖文内容`,
      visible: true,
      fixed: true,
      width: 250,
      showTooltip: true,
    },
    {
      key: "platform",
      label: `发帖平台`,
      visible: true,
      fixed: true,
      width: 250,
      showTooltip: true,
    },
    {
      key: "isDelete",
      label: `是否删除`,
      visible: true,
      fixed: true,
      width: 250,
      showTooltip: true,
    },
  ],
  queryParams: {
    userId: proxy.$route.params.id,
    date: [],
    title: null,
    platform: null,
    isDeletes: "全部",
    beginTime: null,
    endTime: null,
    pageNum: 1,
    pageSize: 10,
  },
  postList: [],
  total: 0,
  loading: false,
  postNum: {},
  open: false,
  virForm: {},
  virRules: {
    nickName: [
      { required: true, message: "昵称不可为空", trigger: "submit" },
      { max: 50, message: "昵称不得超过50字符", trigger: "submit" },
    ],
    account: [
      { message: "账号不可为空", trigger: "submit" },
      { max: 50, message: "账号不得超过50字符", trigger: "submit" },
    ],
  },
  isAdd: false,
  editIndex: null,
  postOpen: false,
  postObj: {},
  isShowCode: false,
});
const {
  addForm,
  addRules,
  columns,
  type,
  queryParams,
  postList,
  total,
  postColumns,
  loading,
  postNum,
  open,
  virForm,
  virRules,
  isAdd,
  editIndex,
  postOpen,
  postObj,
  isShowCode,
} = toRefs(data);

const personTempData = ref({});

function viewCode() {
  isShowCode.value = !isShowCode.value;
  getIdCode();
}

function getIdCode() {
  let temp = addForm.value.code.toString();
  // if (!type.value || isShowCode.value) {
  //   addForm.value.idCard = addForm.value.code
  // } else {
  //   addForm.value.idCard = temp.substring(0, 3) + '****' + temp.substring(temp.length - 4, temp.length)
  // }
  addForm.value.idCard = addForm.value.code;
}

function handleAvatarSuccess(e) {
  e.url = import.meta.env.VITE_APP_PORTAL_API + e.fileName;
  addForm.value.file = e;
  addForm.value.avatar = import.meta.env.VITE_APP_PORTAL_API + e.fileName;
}

function beforeAvatarUpload(e) {
  if (e.size > 2 * 1024 * 1024) {
    ElMessage.error("上传图片不得大于2M");
    return false;
  }
}

function handleRemove(e) {
  addForm.value.file = {};
  addForm.value.avatar = "";
}

function beforeRemove() {}

function addTableData() {
  open.value = true;
  isAdd.value = true;
  console.log(proxy.$refs["virRef"]);
  proxy.$refs["virRef"].openDlg("add", {});
  // addForm.value.internetUserAccountList.push({})
}

function handleEdit(scope) {
  virForm.value = scope.row;
  isAdd.value = false;
  editIndex.value = scope.$index;
  // open.value = true
  proxy.$refs["virRef"].openDlg("edit", virForm.value);
}

function handleDelete(row) {
  // console.log(row, row.$index)

  ElMessageBox.confirm("确定要删除该记录吗", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      addForm.value.internetUserAccountList.splice(row.$index, 1);
      handleSubmit();
    })
    .catch(() => {
      ElMessage({
        type: "danger",
        message: "删除失败",
      });
    });

  // addForm.value.internetUserAccountList.splice(row.$index, 1)
  // console.log(addForm.value.internetUserAccountList)
  // .push({})
}

function handleSubmit(paramsType) {
  proxy.$refs["addFormRef"].validate((valid) => {
    if (valid) {
      if (!addForm.value.id) {
        console.log("111addForm.value", addForm.value);

        addNetPerson(addForm.value).then((res) => {
          if (res.code === 200) {
            ElMessage({
              type: "success",
              message: res.msg,
            });
            if (paramsType === "basic") {
              router.back();
            }
          } else {
            ElMessage({
              type: "danger",
              message: res.msg,
            });
          }
        });
      } else {
        editNetPerson(addForm.value).then((res) => {
          if (res.code === 200) {
            ElMessage({
              type: "success",
              message: res.msg,
            });
            cancel();
          } else {
            ElMessage({
              type: "danger",
              message: res.msg,
            });
          }
        });
      }
    }
  });
}

function cancel(resetType) {
  if (resetType === "basic") {
    if (proxy.$route.params.id) {
      Object.assign(addForm.value, personTempData.value);
    } else {
      const basicReset = {
        address: "",
        idCard: "",
        property: "",
        sex: "",
        phone: "",
        name: "",
      };
      Object.assign(addForm.value, basicReset);
      router.back();
    }
    type.value = true;
  }
}

function getPersonData() {
  if (addForm.value.id) {
    getPersonDetailInfo(addForm.value.id)
      .then((res) => {
        if (res.code === 200) {
          addForm.value = res.data;
          personTempData.value = { ...addForm.value };
          addForm.value.code = addForm.value.idCard.toString();
          console.log(addForm.value.code, addForm.value.idCard);
          getIdCode();
          /*if(type.value){
            let temp = addForm.value.idCard.toString()
            addForm.value.idCard = temp.substring(0,3) + '****'+ temp.substring(temp.length - 4, temp.length)
            }*/
        }
      })
      .catch((err) => {});
  }
}

function getList() {
  queryParams.value.isDelete =
    queryParams.value.isDeletes === "全部" ? null : queryParams.value.isDeletes;
  loading.value = true;
  if (!queryParams.value.date) {
    queryParams.value.date = [];
  }
  if (queryParams.value.date.length > 0) {
    queryParams.value.beginTime = queryParams.value.date[0];
    queryParams.value.endTime = queryParams.value.date[1];
  }

  console.log("queryParams.value", queryParams.value);

  getPostList(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        postList.value = res.rows;
        total.value = res.total;

        // postList.value = [];
        total.value = 0;
      }
    })
    .catch((err) => {})
    .finally((f) => {
      loading.value = false;
    });
}

function getPosts() {
  getPostRecord(addForm.value.id)
    .then((res) => {
      if (res.code === 200) {
        postNum.value = res.data;
      }
    })
    .catch((err) => {});
}

function initData() {
  if (addForm.value.id) {
    addForm.value = {
      id: addForm.value.id,
    };
    getPersonData();
    if (type.value) {
      getList();
      getPosts();
    }
  }
}

function handleQuery() {
  getList();
}

function submitForm(value) {
  if (isAdd.value) {
    addForm.value.internetUserAccountList?.push(value);
    // handleSubmit();
  } else {
    addForm.value.internetUserAccountList[editIndex.value] = deepClone(value);
    handleSubmit();
  }

  /* proxy.$refs["virRef"].validate(valid => {
      if (valid) {
        if (isAdd.value) {
          addForm.value.internetUserAccountList.push(virForm.value)
        } else {
          addForm.value.internetUserAccountList[editIndex.value] = deepClone(virForm.value)
        }
        dialogCancel()
      }
    });*/
}

function dialogCancel() {
  open.value = false;

  virForm.value = {};
}

function getRemark(value) {
  if (value === 0) {
    return "启用";
  } else {
    return "停用";
  }
}

function resetQuery() {
  queryParams.value.date = [];
  queryParams.value.title = null;
  queryParams.value.platform = null;
  queryParams.value.isDeletes = "全部";
  queryParams.value.beginTime = null;
  queryParams.value.endTime = null;
  queryParams.value.pageNum = 1;
  queryParams.value.pageSize = 10;
  handleQuery();
}

//查看详情
function handleCheck(row) {
  postOpen.value = true;
  postObj.value = row;
}

initData();
</script>

<style lang="scss" scoped>
.col-bottom {
  margin-bottom: 1.67rem;
}

.form-container {
  width: inherit;
  display: flex;
  /*align-items: center;*/
  border-radius: 9px;
  border: 1px solid #eeeeef;
  padding: 2rem 2.17rem;
}

.back-btn {
  height: 64px;
  border-bottom: 1px solid #e6e9ed;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 700;
  font-size: 16px;
  color: #1f2329;
  // margin-bottom: 22px;
  cursor: pointer;
  padding: 13px 10px 10px;
}

.person-form {
  width: 80vw;
  border-radius: 9px;
  border: 1px solid #eeeeef;
}

.app-container {
  position: relative;
  padding: 0;

  .footer {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.body-container {
  padding: 0 22px;
  margin-top: 22px;
}

.model-container {
  display: flex;
  flex-direction: column;
  /*align-items: center;*/
  margin-bottom: 3.08rem;

  .app-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding-left: 9px;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      width: 3px;
      height: 16px;
      background-color: #1356f0;
      border-radius: 2px;
    }

    .btn-box {
      display: flex;
      align-items: center;
    }
  }

  .post-container {
    width: 100%;
    display: flex;
    align-items: center;

    .post-item {
      height: 100px;
      width: 348px;
      /*border: 1px solid rgba(0,0,0,.1);*/
      border-radius: 5px;
      margin-right: 20px;
      padding: 20px;
      background-color: #f9fafb;

      .post-num {
        color: #1f2329;
        font-size: 29px;
        font-weight: 700;
      }

      .post-info {
        /*height: 60px;*/
        display: flex;
        align-items: center;
        margin-bottom: 13px;
        /*flex-direction: column;*/
        /*justify-content: space-between;*/

        .post-img {
          width: 14px;
          height: 14px;
          margin-right: 6px;
        }

        font-size: 14px;
        color: #646a73;

        .post-label {
        }
      }
    }
  }
}

.avatar-uploader {
  display: flex;
  /*align-items: center;*/
  justify-content: center;
  height: 4.67rem;
  width: 4.67rem;
  /*width: 56px;*/
  /*height: 56px;*/
}

.avatar-uploader-icon,
.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.avatar-uploader-icon {
  font-size: 1.39rem;
  height: 4.67rem;
  width: 4.67rem;
  margin-right: 18px;
  background: #fafafa;
  border-radius: 0.25rem;
  border: 0.08rem dashed #e8e8e8;
}

.avatar {
  width: 7.25rem;
  height: 9.17rem;

  .close-icon {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    border-radius: 0 0 0 10px;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #fff;
    cursor: pointer;
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.open-status,
.stop-status {
  width: 38px;
  height: 18px;
  border-radius: 3px;
  font-size: 12px;
  display: flex;
  line-height: 18px;
  justify-content: center;
}

.open-status {
  background: #e6ecff;
  color: #1749e0;
}

.stop-status {
  background-color: #ffe8ef;
  color: #f82626;
}

:deep(.el-dialog__header) {
  text-align: center !important;
}

:deep(.el-form-item__content) {
  flex-wrap: nowrap;
}

:deep(.el-form-item.is-required > .el-form-item__label::after) {
  content: "*";
  color: #f56c6c;
  margin-left: 4px;
}

:deep(.el-select--large .el-select__wrapper) {
  min-height: 0;
  height: 2.67rem;
  background: #ffffff;
  /*border-radius: 0.5rem;*/
  /*border: 0.08rem solid #CFD2D6;*/
}

:deep(.el-input__inner) {
  background: #ffffff;
  /*border-radius: 0.5rem;*/
  /*border: 0.08rem solid #CFD2D6;*/
}

:deep(.el-input__wrapper) {
  background-color: #ffffff !important;
  height: 2.67rem;
  border-radius: 0.5rem;
  border: 0.08rem solid #cfd2d6;
  box-shadow: none;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #ffffff !important;
  height: 2.67rem;
  border: none;
  box-shadow: none;
}

:deep(.el-form-item--default) {
  margin-bottom: 0;
}

:deep(.el-select__wrapper) {
  border-radius: 0.5rem;
  border: 0.08rem solid #cfd2d6;
  box-shadow: none;
  height: 2.67rem;
}

:deep(.el-select__wrapper.is-disabled:hover) {
  box-shadow: none;
}

:deep(.el-form-item) {
  margin-right: 0.564rem;
}

:deep(.el-range-editor.el-input__wrapper) {
  display: flex;
  justify-content: flex-start;
}

:deep(.el-date-editor .el-range-input) {
  text-align: start;
  width: 45%;
}

:deep(.el-date-editor .el-range__icon) {
  position: absolute;
  top: 50%;
  right: 1.5rem;
  transform: translateY(-50%);
}

:deep(.el-upload__tip) {
  width: 78px;
}
</style>
