<template>
  <ECharts
    v-if="lineData.length > 0"
    ref="lineRef"
    :options="lineOptions"
    :is-watch-options="false"
  />
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import ECharts from "@/components/ECharts/index.vue";

const props = defineProps({
  lineData: {
    type: Array,
    default: () => [],
  },
  styleConfig: {
    type: Object,
    default: () => ({}),
  },
});

const lineRef = ref(null);
const flowLightTimer = ref();
const startOffset1 = ref(0);
const startOffset2 = ref(0);
const endOffset1 = ref(0);
const endOffset2 = ref(0);

const lineOptions = computed(() => ({
  tooltip: {
    trigger: "axis",
    show: true,
    confine: true,
    formatter(params) {
      if (!params || params.length === 0) return "";
      const firstParam = params[0];
      const xAxisValue = firstParam.axisValue;
      const yAxisValue = firstParam.value;
      return `${xAxisValue}: ${yAxisValue}个`;
    },
  },
  grid: {
    left: 20,
    right: 10,
    bottom: 10,
    containLabel: true,
  },
  xAxis: {
    data: props.lineData.map((item) => item[0]),
    axisLine: { lineStyle: { color: "#394655" } },
    axisTick: { show: false },
    axisLabel: {
      margin: 5,
      color: "#BCC6D3",
      fontSize: 14,
      fontWeight: "bold",
      fontFamily: "DINAlternate",
    },
  },
  yAxis: {
    splitLine: { show: false },
    axisLabel: {
      margin: 6,
      color: "#929FAE",
      fontSize: 14,
      fontWeight: "bold",
      fontFamily: "DINAlternate",
    },
  },
  series: [
    {
      type: "line",
      smooth: true,
      data: props.lineData.map((item) => item[1]),
      showSymbol: false,
      hoverAnimation: true,
      animation: false,
      cursor: "default",
      lineStyle: { color: "#1E88DE", width: 2 },
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: "rgba(130,174,255,0.32)" },
            { offset: 1, color: "rgba(77,135,244,0)" },
          ],
          global: false,
        },
      },
    },
    {
      type: "line",
      smooth: true,
      data: props.lineData.map((item) => item[1]),
      showSymbol: false,
      animation: false,
      lineStyle: {
        width: 4,
        color: {
          type: "linear",
          colorStops: [
            { offset: startOffset1.value, color: "transparent" },
            { offset: startOffset2.value, color: "rgba(44, 160, 255, 0)" },
            { offset: endOffset1.value, color: "rgba(202, 240, 255, 1)" },
            { offset: endOffset2.value, color: "transparent" },
          ],
        },
      },
    },
  ],
}));

function flowLightLine() {
  let i = 1;
  let step = 0.005;
  flowLightTimer.value = setInterval(() => {
    i++;
    startOffset1.value = step * (i - 1);
    startOffset2.value = step * i;
    endOffset1.value = startOffset2.value + step * 20;
    endOffset2.value = endOffset1.value + step;
    if (endOffset2.value >= 1) {
      endOffset2.value = 1;
      i = 0;
    }
    lineRef.value?.getInstance().setOption({
      series: [
        {
          type: "line",
          smooth: true,
          data: props.lineData.map((item) => item[1]),
          showSymbol: false,
          hoverAnimation: true,
          animation: false,
          cursor: "default",
          lineStyle: { color: "#1E88DE", width: 2 },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "rgba(130,174,255,0.32)" },
                { offset: 1, color: "rgba(77,135,244,0)" },
              ],
              global: false,
            },
          },
        },
        {
          lineStyle: {
            color: {
              type: "linear",
              colorStops: [
                { offset: startOffset1.value, color: "transparent" },
                { offset: startOffset2.value, color: "rgba(44, 160, 255, 0)" },
                { offset: endOffset1.value, color: "rgba(202, 240, 255, 1)" },
                { offset: endOffset2.value, color: "transparent" },
              ],
            },
          },
        },
      ],
    });
    lineRef.value?.resizeEcharts();
  }, 1000 / 60);
}

onMounted(() => {
  flowLightLine();
});
onBeforeUnmount(() => {
  clearInterval(flowLightTimer.value);
});
</script>

<style lang="scss" scoped></style>
