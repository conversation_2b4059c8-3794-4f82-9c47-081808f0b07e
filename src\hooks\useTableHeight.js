/**
 * 动态计算表格高度
 * @param {Object} tableContainerRef 表格容器ref
 * @param {Number} offect 偏差值
 */
export function useTableHeight(tableContainerRef, offect) {
  const tableHeight = ref(0); // 表格高度

  onMounted(() => {
    getTableHeight();
    window.addEventListener("resize", getTableHeight);
  });
  onUnmounted(() => {
    window.removeEventListener("resize", getTableHeight);
  });

  /**
   * 窗口大小改变时，重新计算表格高度
   */
  function getTableHeight() {
    tableHeight.value = tableContainerRef.value.clientHeight - offect;
  }

  return {
    tableHeight,
    getTableHeight
  };
}
