<template>
  <div class="w-full h-full flex flex-col relative">
    <!-- 顶部 -->
    <header class="w-full flex justify-between items-start poHeader">
      <!-- 编号、是否为首报 -->
      <section class="flex flex-col gap-y-[12px]">
        <div class="flex gap-x-[10px] items-center">
          <!-- <svg-icon
            icon-class="opinion-handle-title"
            class="!w-[20px] !h-[20px]"
          /> -->
          <span class="poDetailTitle">{{
            publicOpinionInfo?.reportNumber
          }}</span>
        </div>

        <div class="flex gap-x-[10px] items-center">
          <div v-if="publicOpinionInfo.isFirst || true" class="flex-center">
            <div class="firstTag">首报</div>
          </div>
          <span
            class="detailContent leading-normal whitespace-normal break-all"
          >
            {{ publicOpinionInfo?.poName || "-" }}
          </span>
        </div>
      </section>

      <!-- 转阅 -->
      <div v-if="false" class="cursor-pointer">
        <el-dropdown trigger="click">
          <svg-icon icon-class="referTo" class="!w-[20px] !h-[20px]" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                ><span @click="transmit('dingDing', '转至钉钉群')"
                  >转至钉钉群</span
                ></el-dropdown-item
              >
              <el-dropdown-item
                ><span @click="transmit('weChatGroup', '转至企业微信')"
                  >转至企业微信</span
                ></el-dropdown-item
              >
              <el-dropdown-item
                ><span @click="transmit('self', '转至个人')"
                  >转至个人</span
                ></el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 内容 -->
    <main
      :class="[
        'mt-[24px]  flex flex-col gap-y-[24px] overflow-y-auto ',
        showOperation ? 'h-[750px]' : 'h-[800px]',
      ]"
    >
      <section>
        <div class="detailLabel">舆情内容</div>
        <div class="flex items-center">
          <ImagePreview
            v-if="contentIsImg(publicOpinionInfo.poContent)"
            :src="publicOpinionInfo.poContent"
            :width="200"
            :height="120"
          />
          <p
            v-else
            class="detailContent leading-normal whitespace-pre-wrap break-all"
          >
            {{ poContentF(publicOpinionInfo.poContent) }}
          </p>
        </div>
      </section>

      <!-- 贴文链接 -->
      <section>
        <div class="detailLabel">贴文链接</div>
        <el-link
          v-if="publicOpinionInfo.poLink"
          class="detailContent linkText break-all"
          :underline="false"
          :href="publicOpinionInfo.poLink"
          target="blank"
        >
          {{ publicOpinionInfo.poLink }}
        </el-link>
        <div class="detailContent" v-else>-</div>
      </section>

      <!-- 照片可能是多个 -->
      <section>
        <div class="detailLabel">照片</div>
        <div
          v-if="publicOpinionInfo?.poImg?.length > 0"
          class="flex-1 flex-wrap flex gap-[12px]"
        >
          <ImagePreview
            v-for="(item, index) in publicOpinionInfo?.poImg"
            :key="index"
            :src="item"
            :width="200"
            :height="120"
            @click.stop
            class="cursor-pointer"
          />
        </div>
        <div class="detailContent" v-else>-</div>
      </section>

      <section class="poEvent-container">
        <div class="detailLabel">所属事件</div>
        <!-- <div class="detailContent">
          {{ poEventF(publicOpinionInfo.poEvent) }}
        </div> -->
        <PoEvent :poEvent="publicOpinionInfo.poEvent" visible-key="visible2" />
      </section>

      <section>
        <div class="detailLabel">上报单位</div>
        <div class="detailContent">
          {{ publicOpinionInfo?.workUnit || "-" }}
        </div>
      </section>

      <section class="flex flex-col">
        <span class="detailLabel">来源</span>
        <dict-tag
          v-if="publicOpinionInfo?.reportSource"
          :options="opinion_source_type"
          :value="publicOpinionInfo?.reportSource"
          class="detailContent"
        />
        <div class="detailContent" v-else>-</div>
      </section>

      <section>
        <div class="detailLabel">网络类型</div>
        <div class="detailContent">
          {{ publicOpinionInfo?.platformType || "-" }}
        </div>
      </section>

      <section>
        <div class="detailLabel">网络昵称</div>
        <div class="detailContent">
          {{ publicOpinionInfo?.netizenNickname || "-" }}
        </div>
      </section>

      <section>
        <div class="detailLabel">网民账号</div>
        <div class="detailContent">
          {{ publicOpinionInfo?.netizenAccount || "-" }}
        </div>
      </section>

      <section>
        <div class="detailLabel">报送时间</div>
        <div class="detailContent">
          {{ publicOpinionInfo?.createTime || "-" }}
        </div>
      </section>

      <section>
        <div class="detailLabel">目标群</div>
        <div class="detailContent">
          {{ publicOpinionInfo?.targetGroup || "-" }}
        </div>
      </section>
    </main>

    <!-- 按钮 -->
    <footer v-if="showHandleBtn" class="mt-[20px] absolute bottom-0">
      <el-button class="handleBtn" type="primary" @click="handleOpinion"
        >舆情处置</el-button
      >
    </footer>
  </div>
</template>

<script setup>
import PoEvent from "@/views/publicOpinionManage/components/PoEvent.vue";

const props = defineProps({
  publicOpinionInfo: {
    type: Object,
    default: () => {},
    required: true,
  },
  detailType: {
    type: String,
    required: true,
  },
  showOperation: {
    type: Boolean,
    default: false,
    required: true,
  },
});

const showHandleBtn = computed(() => props.showOperation);

const { proxy } = getCurrentInstance();
const { opinion_source_type } = proxy.useDict("opinion_source_type");

const emit = defineEmits(["showDialog", "showHandleDialog"]);
const poEventF = computed(
  () => (list) =>
    Array.isArray(list) ? list.map((ele) => ele.name).join(",") : "-"
);

const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 内容是否为图片
const poContentF = computed(() => (val) => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter((i) => !contentIsImg.value(i));
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});

const handleOpinion = () => {
  emit("showHandleDialog");
};

defineExpose({
  showHandleBtn,
});
</script>

<style lang="scss" scoped>
.poHeader {
  padding-bottom: 12px;
  border-bottom: 1px solid #f5f5f5;

  .poDetailTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 12px;
    color: #8592a6;
  }
}

.firstTag {
  width: 47px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 13px;
  background: #e6ecff;
  color: #1749e0;
}

// 舆情内容链接
:deep(.linkText) {
  display: inline;
  font-size: 12px;
  color: #0052d9;
  &:visited {
    color: #0052d9;
  }
  .el-link__inner {
    display: inline;
  }
}

.detailLabel {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #1f2125;
  line-height: 20px;
  font-style: normal;
  margin-bottom: 12px;
}

.poEvent-container :deep(.noData),
.detailContent {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #3f434b;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}

.handleBtn {
  width: 96px;
  height: 32px;
  line-height: 32px;
  background: #0070ff;
  border-radius: 6px;

  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
}
</style>
