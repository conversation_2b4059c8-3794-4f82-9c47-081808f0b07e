<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <!-- <breadcrumb
        id="breadcrumb-container"
        class="breadcrumb-container"
      /> -->
      <tags-view v-if="needTagsView" class="mb-[10px]" />
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="tagsViewStore.cachedViews">
          <component
            v-if="!route.meta.link"
            :is="Component"
            :key="route.path"
          />
        </keep-alive>
      </transition>
    </router-view>
    <iframe-toggle />
  </section>
</template>

<script setup>
import iframeToggle from "./IframeToggle/index";
import useTagsViewStore from "@/store/modules/tagsView";
import Breadcrumb from "@/components/Breadcrumb";
import useSettingsStore from "@/store/modules/settings";
import TagsView from "./TagsView/index";

const tagsViewStore = useTagsViewStore();
const settingsStore = useSettingsStore();
const needTagsView = computed(() => settingsStore.tagsView);
</script>

<style lang="scss" scoped>
.app-main {
  /* 64= navbar  64  */
  height: calc(100vh - 64px - 24px);
  width: 100%;
  background-color: #ffffff;
  border-radius: 4px;
  position: relative;
  overflow: auto;
}
.topmenu-container {
  position: absolute;
  left: 50px;
}
.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 58 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>
