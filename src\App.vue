<!--
 * @Author: 周杰 <EMAIL>
 * @Date: 2025-01-17 18:01:07
 * @LastEditors: 周杰 <EMAIL>
 * @LastEditTime: 2025-07-16 19:34:09
 * @FilePath: \patrol-intel-web\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- <el-watermark class="h-full !z-99" :font="fontStyle" :content="[watermarkContent]"> -->
  <router-view />
  <!-- </el-watermark> -->
</template>

<script setup>
import useSettingsStore from "@/store/modules/settings";
import { handleThemeStyle } from "@/utils/theme";
import { useBrowserCheck } from "@/hooks/useBrowserCheck.js";
import useUserStore from "@/store/modules/user.js";
import { useRoute } from "vue-router";
import {
  DARK_ROUTE_NAME_LIST,
  DARK_WATERMARK_DEFAULT_COLOR,
  LIGHT_WATERMARK_DEFAULT_COLOR,
} from "@/constant/index.js";
import { getConfigKey } from "@/api/system/config";

const userStore = useUserStore();
const route = useRoute();

// 水印
const darkWatermarkColor = ref(DARK_WATERMARK_DEFAULT_COLOR);
const lightWatermarkColor = ref(LIGHT_WATERMARK_DEFAULT_COLOR);
const fontStyle = ref({
  color: DARK_WATERMARK_DEFAULT_COLOR,
});

// 水印内容，由名称-昵称组成
const watermarkContent = computed(() => {
  const { name, nickname } = userStore; // 解构赋值
  return name && nickname ? `${name}-${nickname}` : "";
});

// 监听路由名称，根据不同的页面改变颜色
watch(
  () => route.name,
  (newV) => {
    changeWatermarkColorByRouteName(newV);
  },
  { deep: true, immediate: true }
);

// 在登录后调用接口获取水印颜色
watch(
  () => userStore.token,
  async (newToken) => {
    if (newToken) {
      await getWatermarkColor();
    }
  }
);

// 根据路由名称改变水印颜色
function changeWatermarkColorByRouteName(routeName) {
  if (routeName && userStore.token) {
    // 是否为深色背景
    const isDarkPage = DARK_ROUTE_NAME_LIST.includes(routeName);
    fontStyle.value = {
      color: isDarkPage ? darkWatermarkColor.value : lightWatermarkColor.value,
    };
  }
}

// 获取水印颜色
async function getWatermarkColor() {
  try {
    // 通过网安数智赋能平台参数管理配置的水印色获取水印颜色，实现统一动态配置
    const res = await Promise.all([
      getConfigKey("sys.darkWatermark.color"),
      getConfigKey("sys.lightWatermark.color"),
    ]);
    darkWatermarkColor.value = res[0]?.msg || DARK_WATERMARK_DEFAULT_COLOR;
    lightWatermarkColor.value = res[1]?.msg || LIGHT_WATERMARK_DEFAULT_COLOR;
  } catch (e) {
    console.log(e, "error");
    darkWatermarkColor.value = DARK_WATERMARK_DEFAULT_COLOR;
    lightWatermarkColor.value = LIGHT_WATERMARK_DEFAULT_COLOR;
  }
}

onMounted(async () => {
  if (userStore.token) {
    await getWatermarkColor();
  }
  changeWatermarkColorByRouteName(route.name);
  await nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
    useBrowserCheck();
  });
});
</script>
