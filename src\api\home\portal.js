import portalRequest from "@/utils/portalRequest.js";

// 获取便签列表
export function postNotesList(params) {
  return portalRequest({
    url: '/api/oa/short-note/list',
    method: 'get',
    params,
  });
}

// 新增便签
export function postNotesAdd(data) {
  return portalRequest({
    url: '/api/oa/short-note/add',
    method: 'post',
    data,
  });
}

// 修改便签
export function postNotesEdit(data) {
  return portalRequest({
    url: '/api/oa/short-note/edit',
    method: 'post',
    data,
  });
}

// 删除便签
export function postNotesDelete(id) {
    return portalRequest({
        url: `/api/oa/short-note/delete/${id}`,
        method: 'post',
    });
}

// 值班带班
export function getDutyList() {
  return portalRequest({
    url: '/api/oa_duty/today_list',
    method: 'get',
  });
}

// 待办事项
export function getTodoList() {
  return portalRequest({
    url: '/system/notice/getToDomessage',
    method: 'get',
  });
}

// 获取外部应用
export function getOutLinks(){
  return portalRequest({
    url: '/backend/app_info/listOthers',
    method: 'get',
  });
}

// 获取链接应用
export function getLinks(){
  return portalRequest({
    url: '/backend/app_info/getLinkList',
    method: 'get',
  });
}

// 查询门户页面的运维人员信息
export function getOperationsInfo(){
  return portalRequest({
    url: '/system/notice/getOperationsInfo',
    method: 'get',
  });
}