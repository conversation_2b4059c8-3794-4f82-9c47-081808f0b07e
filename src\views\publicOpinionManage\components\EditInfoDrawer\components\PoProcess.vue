<template>
  <div class="poProcess-container">
    <div class="poProcessTitle">舆情流程</div>
    <el-scrollbar
      ref="scrollbarRef"
      class="h-[calc(100%-32px)] pl-[20px] pr-[18px]"
      v-loading="loading"
    >
      <div
        class="processItem"
        v-for="(item, index) in processList"
        :key="index"
      >
        <template v-if="item.type.includes('反馈')">
          <div class="handleType">
            <img :src="item.icon" alt="" class="w-[15px] h-[15px] mr-[8px]" />
            <span>{{ item.type }}</span>
          </div>
          <section class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mt-[13px] mb-[6px]">截止时间</div>
            <div class="handleInfoValue">{{ item.dealTime }}</div>
          </section>
          <section class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">处理时间</div>
            <div class="handleInfoValue">{{ item.handleTime }}</div>
          </section>
          <section class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">处理人</div>
            <div class="handleInfoValue">
              {{ item.handler.name }}（{{ item.handler.unitName }}）
            </div>
          </section>
          <section class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">处理结果</div>
            <div class="handleInfoValue">{{ item.handleResult }}</div>
          </section>
          <section v-if="item.handleOpt" class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">处理措施</div>
            <div class="handleInfoValue">{{ item.handleOpt }}</div>
          </section>
          <section class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">处理说明</div>
            <div class="handleInfoValue">{{ item.handleDesc }}</div>
          </section>

          <!-- 流程状态 -->
          <div class="processStatus" :style="isOverdueF(item.isOverdue).style">
            {{ isOverdueF(item.isOverdue).label }}
          </div>
        </template>

        <template v-else-if="item.type.includes('下发')">
          <div class="handleType">
            <img :src="item.icon" alt="" class="w-[15px] h-[15px] mr-[8px]" />
            <span>{{ item.type }}</span>
          </div>
          <section class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mt-[13px] mb-[6px]">处置单位</div>
            <div class="handleInfoValue">{{ item.processUnit }}</div>
          </section>
          <section v-if="item.handler" class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">处理人</div>
            <div class="handleInfoValue">{{ item.handler }}</div>
          </section>
          <section v-if="item.taskDeadline" class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">任务截止时间</div>
            <div class="handleInfoValue">{{ item.taskDeadline }}</div>
          </section>
          <section v-if="item.selfName" class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">操作人</div>
            <div class="handleInfoValue">{{ item.selfName }}</div>
          </section>

          <!-- 流程状态 -->
          <div class="processStatus" :style="isOverdueF(item.isOverdue).style">
            {{ isOverdueF(item.isOverdue).label }}
          </div>
        </template>

        <template v-else>
          <div class="handleType">
            <img :src="item.icon" alt="" class="w-[15px] h-[15px] mr-[8px]" />
            <span>{{ item.type }}</span>
          </div>
          <section class="px-[16px] mt-[13px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">时间</div>
            <div class="handleInfoValue">{{ item.time }}</div>
          </section>
          <section v-if="item.sendTo" class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">转阅至</div>
            <div class="handleInfoValue">{{ item.sendTo }}</div>
          </section>
          <section v-if="item.handler" class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">处理人</div>
            <div class="handleInfoValue">{{ item.handler }}</div>
          </section>
          <section v-if="item.dealTime" class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">截止时间</div>
            <div class="handleInfoValue">{{ item.dealTime }}</div>
          </section>
          <section v-if="item.selfName" class="px-[16px] mb-[12px]">
            <div class="handleInfoLabel mb-[6px]">操作人</div>
            <div class="handleInfoValue">{{ item.selfName }}</div>
          </section>
        </template>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
defineProps({
  loading: {
    type: Boolean,
    require: true,
  },
  processList: {
    type: Array,
    required: true,
  },
});

const scrollbarRef = ref();

const PROCESS_STATUS_OPTIONS = [
  { label: "正常", value: false, color: "#108A5B", bgColor: "#E1FAF2" },
  { label: "延期提交", value: true, color: "#fff", bgColor: "#D9001B" },
];

// 流程状态
const isOverdueF = (val) => {
  const obj = PROCESS_STATUS_OPTIONS.find((ele) => ele.value === val);
  return {
    style: { backgroundColor: obj.bgColor, color: obj.color },
    label: obj.label,
  };
};

defineExpose({
  scrollbarRef,
});
</script>

<style lang="scss" scoped>
.poProcess-container {
  width: 100%;
  height: 100%;
  padding: 16px 0;
  background: #fcfcfd;

  .poProcessTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 12px;
    color: #8592a6;
    padding-left: 18px;
    margin-bottom: 16px;
  }

  .processItem {
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(220, 228, 228, 0.68);
    border-radius: 6px;
    border: 1px solid #dbe0e6;
    position: relative;

    .handleType {
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 16px;
      border-bottom: 1px solid #ebedf0;

      span {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 12px;
        color: #1f2329;
      }
    }

    .handleInfoLabel {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #646a73;
    }

    .handleInfoValue {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #1f2329;
      word-break: break-all;
    }

    &:not(:last-child) {
      margin-bottom: 16px;
    }

    .processStatus {
      padding: 0 7px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      border-radius: 3px;

      position: absolute;
      right: 12px;
      top: 10px;

      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
    }
  }
}
</style>
