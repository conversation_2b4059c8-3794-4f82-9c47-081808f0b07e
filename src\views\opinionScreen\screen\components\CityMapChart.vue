<template>
  <div class="w-full h-full" ref="mapChartRef"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { changshaGeoJson, mapActiveBottom, mapActiveTop } from "@/utils/mapChart.js";
import mapBg from "@/assets/screen/cityMapBg.png";
import { getAreaPo } from "@/api/eventOpinion/index";
import { CITY_DATA } from "../config/constant";

const { proxy } = getCurrentInstance();

const mapChart = ref(null);
const mapTimer = ref(null);
const activeCity = ref(0);

onMounted(async () => {
  getMap();

  // 展示提示信息
  const cityData = await getChangShaPo();
  handleMapItem(cityData);
  mapTimer.value = setInterval(() => {
    if (activeCity.value === 8) {
      activeCity.value = -1;
    }
    activeCity.value += 1;
    handleMapItem(cityData);
  }, 2000);

  window.addEventListener("resize", onresize);
});

onBeforeUnmount(() => {
  clearInterval(mapTimer.value);
  window.removeEventListener("resize", onresize);
});

function getMap() {
  echarts.registerMap("map", changshaGeoJson);
  const roam = false; // 禁止鼠标缩放
  const layoutCenter = ["50%", "50%"];
  const layoutSize = "150%";
  const aspectScale = 0.9;

  // 地图背景纹理
  const mapBgImg = document.createElement("img");
  mapBgImg.src = mapBg;

  let option = {
    geo: [
      // 第1层地图：带纹理
      {
        id: 0,
        map: "map", // 上面引入的数据
        show: true,
        zlevel: 0,
        roam: roam,
        layoutCenter: layoutCenter, //位置
        layoutSize: layoutSize, //大小
        aspectScale: aspectScale, // 长宽比
        label: {
          show: true,
          color: "#FEFFFF",
          fontFamily: "PangMenZhengDao",
          fontSize: 15,
          lineHeight: 17,
          shadowOffsetY: 1,
          shadowBlur: 3,
          shadowColor: "#3C84F9"
        },
        tooltip: {
          show: false
        },
        itemStyle: {
          areaColor: {
            image: mapBgImg,
            repeat: "no-repeat"
          },
          borderColor: "rgba(242, 253, 255, 0.59)",
          borderWidth: 1
        },
        emphasis: {
          disabled: true
        },
        regions: [
          {
            name: "开福区",
            label: {
              fontSize: 13,
              lineHeight: 11,
              formatter(value) {
                return value.name.split("").join("\n");
              }
            }
          },
          {
            name: "芙蓉区",
            label: {
              fontSize: 12,
              lineHeight: 14
            }
          },
          {
            name: "天心区",
            label: {
              fontSize: 12,
              lineHeight: 11,
              formatter(value) {
                return value.name.split("").join("\n");
              }
            }
          },
          {
            name: "雨花区",
            label: {
              fontSize: 14,
              lineHeight: 16
            }
          }
        ]
      },
      // 第2层地图：边框白色
      {
        id: 1,
        map: "map",
        show: true,
        zlevel: -1,
        roam: roam,
        layoutCenter: ["50%", "50%"],
        layoutSize: layoutSize,
        aspectScale: aspectScale,
        geoIndex: 1,
        label: {
          show: false
        },
        tooltip: {
          show: false
        },
        itemStyle: {
          areaColor: "#F2FDFF",
          borderWidth: 3,
          borderColor: "#F2FDFF",
          shadowOffsetY: 2,
          shadowBlur: 10,
          shadowColor: "rgba(246,248,245,0.81)"
        },
        emphasis: {
          disabled: true
        }
      },
      // 第3层地图：底部阴影
      {
        id: 2,
        map: "map",
        show: true,
        zlevel: -2,
        roam: roam,
        layoutCenter: ["50%", "54%"],
        layoutSize: layoutSize,
        aspectScale: aspectScale,
        geoIndex: 2,
        label: {
          show: false
        },
        tooltip: {
          show: false
        },
        itemStyle: {
          areaColor: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#4973A5" // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#1F477A" // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          },
          shadowColor: "#1F477A",
          shadowOffsetY: 1,
          shadowBlur: 10
        },
        emphasis: {
          disabled: true
        }
      }
    ],
    series: []
  };
  if (!mapChart.value) {
    mapChart.value = markRaw(echarts.init(proxy.$refs.mapChartRef));
  }
  mapChart.value.setOption(option);
}

async function getChangShaPo() {
  const res = await getAreaPo();
  if (res.code === 200) {
    return CITY_DATA.map(ele => ({
      ...ele,
      count: res.data?.find(i => i.area.includes(ele.name))?.count || 0
    }));
  }
}

function handleMapItem(cityData) {
  let option = mapChart.value.getOption();
  let bottomItem = {
    type: "scatter",
    coordinateSystem: "geo",
    geoIndex: 0,
    symbol: `image://data:image/gif;${mapActiveBottom}`,
    symbolSize: [57, 54],
    data: [{ value: cityData[activeCity.value].bottomValue }]
  };
  let lineItem = {
    type: "lines",
    coordinateSystem: "geo",
    geoIndex: 0,
    lineStyle: {
      type: "dashed",
      color: "#5FB0FC",
      width: 2
    },
    data: [{ coords: cityData[activeCity.value].lineValue }]
  };
  let topItem = {
    type: "scatter",
    coordinateSystem: "geo",
    geoIndex: 0,
    symbol: `image://data:image/gif;${mapActiveTop}`,
    symbolSize: [184, 60],
    itemStyle: {
      opacity: 1
    },
    emphasis: {
      disabled: true
    },
    data: [{ value: cityData[activeCity.value].topValue }]
  };
  let topTextItem = {
    type: "scatter",
    coordinateSystem: "geo",
    geoIndex: 0,
    label: {
      show: true,
      color: "#FEFFFF",
      fontFamily: "PangMenZhengDao",
      fontSize: 20,
      lineHeight: 23,
      shadowOffsetY: 1,
      shadowBlur: 3,
      shadowColor: "#3C84F9",
      formatter: params => {
        return params.data.name;
      }
    },
    itemStyle: {
      color: "transparent"
    },
    data: [{ name: cityData[activeCity.value].count, value: cityData[activeCity.value].topTextValue }]
  };
  option.series = [];
  option.series.push(bottomItem);
  option.series.push(lineItem);
  option.series.push(topItem);
  option.series.push(topTextItem);
  mapChart.value.setOption(option, true);
}

/**
 * 重新调整图表大小
 */
function onresize() {
  if (mapChart.value) {
    mapChart.value.resize();
  }
}
</script>

<style lang="scss" scoped></style>
