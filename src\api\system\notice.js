import request from '@/utils/request'
import portalRequest from "@/utils/portalRequest.js";

// 查询公告列表
export function listNotice(query) {
  return portalRequest({
    url: '/system/notice/list',
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getNotice(noticeId) {
  return portalRequest({
    url: '/system/notice/' + noticeId,
    method: 'get'
  })
}

// 新增公告
export function addNotice(data) {
  return portalRequest({
    url: '/system/notice',
    method: 'post',
    data: data
  })
}

// 修改公告
export function updateNotice(data) {
  return portalRequest({
    url: '/system/notice',
    method: 'put',
    data: data
  })
}

// 删除公告
export function delNotice(noticeId) {
  return portalRequest({
    url: '/system/notice/' + noticeId,
    method: 'delete'
  })
}
