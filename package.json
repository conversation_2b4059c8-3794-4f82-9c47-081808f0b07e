{"name": "ruoyi", "version": "3.8.7", "description": "统一工作平台", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:client": "vite build --mode client", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/vue3": "^6.1.10", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.6.1", "animate.css": "^4.1.1", "animejs": "^3.2.2", "autofit.js": "^3.1.3", "autoprefixer": "^10.4.20", "axios": "0.27.2", "bowser": "^2.11.0", "core-js": "^3.38.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "5.4.3", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-china-area-data": "^6.1.0", "element-plus": "2.7.7", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "less": "^4.2.0", "lodash": "^4.17.21", "moment": "^2.30.1", "nprogress": "0.2.0", "pinia": "2.1.7", "sign-canvas-plus": "^2.0.3", "sortablejs": "^1.15.6", "three": "^0.169.0", "uuid": "^11.1.0", "vue": "3.3.9", "vue-clipboard3": "^2.0.0", "vue-cropper": "1.1.1", "vue-router": "4.2.5", "vue3-seamless-scroll": "^2.0.1", "vuedraggable": "^4.1.0", "vxe-pc-ui": "^4.2.6", "vxe-table": "^4.7.82", "watermark-dom": "^2.3.0", "zrender": "^5.6.0"}, "devDependencies": {"@unocss/transformer-directives": "^0.57.7", "@vitejs/plugin-legacy": "5.3.2", "@vitejs/plugin-vue": "4.5.0", "@vue/compiler-sfc": "3.3.9", "postcss": "^8.5.2", "postcss-px-to-viewport": "^1.1.1", "prettier": "3.3.3", "sass": "1.69.5", "terser": "^5.36.0", "unocss": "^0.57.7", "unplugin-auto-import": "^0.17.1", "unplugin-vue-components": "^0.27.0", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "5.0.4", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-top-level-await": "^1.4.4"}}