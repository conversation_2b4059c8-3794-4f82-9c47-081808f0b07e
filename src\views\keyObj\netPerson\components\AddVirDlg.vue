<template>
  <el-dialog
    :title="dialogTitle"
    v-model="open"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <el-form
      :model="virForm"
      ref="virRef"
      :rules="virRules"
      label-width="100px"
    >
      <el-form-item label="所属平台" prop="type">
        <!-- <el-input v-model="virForm.type" placeholder="请输入类型" /> -->
        <EditPlatformType
          v-model="virForm.type"
          :placeholder="'请选择所属平台'"
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="昵称" prop="nickName">
        <el-input
          v-model="virForm.nickName"
          placeholder="请输入昵称"
          show-word-limit
          maxlength="50"
        />
      </el-form-item>
      <el-form-item label="网民ID" prop="account">
        <el-input v-model="virForm.account" placeholder="请输入网民ID" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="virForm.status"
          placeholder="请输入状态"
          size="large"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="dialogCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from "vue";
import EditPlatformType from "@/views/publicOpinionManage/components/EditPlatformType.vue";
const emit = defineEmits(["submitForm"]);
const { proxy } = getCurrentInstance();

const typeOptions = ref([
  {
    label: "启用",
    value: "0",
  },
  {
    label: "停用",
    value: "1",
  },
]);

const data = reactive({
  virForm: {
    status: "0", // 新建时默认启用
  },
  open: false,
  virRules: {
    type: [{ required: true, message: "所属平台不可为空", trigger: "submit" }],
    nickName: [
      { required: true, message: "昵称不可为空", trigger: "submit" },
      { max: 50, message: "昵称不得超过50字符", trigger: "submit" },
    ],
    account: [{ max: 50, message: "网民ID不得超过50字符", trigger: "submit" }],
    // 状态不必填
  },
});
const { virForm, open, virRules } = toRefs(data);

const dialogType = ref("add");

const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新增" : "编辑";
});

function openDlg(type, obj) {
  dialogType.value = type;
  virForm.value = {
    ...obj,
    status: obj && obj.status !== undefined ? obj.status : "0", // 编辑时保留原值，新建时默认启用
  };
  open.value = true;
}

function submitForm() {
  proxy.$refs["virRef"].validate((valid) => {
    if (valid) {
      console.log("virForm.value", virForm.value);
      emit("submitForm", virForm.value);
      dialogCancel();
    }
  });
}

function dialogCancel() {
  open.value = false;
  virForm.value = {};
}

defineExpose({
  openDlg,
});
</script>

<style scoped>
:deep(.el-form-item.is-required > .el-form-item__label::after) {
  content: "" !important;
}
/* 
:deep(.el-input__wrapper) {
  background-color: #ffffff !important;
  height: 2.67rem;
  border-radius: 0.5rem;
  border: 0.08rem solid #cfd2d6;
  box-shadow: none;
}

:deep(.el-select__wrapper) {
  border-radius: 0.5rem;
  border: 0.08rem solid #cfd2d6;
  box-shadow: none;
  height: 2.67rem;
} */

:deep(.el-form-item--default) {
  margin-bottom: 18px !important;
}
</style>
