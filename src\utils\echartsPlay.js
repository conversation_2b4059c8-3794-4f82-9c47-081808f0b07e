/**
 * @param chartInstance echarts实例响应对象
 * @param dataArr 数据列表
 * @param autoplayInterval 图表定时器，控制播放开始和结束，是响应式对象
 * */
export function initChartPlay(chartInstance, dataArr, autoplayInterval) {
  if (!chartInstance || !chartInstance.value || !Array.isArray(dataArr) || dataArr.length === 0) {
    console.error("无效的参数：chartInstance 或 dataArr无效");
    return;
  }

  // 自动轮播逻辑
  let currentIndex = -1;

  const updateChart = () => {
    chartInstance.value.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    });
    chartInstance.value.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentIndex,
    });
  };

  const play = () => {
    if (currentIndex >= 0) {
      chartInstance.value.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: currentIndex,
      });
    }
    // 更新索引
    currentIndex = (currentIndex + 1) % (dataArr.length);

    updateChart();
  };

  const playAutoplay = (time = 3000) => {
    autoplayInterval.value = setInterval(play, time); // 每3秒轮播一次
  };

  const pauseAutoplay = () => {
    clearInterval(autoplayInterval.value);
    if (currentIndex >= 0) {
      chartInstance.value.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: currentIndex,
      });
    }
  };

  const resumeAutoplay = () => {
    updateChart();
    playAutoplay();
  };

  play(); // 默认选中第一个
  playAutoplay(); // 开始自动轮播

  chartInstance.value.on('mouseover', (params) => {
    if (params.dataIndex === currentIndex) {
      updateChart();
    } else {
      pauseAutoplay();
    }
  });

  chartInstance.value.on('mouseout', () => {
    pauseAutoplay();
    resumeAutoplay();
  });
}

/**
 * @param chartInstance echarts实例响应对象
 * @param autoplayInterval 图表定时器，控制播放开始和结束，是响应式对象
 * */
export function clearChartPlay(chartInstance, autoplayInterval) {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  clearInterval(autoplayInterval.value); // 清除定时器
  autoplayInterval.value = null; // 重置为null
}
