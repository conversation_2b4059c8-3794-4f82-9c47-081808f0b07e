<template>
  <div v-if="Array.isArray(poEvent)" class="flex items-center gap-[5px] flex-wrap">
    <!-- 鼠标移入打点的舆情事件，展示文本提示 -->
    <el-tooltip v-for="item in poEvent" :key="item.id" :content="item.name" placement="top" :show-after="300" :visible="item[visibleKey]">
      <div
        class="tagWrapper truncate"
        @mouseover="item[visibleKey] = $event.target.scrollWidth > $event.target.clientWidth"
        @mouseleave="item[visibleKey] = false"
      >
        <img src="@/assets/images/poManage/event.png" alt="event" class="w-[10px] h-[10px] mr-[4px]" />
        <span class="tagText truncate">{{ item.name }}</span>
      </div>
    </el-tooltip>
    <img v-if="isShowAddEvent" src="@/assets/images/poManage/add.svg" alt="" class="w-[28px] h-[28px]" @click.stop="$emit('showEvent')" />
  </div>
  <template v-else>
    <img v-if="isShowAddEvent" src="@/assets/images/poManage/add.svg" alt="" class="w-[28px] h-[28px]" @click.stop="$emit('showEvent')" />
    <span v-else class="noData">-</span>
  </template>
</template>

<script setup>
defineProps({
  poEvent: {
    type: [Array, String],
    default: () => []
  },
  isShowAddEvent: {
    type: Boolean,
    default: false
  },
  // 由于对象地址一样，为了避免列表/抽屉/弹窗同时出现 tooltip
  visibleKey: {
    type: String,
    default: "visible"
  }
});

defineEmits(["showEvent"]);
</script>

<style lang="scss" scoped>
.tagWrapper {
  height: 22px;
  line-height: 22px;
  display: flex;
  align-items: center;
  padding-left: 8px;
  padding-right: 8px;
  background: #ffffff;
  box-shadow: 0px 1px 3px 0px rgba(222, 224, 232, 0.5);
  border-radius: 14px;
  border: 1px solid #e6e6e9;
}

.tagText {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #666f80;
}
</style>
