<template>
  <div>
    <el-dialog
      :model-value="modelValue"
      title="舆情处置"
      :width="'700'"
      @close="onCancel"
    >
      <el-form
        ref="handleFormRef"
        label-position="top"
        :model="handleForm"
        :rules="HANDLE_RULES(measuresValidator)"
        status-icon
        class="custom-form !block"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="处置结果" prop="handleResult">
              <el-radio-group
                v-model="handleForm.handleResult"
                @change="handleResultChange"
              >
                <el-radio
                  v-for="r in handleResultOptions"
                  :key="r.code"
                  :label="r.label"
                  :value="r.code"
                >
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col
            :span="24"
            v-if="handleForm.handleResult === HANDLE_RESULT_CODE.FINISHED_CODE"
          >
            <el-form-item label="处置措施" prop="handleMeasures">
              <el-radio-group v-model="handleForm.handleMeasures">
                <el-radio
                  v-for="m in handleMeasuresOptions"
                  :label="m.label"
                  :value="m.code"
                >
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="descriptionLabel" prop="description">
              <el-input
                v-model="handleForm.description"
                show-word-limit
                maxlength="500"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 6 }"
                placeholder="请输入处置说明"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onHandle"> 提交 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { HANDLE_RULES } from "../config/index.js";
import { HANDLE_RESULT_CODE } from "../config/constant.js";
import {
  getHandleResultOptions,
  getHandleMeasuresOptions,
} from "@/api/publicOpinionCollaboration/index.js";
import { deepClone } from "@/utils";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(["update:modelValue", "submit"]);
const descriptionLabel = computed(() => {
  return handleForm.value.handleResult === HANDLE_RESULT_CODE.FINISHED_CODE
    ? "处置说明"
    : "进度说明";
});

const handleFormRef = ref(null);
const handleForm = ref({
  handleResult: "RESOLVE_FOLLOW",
  handleMeasures: "",
  description: "",
});

const handleResultOptions = ref([]);
const handleMeasuresOptions = ref([]);
const getSelectOptions = async () => {
  const promise = [getHandleResultOptions(), getHandleMeasuresOptions()];
  const resArr = await Promise.all(promise);
  if (resArr.every((ele) => ele.code === 200)) {
    handleResultOptions.value = resArr[0].data || [];
    handleMeasuresOptions.value = resArr[1].data || [];
  }
};

const handleResultChange = () => {
  if (handleForm.value.handleResult === HANDLE_RESULT_CODE.CONTINUE_CODE) {
    handleForm.value.handleMeasures = "";
  } else {
    handleForm.value.handleMeasures = "OPT1";
  }
};

const measuresValidator = (rule, value, callback) => {
  if (
    handleForm.value.handleResult === HANDLE_RESULT_CODE.FINISHED_CODE &&
    value === ""
  ) {
    callback(new Error("请选择处置措施"));
  } else {
    callback();
  }
};

const onCancel = () => {
  emit("update:modelValue", false);
  handleFormRef.value.resetFields();
};

const onHandle = () => {
  emit("submit", deepClone(handleForm.value));
  handleFormRef.value.resetFields();
};

defineExpose({
  handleFormRef,
});

getSelectOptions();
</script>

<style lang="scss" scoped>
:deep(.custom-form) {
  .el-form-item--default {
    margin-bottom: 12px;
  }

  .el-form-item__label,
  .el-radio__label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
  }
}

:deep(.el-dialog) {
  padding: 24px;
  .el-dialog__header {
    padding-left: 0;
  }
  .el-dialog__title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
  }
}
</style>
