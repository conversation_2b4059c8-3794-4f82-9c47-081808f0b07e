server {
  listen      80;
  listen      [::]:80;
  server_name _;
 

  # security
  include     nginxconfig.io/security.conf;

  # index.html fallback
  location /patrol-opinion {
     alias       /var/www/public;
      try_files $uri $uri/ /patrol-opinion/index.html;
  }

  # reverse proxy
  location /portal-api/ {
      proxy_pass http://${API_GATEWAY_HOST}:${API_GATEWAY_PORT}/;
      include    nginxconfig.io/proxy.conf;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; # 添加真实客户端 IP
      proxy_set_header X-Real-IP $remote_addr;                    # 另一个常用的真实 IP 头

     # add_header X-Forwarded-For $proxy_add_x_forwarded_for;
     # add_header X-Real-IP $remote_addr;
  }

    location /websocket {                        
        proxy_pass http://${API_GATEWAY_HOST}:${API_GATEWAY_PORT}/websocket;  
        proxy_http_version 1.1;                                     
        proxy_set_header Upgrade $http_upgrade;                     
        proxy_set_header Connection "Upgrade";                      
        proxy_set_header Host $host;                                
        proxy_set_header X-Real-IP $remote_addr;                    
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;                 
    } 

   location /low-browser {                 
   root /opt;                              
}   
  # additional config
  #include nginxconfig.io/general.conf;
}
