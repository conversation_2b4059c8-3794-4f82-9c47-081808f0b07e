<template>
  <header class="headerInfo-container relative flex items-center">
    <!-- 顶部动效 -->
    <div class="w-full h-full overflow-hidden absolute z-[8]">
      <video autoplay loop muted class="topVideoWrapper">
        <source src="@/assets/video/topBg.webm" type="video/webm" />
        Your browser does not support the video tag.
      </video>
    </div>

    <div class="logoWrapper flex items-center">
      <img src="@/assets/images/home/<USER>" alt="logo" class="logoImg" />
      <span class="logoTitle">{{ defaultSettings.title }}</span>
    </div>

    <div
      class="headerRightWrapper flex items-center flex-1 absolute z-[9] right-[0]"
      :class="[isBindWechat ? 'justify-end' : 'justify-between']"
    >
      <section class="noticeWrapper flex items-center" v-if="!isBindWechat">
        <img
          src="@/assets/images/home/<USER>"
          alt="notice"
          class="noticeIcon"
        />
        <span class="noticeContent"
          >您当前的账号当前暂未绑定微信，会影响接收微信通知，请点击去绑定按钮进行绑定</span
        >
        <el-popover
          trigger="click"
          placement="bottom"
          popper-class="bindQRcodeWrapper"
        >
          <template #reference>
            <div class="flex items-center cursor-pointer bindWrapper">
              <span class="bindBtn">去绑定</span>
              <img
                src="@/assets/images/home/<USER>"
                alt="noticeArrow"
                class="bindArrowIcon"
              />
            </div>
          </template>
          <img :src="weixinQRcode" alt="QRcode" class="weixinQRImg" />
        </el-popover>
      </section>
      <UserDropdown
        popper-class="homeUserDropdown"
        :font-style="{ color: '#FEFFFF' }"
      />
    </div>
  </header>
</template>

<script setup>
import defaultSettings from "@/settings";
import UserDropdown from "@/layout/components/UserDropdown";
import { createWeixinQrCode, judgeBindWechat } from "@/api/home";
import { getToken } from "@/utils/auth";

const isBindWechat = ref(true); // 是否绑定微信
const weixinQRcode = ref(""); // 微信公众号二维码
const socket = ref(null);

/**
 * 判断是否绑定微信
 */
async function judgeIsBindWechat() {
  const res = await judgeBindWechat();
  if (res.code === 200) {
    isBindWechat.value = res.data;
  }
}

/**
 * 获取微信公众号二维码
 */
async function getWeixinQRcode() {
  const res = await createWeixinQrCode();
  if (res.code === 200) {
    weixinQRcode.value = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${res.data.ticket}`;
  }
}

/**
 * 实时监听微信是否绑定
 */
function realTimeWatchWechat() {
  const token = getToken();
  socket.value = new WebSocket(
    import.meta.env.VITE_APP_WEBSOCKET_URL + "/message",
    [encodeURI(token)]
  );

  socket.value.onopen = function () {
    console.log("连接已打开");
  };

  socket.value.onmessage = function (event) {
    if (event.data === "绑定微信成功") {
      isBindWechat.value = true;
    }
  };

  socket.value.onerror = function (error) {
    console.error(error);
  };

  socket.value.onclose = function () {
    console.log("连接已关闭");
  };
}

judgeIsBindWechat();
getWeixinQRcode();

onMounted(() => {
  realTimeWatchWechat();
});

onBeforeUnmount(() => {
  socket.value.close();
  socket.value = null;
});
</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;

.headerInfo-container {
  width: 100%;
  height: 100%;

  .topVideoWrapper {
    width: px2vw(1920);
    height: px2vw(1080);
  }

  .logoWrapper {
    margin-left: px2vw(47);
    .logoImg {
      width: px2vw(40);
      margin-right: px2vw(23);
    }
    .logoTitle {
      font-family: "YouSheBiaoTiHei";
      font-family: YouSheBiaoTiHei;
      font-size: px2vw(38);
      color: #eaf6ff;
      line-height: px2vw(49);
      text-shadow: 0 px2vw(2) px2vw(4) #1c5da0;
    }
  }

  .headerRightWrapper {
    width: px2vw(1300);
    height: px2vw(65);
    padding: 0 px2vw(52) 0 px2vw(106);
    background: linear-gradient(
      270deg,
      rgba(48, 137, 249, 0) 0%,
      rgba(48, 136, 248, 0.3) 100%
    );
    clip-path: polygon(0 100%, px2vw(70) 0, 100% 0, 100% 100%);

    .noticeWrapper {
      .noticeIcon {
        width: px2vw(20);
        height: px2vw(19);
        margin-right: px2vw(10);
      }

      .noticeContent {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: px2vw(16);
        color: #b9d3f5;
        line-height: px2vw(22);
      }

      .bindWrapper {
        margin-left: px2vw(30);
        .bindBtn {
          font-family: YouSheBiaoTiHei;
          font-size: px2vw(15);
          color: #60a4ff;
          line-height: px2vw(20);
          margin-right: px2vw(6);
        }
        .bindArrowIcon {
          width: px2vw(15);
          height: px2vw(19);
        }
      }
    }

    :deep(.userDropdown-container) {
      .avatar-wrapper {
        gap: px2vw(5);
        padding-right: px2vw(35);
        img {
          width: px2vw(30);
        }
        span {
          color: #b9d3f5;
          font-size: px2vw(13);
          margin-left: px2vw(10);
        }
        i {
          color: #6c8990;
          font-size: px2vw(12);
        }
      }
    }
  }
}
</style>
