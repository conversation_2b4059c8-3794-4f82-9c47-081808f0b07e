<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="全部处置舆情" name="all" />
      <el-tab-pane name="mine">
        <template #label>
          <span class="mr-[5px]">我的任务</span>
          <span class="todoNum flex-center">{{ poManageStore.myTaskNum }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <AllHandlePo v-show="activeName === 'all'" class="h-[calc(100%-60px)]" />
    <MyHandlePo v-show="activeName === 'mine'" class="h-[calc(100%-60px)]" />
  </div>
</template>

<script setup>
import AllHandlePo from "./components/AllHandlePo.vue";
import MyHandlePo from "./components/MyHandlePo.vue";
import { usePoManageStore } from "@/store/modules/poManage.js";
const poManageStore = usePoManageStore();
poManageStore.getMyTaskNum();

const activeName = ref("all"); // 激活的tab

// 从首页进，展示我的tab
if (history.state.tab) {
  activeName.value = history.state.tab;
}

provide("activeTabName", activeName);
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: absolute; // 撑开高度
  padding: 0 20px 10px;

  :deep(.el-tabs) {
    .el-tabs__header {
      margin-bottom: 10px;
      .el-tabs__nav {
        .el-tabs__item {
          height: 51px;
          display: flex;
          justify-content: center;
          align-items: center;

          font-family:
            PingFangSC,
            PingFang SC;
          font-size: 16px;
          font-weight: 400;
          color: #8f959e;

          &.is-active {
            font-weight: 600;
            color: #1f2329;
          }
        }
      }
    }

    .todoNum {
      width: 25px;
      height: 16px;
      background: url("@/assets/images/home/<USER>") no-repeat;
      background-size: cover;
      background-position: center;

      font-family: DINAlternate;
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      letter-spacing: 0;
    }
  }
}
</style>
