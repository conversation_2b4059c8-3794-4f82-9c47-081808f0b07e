/*
 * @Author: 周杰 <EMAIL>
 * @Date: 2025-03-07 16:59:11
 * @LastEditors: 周杰 <EMAIL>
 * @LastEditTime: 2025-07-22 15:25:02
 * @FilePath: \patrol-intel-web\src\views\publicOpinionManage\config\formConfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const FEEDBACK_RULES = () => {
  return {
    // originHandleUnit: [
    //   {
    //     required: true,
    //     message: "请输入处置结果",
    //     trigger: "blur",
    //   },
    // ],
    newHandleUnit: [
      {
        required: true,
        message: "请选择新处置单位",
        trigger: ["change", "blur"],
      },
    ],
    auditPerson: [
      {
        required: true,
        message: "请选择审核人",
        trigger: ["change", "blur"],
      },
    ],
  };
};

export const TASK_DISPATCH_RULES = (processName) => {
  return {
    processUnit: [
      {
        required: true,
        message: `请选择${processName}单位`,
        trigger: ["blur", "change"],
      },
    ],
    deadlineDate: [
      {
        required: true,
        message: "请选择截止时间",
        trigger: ["blur", "change"],
      },
    ],
  };
};
