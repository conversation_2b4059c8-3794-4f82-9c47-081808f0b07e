<template>
  <div class="app-item" @click="handleClick(i)" v-for="i in applists">
    <el-avatar :size="40" :src="getAssetURL(i.img)" />
    <span>{{ i.title }}</span>
  </div>
</template>

<script setup>
let modules = import.meta.glob("@/assets/home/<USER>")

defineProps({
  applists: {
    type: Array,
    required: true,
  },
});
const emit = defineEmits(["handleClick"]);
const handleClick = (i) => {
  emit("handleClick", i);
};
// 获取静态图片
const getAssetURL = (image) => {
  // 参数一: 相对路径  // 参数二: 当前路径的URL
  return new URL(`../../../../assets/home/<USER>
}
</script>

<style scoped>
.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  height: 80px;
  width: 80px;
  justify-content: center;
  min-width: 80px;
  cursor: pointer;

  span {
    font-size: 13px;

    &:hover {
      color: #fff;
      text-shadow: #808080 1px 2px 3px;
    }
  }
}
</style>
