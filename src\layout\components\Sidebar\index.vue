<template>
  <div :class="{ 'has-logo': showLogo }" :style="{ backgroundColor: '#fff' }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="'#fff'"
        :text-color="
          sideTheme === 'theme-dark'
            ? variables.menuColor
            : variables.menuLightColor
        "
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
        <!-- <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        /> -->
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.module.scss";
import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";

const route = useRoute();
const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
// 当前环境
const isDev = process.env.NODE_ENV === "development";
// 判断当前路由(u)是否是这个路由的子路由
const isThisRoute = (itemroute) => {
  return itemroute.path === route.matched[0].path;
};

const sidebarRouters = computed(() => permissionStore.sidebarRouters);
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
// const isCollapse = computed(() => !appStore.sidebar.opened);
const isCollapse = false;

const activeMenu = computed(() => {
  const { meta, path } = route;

  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }

  if (path && path === "/rumorOpinion/rumorDetail") {
    return "/rumorOpinion/rumorOpinion";
  }

  if (path && path === "/keyObj/waterArmyDetail") {
    return "/keyObj/waterArmy";
  }

  return path;
});
</script>
