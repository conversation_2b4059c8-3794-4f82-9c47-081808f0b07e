<template>
  <nav class="home-side-nav">
    <li v-for="menu in menus" :key="menu.id" :class="{ active: isActive(menu) }" class="pointer"
      @click="navigateTo(menu)">
      <svg-icon class="home-icon" :icon-class="isActive(menu)?(menu.icon + '-a'):menu.icon" />
      {{ menu.name }}
    </li>
  </nav>
</template>

<script setup>
import { ref } from "vue";
import { menus } from './menuList.js'
const route = useRoute();
const router = useRouter();
const setUrlParameter = (key, value) => {
  const query = { ...route.query };
  query[key] = value;
  router.replace({ query });
};

// onMounted(() => {
//   if (!router.currentRoute.value.tab) {
//     setUrlParameter('tab', 'gzt')
//   }
// })
// 过度效果
const activeMenu = ref(null);
const props = defineProps({
  currentMenu: Object,
});
// const emit = defineEmits(["update:currentMenu"]);
function navigateTo(menu) {
  activeMenu.value = menu;
  setUrlParameter('tab', menu.tab)
  // emit("update:currentMenu", menu);
}
watch(() => route.query.tab, (n) => {
  let it = menus.find((item) => item.tab == n);
  if (it) { activeMenu.value = it; }
  else {
    setUrlParameter('tab', 'gzt')
    activeMenu.value = menus[0]
  }
}, { immediate: true })
// onMounted(() => {
//   // 默认工作台
//   navigateTo(menus[0]);
// });
// watch(() => activeMenu.value, () => {
//   if (!activeMenu.value) {
//     navigateTo(menus[0]);
//   }
// })
defineExpose({ setUrlParameter })
function isActive(menu) {
  return activeMenu.value?.id === menu.id;
}
</script>

<style scoped lang="scss">
.home-side-nav {
  width: 100%;
  padding: 25px 10px;
}

nav li {
  padding: 10px;
  transition: all 0.5s ease;
  list-style-type: none;
  opacity: 0.67;
  font-size: 14px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  .home-icon {
    color: transparent;
    opacity: 1;
    width: 65px;
    height: 71px;
  }
}

nav li:hover,
nav li.active {
  opacity: 1;
  background-image: url('@/assets/home/<USER>');
  background-repeat: no-repeat;
  background-position: right -5px  center;
  background-size: 30% 30%;
}
</style>
