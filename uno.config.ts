// uno.config.ts
import { defineConfig, presetUno } from "unocss";
import transformerDirectives from "@unocss/transformer-directives";

export default defineConfig({
  // ...UnoCSS options
  presets: [presetUno()],
  transformers: [transformerDirectives()],
  theme: {
    fontFamily: {
      sans: "ui-sans-serif, system-ui",
      pf: "PingFang SC, sans-serif", // 使用苹方简体作为自定义字体
      PangMenZhengDao: "PangMenZhengDao, sans-serif", // 使用庞门正道字体
      DA: "DINAlternate, DINAlternate, sans-serif",
      HuXiaoBo: "HuXiaoBo, sans-serif",
      DIN: "DIN, sans-serif",
    },
  },
  shortcuts: {
    "flex-center": "flex items-center justify-center",
    "flex-col-center": "flex flex-col items-center justify-center",
    "flex-row-center": "flex flex-row items-center justify-center",
    "absolute-transform-center":
      "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
    bordered: "border border-[#eee] border-solid",
  },
});
