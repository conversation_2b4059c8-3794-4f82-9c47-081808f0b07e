import fbIcon from "@/assets/images/poManage/fb.png";
import zyIcon from "@/assets/images/poManage/zy.png";
import lcIcon from "@/assets/images/poManage/lc.png";
import hbIcon from "@/assets/images/poManage/hb.png";
import czIcon from "@/assets/images/poManage/cz.png";

import {
  HANDLE_STATUS,
  ARTICLE_STATUS,
  PROCESS_STATUS,
  PROCESS_RESULT_CODE,
  HANDLE_RESULT_CODE,
} from "./constant.js";

export const APPRAISAL_RULES = {
  isTrue: {
    required: true,
    message: "请选择属实情况",
    trigger: ["change", "blur"],
  },
};

export const HANDLE_RULES = (measuresV) => {
  return {
    handleResult: [
      {
        required: true,
        message: "请选择处置结果",
        trigger: ["change", "blur"],
      },
    ],
    handleMeasures: [
      {
        required: true,
        validator: measuresV,
        trigger: ["change", "blur"],
      },
    ],
  };
};

export const HANDLE_RESULT_OPTIONS = [
  {
    label: "继续跟进",
    code: HANDLE_RESULT_CODE.CONTINUE_CODE,
  },
  {
    label: "完成处置",
    code: HANDLE_RESULT_CODE.FINISHED_CODE,
  },
];

// 舆情流转-舆情流转-流转状态
export const PROCESS_STATUS_OPTIONS = [
  {
    label: "正常",
    value: PROCESS_STATUS.NORMAL_STATUS,
    color: "#108A5B",
    bgColor: "#E1FAF2",
  },
  {
    label: "延期提交",
    value: PROCESS_STATUS.DELAY_STATUS,
    color: "#fff",
    bgColor: "#D9001B",
  },
];

// 舆情流转-舆情处置-处置结果选项
export const PROCESS_RESULT_OPTIONS = [
  {
    id: 14,
    categoryCode: "RESOLVE",
    code: PROCESS_RESULT_CODE.CONTINUE_CODE,
    label: "继续跟进",
  },
  {
    id: 15,
    categoryCode: "RESOLVE",
    code: PROCESS_RESULT_CODE.FINISHED_CODE,
    label: "完成处置",
  },
];

// 舆情流转-舆情类型
export const PROCESS_TYPE_OPTIONS = [
  {
    id: 1,
    code: "CZ",
    label: "舆情cz查看",
    stepType: "1",
  },
  {
    id: 2,
    code: "LC",
    label: "舆情lc查看",
    stepType: "3",
  },
  {
    id: 3,
    code: "HB",
    label: "舆情hb查看",
    stepType: "1",
  },
];

// 处置状态
export const HANDLE_STATUS_LIST = [
  {
    label: "待研判",
    value: HANDLE_STATUS.WAIT_HANDLE,
    color: "#1749E0",
    bgColor: "#E6ECFF",
  },
  {
    label: "跟进中",
    value: HANDLE_STATUS.ING_HANDLE,
    color: "#D45D27",
    bgColor: "#FCF1E4",
  },
  {
    label: "已完成",
    value: HANDLE_STATUS.FINISH_HANDLE,
    color: "#108A5B",
    bgColor: "#E1FAF2",
  },
];

// 贴文状态
export const ARTICLE_STATUS_LIST = [
  { label: "未删除", value: ARTICLE_STATUS.NO_DEL },
  { label: "已删除", value: ARTICLE_STATUS.FINISH_DEL },
];

// 舆情流转-舆情类型
export const PROCESS_TITLE_ICON = [
  {
    code: "NEW",
    icon: fbIcon,
  },
  {
    code: "VIEW",
    icon: zyIcon,
  },
  {
    code: "CHECK",
    icon: lcIcon,
  },
  {
    code: "REPORT",
    icon: hbIcon,
  },
  {
    code: "RESOLVE",
    icon: czIcon,
  },
];
