import request from '@/utils/request';
// 列表
export function getList(data) {
  return request({
    url: '/system/scoreConfig/list',
    method: 'post',
    data,
  });
}
// 保存
export function save(data) {
  return request({
    url: '/system/scoreConfig/save',
    method: 'post',
    data,
  });
}
// 删除
export function del(data) {
  return request({
    url: '/system/scoreConfig/delete',
    method: 'post',
    data,
  });
}
// 更新排序
export function updateSort(data) {
  return request({
    url: '/system/scoreConfig/updateOrder',
    method: 'post',
    data,
  });
}
