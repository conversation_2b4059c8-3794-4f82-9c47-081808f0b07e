export const applicationColumns = [
  {
    label: '序号',
    type: 'index',
    width: '60px',
  },
  {
    prop: 'appName',
    label: '应用名称',
    align: 'left',
    width: '130px',
  },
  {
    prop: 'appUrl',
    label: '应用链接',
    align: 'left',
    width: '380px',
  },
  {
    prop: 'appImg',
    label: '图标',
    align: 'left',
    slotName: 'iconSlot',
  },
  {
    prop: 'sysName',
    label: '所属系统',
    align: 'left',
  },
  {
    prop: 'appType',
    label: '应用类别',
    align: 'left',
    slotName: 'appTypeSlot',
  },
  {
    prop: 'sort',
    label: '排序',
    align: 'center',
  },
  {
    prop: 'remark',
    label: '备注',
    align: 'left',
  },
  {
    prop: 'status',
    label: '是否启用',
    align: 'left',
    slotName: 'isEnableSlot',
  },
];

export const operationFormRule = {
  appName: [
    {
      required: true,
      message: '请输入系统名称',
      trigger: 'blur',
    },
  ],
  sort: [
    {
      required: true,
      message: '请输入应用排序',
      trigger: 'blur',
    },
  ],
  menuId: [
    {
      required: true,
      message: '请选择父级菜单',
      trigger: 'change',
    },
  ],
  sysId: [
    {
      required: true,
      message: '请选择所属系统',
      trigger: 'change',
    },
  ],
  appType: [
    {
      required: true,
      message: '请选择应用类别',
      trigger: 'change',
    },
  ],
  appUrl: [
    {
      required: true,
      message: '请输入系统地址',
      trigger: 'blur',
    },
  ],
  category:[{
    required: true,
    message: '请选择类别',
    trigger: 'change',
  }],
  attachments: [{ required: true, message: '请上传安装包', trigger: ['change', 'blur'] }],
  // icon: [{
  //     required: true,
  //     message: '请输入图标',
  //     trigger: 'blur'
  // }],
  status: [
    {
      required: true,
      message: '请选择是否启用',
      trigger: 'change',
    },
  ],
};
