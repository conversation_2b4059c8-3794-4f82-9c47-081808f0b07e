/**
 * 二级页-表格列
 */
export const PO_MANAGE_DETAIL_COLUMNS = [
  {
    prop: "createTime",
    label: "报送时间",
    minWidth: "90px",
    align: "left",
    slotName: "createTime"
  },
  {
    prop: "platformType",
    label: "发布平台",
    align: "left",
    minWidth: "100px"
  },
  {
    prop: "poLink",
    label: "贴文链接",
    minWidth: "220px",
    align: "left",
    slotName: "poLink"
  },
  {
    prop: "netizenNickname",
    label: "网民昵称",
    align: "left",
    minWidth: "140px",
    slotName: "netizenNickname"
  },
  {
    prop: "poContent",
    label: "舆情内容",
    minWidth: "230px",
    align: "left",
    slotName: "poContent"
  },
  {
    prop: "poImg",
    label: "图片",
    minWidth: "160px",
    slotName: "poImg"
  },
  {
    prop: "workUnit",
    label: "上报单位",
    align: "left",
    minWidth: "150px",
    slotName: "workUnit"
  },
  {
    prop: "happenLocation",
    label: "舆情属地",
    align: "left",
    minWidth: "140px"
  },
  {
    prop: "isSensitive",
    label: "是否为敏感舆情",
    minWidth: "140px",
    slotName: "isSensitive"
  },
  {
    prop: "articleStatus",
    label: "贴文状态",
    minWidth: "100px",
    slotName: "articleStatus"
  }
];
