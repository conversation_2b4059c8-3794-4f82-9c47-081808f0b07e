import request from "@/utils/request";

// 谣言中心
// 舆情列表
export function getRumorOpinionList(query) {
  return request({
    url: "/business/rumorOpinion/list/rumor",
    method: "get",
    params: query
  });
}

// 舆情详情
export function getAppraisalDetailInfo(id) {
  return request({
    url: `/business/rumorOpinion/${id}`,
    method: "get",
  });
}

// 谣言鉴定
export function appraisalRumor(data) {
  return request({
    url: "/business/rumorOpinion",
    method: "put",
    data,
  });
}
