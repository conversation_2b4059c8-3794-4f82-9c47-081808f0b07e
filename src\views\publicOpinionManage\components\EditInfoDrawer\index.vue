<template>
  <el-drawer
    class="editInfoDrawer-container"
    :model-value="modelValue"
    title="title"
    :with-header="false"
    :size="1000"
    modal-class="editInfoDrawerModal"
    :before-close="onClose"
  >
    <img
      class="closeIcon"
      src="@/assets/images/poManage/close.png"
      alt=""
      @click="onClose"
    />
    <div class="drawerWrapper">
      <div class="w-[calc(100%-375px)] pt-[16px] pb-[21px] pl-[26px] pr-[10px]">
        <header class="flex justify-between items-center mb-[14px] pr-[14px]">
          <div class="flex items-center">
            <!-- <svg-icon icon-class="opinion-handle-title" class="!w-[20px] !h-[20px] mr-[8px]" /> -->
            <span class="text-[12px] text-[#8592A6]">{{ poInfo.poId }}</span>
          </div>

          <div class="flex gap-x-[20px]">
            <div class="cursor-pointer" v-if="drawerType === 'view' && !isEdit">
              <span class="text-[14px] text-[#0052D9]" @click="isEdit = true"
                >编辑</span
              >

              <!-- <el-dropdown trigger="click">
              <svg-icon icon-class="referTo" class="!w-[20px] !h-[20px]" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item><span>转至钉钉群</span></el-dropdown-item>
                  <el-dropdown-item><span>转至企业微信</span></el-dropdown-item>
                  <el-dropdown-item><span @click="$emit('showDialog', getPoProcess)">转至个人</span></el-dropdown-item>
                </el-dropdown-menu>
              </template>
</el-dropdown> -->
            </div>
            <div
              class="cursor-pointer flex items-center"
              v-if="
                currentDrawerType === 'handle' &&
                !poInfo?.feedBackId &&
                !handleStatusIsIng
              "
              @click="handleFeedback"
            >
              <svg-icon
                icon-class="feedback"
                class="!w-[11px] !h-[11px] mr-[5px]"
              />

              <span class="feedback">反馈</span>
            </div>
          </div>
        </header>
        <PoInfo
          ref="poInfoRef"
          :is-edit="isEdit"
          :po-info="poInfo"
          :form-data="formData"
          :model-value="modelValue"
          :class="[isShowBtn ? 'h-[calc(100%-80px)]' : 'h-[calc(100%-40px)]']"
        />

        <div class="mt-[20px]" v-if="isEdit">
          <el-button class="addDialogBtn" @click="onOk">提交</el-button>
          <el-button class="cancleDialogBtn" @click="onCancle">取消</el-button>
        </div>

        <div class="mt-[20px]" v-if="currentDrawerType === 'handle'">
          <el-button
            v-if="showDispatchBtn"
            class="handleBtn handleBtn--primary"
            @click="onDispatch(processName.substring(2))"
            >{{ processName.substring(2) }}任务下发</el-button
          >
          <el-button
            :class="[
              'handleBtn',
              showDispatchBtn ? 'handleBtn--sub' : 'handleBtn--primary',
            ]"
            @click="onHandle"
            >{{ processName }}</el-button
          >
        </div>
      </div>
      <PoProcess
        ref="poProcessRef"
        :loading="loading"
        :process-list="processList"
      />
    </div>

    <!-- 处理弹框 -->
    <HandleDialog
      v-model="showHandleDialog"
      :po-info="poInfo"
      :handle-measures-options="handleMeasuresOptions"
      @refreshData="refresh"
      @changeDrawerType="changeDrawerType"
      @changeHandleStatusIsIng="changeHandleStatusIsIng"
    />

    <!-- 舆情反馈 -->
    <FeedbackDialog
      ref="feedbackDialogRef"
      @refreshData="refreshAndClose"
    ></FeedbackDialog>

    <!-- 反馈审核 -->
    <AuditFeedbackDialog
      ref="auditFeedbackDialogRef"
      @refreshData="refreshAndClose"
    ></AuditFeedbackDialog>

    <!-- 任务下发 -->
    <TaskDispatchDialog
      ref="taskDispatchDialogRef"
      @refreshData="refreshAndClose"
    ></TaskDispatchDialog>
  </el-drawer>
</template>

<script setup name="EditInfoDrawer">
import PoInfo from "./components/PoInfo.vue";
import PoProcess from "./components/PoProcess.vue";
import HandleDialog from "./components/HandleDialog.vue";
import FeedbackDialog from "./components/FeedbackDialog.vue";
import AuditFeedbackDialog from "./components/AuditFeedbackDialog.vue";
import TaskDispatchDialog from "./components/TaskDispatchDialog.vue";
import {
  editPoInfo,
  getProcessList,
  createProcess,
} from "@/api/poManage/poInfo";
import {
  PO_PROCESS_LIST,
  PROCESS_TYPE,
  VIEW_PROCESS_CODE,
} from "../../config/mapRel";
import { HANDLE2_STATUS } from "@/views/publicOpinionManage/config/constant";
import { getHandleMeasuresOptions } from "@/api/publicOpinionCollaboration/index.js";
import { usePoManageStore } from "@/store/modules/poManage.js";
import { setAddress } from "@/utils/area.js";
import useUserStore from "@/store/modules/user.js";
import { getDict } from "@/utils/dict";

const { proxy } = getCurrentInstance();
const { department_type } = proxy.useDict("department_type");

const taskDispatchDialogRef = ref(null);
const poManageStore = usePoManageStore();
const userStore = useUserStore();

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  // 取值: edit view onlyView handle
  drawerType: {
    type: String,
    required: true,
  },
  poInfo: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["update:modelValue", "showDialog", "refreshData"]);

const currentDrawerType = ref(""); // 当前抽屉类型，目前只用于 处置操作后把类型转化为view模式

watch(
  () => props.drawerType,
  (val) => {
    if (val) {
      currentDrawerType.value = val;
    }
  }
);

const isEdit = ref(false); // 是否是编辑模式
const poInfoRef = ref();
const poProcessRef = ref();
const formData = ref({}); // 编辑时的表单数据
const loading = ref(false);
const processList = ref([]);
const showHandleDialog = ref(false);
const handleMeasuresOptions = ref([]); // 处置措施选项

const showDispatchBtn = computed(() => {
  // console.log(
  //   "getDict(userStore.deptType, department_type)",
  //   getDict(userStore.deptType, department_type.value)
  // );
  return getDict(userStore.deptType, department_type.value) === "区县";
  // return true;
}); // 是否显示派发按钮

const isShowBtn = computed(() => isEdit.value || props.drawerType === "handle");
const processLabel = computed(
  () => (val) =>
    PO_PROCESS_LIST.find((i) => i.categoriesCode === val)?.fontLabel || ""
);
const processIcon = computed(
  () => (val) =>
    PO_PROCESS_LIST.find((i) => i.categoriesCode === val)?.icon || ""
);

const feedbackDialogRef = ref(null);
const auditFeedbackDialogRef = ref(null);

const handleStatusIsIng = ref(false); // 处置状态是否为进行中

/**
 * 每次打开抽屉：
 * 1. 判断是否是编辑模式，同时回填表单数据
 * 2. 获取 处置措施、舆情流程数据
 */
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      currentDrawerType.value = props.drawerType;
      handleStatusIsIng.value = HANDLE2_STATUS.ING.includes(
        props.poInfo.handleStatus2
      );
      isEdit.value = props.drawerType === "edit";
      formData.value = {
        poName: props.poInfo.poName,
        poContent: props.poInfo.poContent,
        poLink: props.poInfo.poLink,
        imgList: "",
        poEvent: Array.isArray(props.poInfo.poEvent)
          ? props.poInfo.poEvent
              .filter((i) => i.status === "1")
              .map((i) => i.id)
          : [], // 仅回显进行中的事件
        workUnit: props.poInfo.wechatDeptId,
        platformType:
          props.poInfo.platformType === "-" ? "" : props.poInfo.platformType,
        netizenNickname:
          props.poInfo.netizenNickname === "-"
            ? ""
            : props.poInfo.netizenNickname,
        netizenAccount:
          props.poInfo.netizenAccount === "-"
            ? ""
            : props.poInfo.netizenAccount,
        createTime: props.poInfo.createTime,
        publicTime: props.poInfo.publicTime,
        happenLocation:
          props.poInfo.happenLocation === "-"
            ? ""
            : setAddress(props.poInfo.happenLocation),
        poType: props.poInfo.poTypeId,
        poMediaType: props.poInfo.poMediaType?.toString(),
        isSensitive: props.poInfo.isSensitive,
      };

      await getSelectOptions();
      // [处置-我的任务]如果是转阅-待查看，将待查看变成已查看
      if (
        props.poInfo.commandCategory === PROCESS_TYPE.VIEW &&
        HANDLE2_STATUS.WAIT_VIEW.includes(props.poInfo.handleStatus2)
      ) {
        toViewed();
      } else {
        getPoProcess();
      }
    }
  }
);

const processName = computed(() => {
  return props.poInfo?.feedBackId
    ? "反馈审核"
    : processLabel.value(props.poInfo.commandCategory);
});

/**
 * 当前处置状态修改-用于控制显示反馈按钮
 */
async function changeHandleStatusIsIng(status) {
  handleStatusIsIng.value = status;
}

/**
 * 反馈按钮操作
 */
async function handleFeedback() {
  feedbackDialogRef.value.openDialog(props.poInfo);
}

/**
 * 更改抽屉类型，用于处置完成后的操作
 */
function changeDrawerType(type) {
  currentDrawerType.value = type;
}

/**
 * 处置操作
 */
async function onHandle() {
  if (props.poInfo?.feedBackId) {
    // 舆情审核操作
    auditFeedbackDialogRef.value.openDialog(props.poInfo);
  } else {
    console.log("props.poInfo", props.poInfo);
    showHandleDialog.value = true;
  }
}

/**
 * 获取处置措施
 */
async function getSelectOptions() {
  const res = await getHandleMeasuresOptions();
  if (res.code === 200) {
    handleMeasuresOptions.value = res.data || [];
  }
}

/**
 * 将待查看转为已查看，同时刷新相关数据
 */
async function toViewed() {
  const params = {
    reportIds: [props.poInfo.id],
    workId: props.poInfo.taskId,
    command: "VIEW_DONE",
  };

  const res = await createProcess(params);
  if (res.code === 200) {
    refresh();
  }
}

/**
 * 获取舆情流程数据
 */
async function getPoProcess() {
  loading.value = true;
  const res = await getProcessList({ reportId: props.poInfo.id });
  if (res.code === 200) {
    processList.value = res.data
      .filter((ele) => VIEW_PROCESS_CODE.includes(ele.command))
      .map((ele) => {
        if (ele.commandCategory === PROCESS_TYPE.NEW) {
          return {
            icon: processIcon.value(ele.commandCategory),
            type: processLabel.value(ele.commandCategory),
            time: ele.optTime,
          };
        } else if (ele.commandCategory === PROCESS_TYPE.VIEW) {
          return {
            icon: processIcon.value(ele.commandCategory),
            type: processLabel.value(ele.commandCategory),
            time: ele.optTime,
            sendTo: ele.inChargeList
              .map((i) => `${i.userInChargeName}（${i.deptInChargeName}）`)
              .join("、"),
            selfName: ele.optUserName,
          };
        } else {
          // LC HB CZ
          const continueCode = PO_PROCESS_LIST.filter(
            (i) => i.CONTINUE_CODE
          ).map((i) => i.CONTINUE_CODE);
          const finishCode = PO_PROCESS_LIST.filter((i) => i.FINISHED_CODE).map(
            (i) => i.FINISHED_CODE
          );
          const feedbackProcess = [...continueCode, ...finishCode];
          console.log("ele", ele);

          if (ele.commandOpt === "DISPATCH") {
            return {
              icon: processIcon.value(ele.commandCategory),
              type:
                processLabel.value(ele.commandCategory)?.substring(2) +
                "任务下发",
              processUnit: ele.inChargeList
                .map((i) => i.deptInChargeName)
                .filter((item, index, self) => self.indexOf(item) === index)
                .join("、"), // 针对deptInChargeName去重后再用、连接
              handler: ele.inChargeList
                .map((i) => `${i.userInChargeName}（${i.deptInChargeName}）`)
                .join("、"),
              selfName: ele.optUserName,
              taskDeadline: ele.deadlineTime,
              isOverdue:
                new Date(ele.optTime).getTime() >
                new Date(ele.deadlineTime).getTime(), // 是否舆逾期
            };
          } else {
            if (feedbackProcess.includes(ele.command)) {
              let handleResult = "";
              if (continueCode.includes(ele.command)) {
                handleResult = "继续跟进";
              } else {
                handleResult =
                  "完成" +
                  PO_PROCESS_LIST.find(
                    (i) =>
                      i.CONTINUE_CODE === ele.command ||
                      i.FINISHED_CODE === ele.command
                  ).fontText;
              }
              return {
                icon: processIcon.value(ele.commandCategory),
                type: processLabel.value(ele.commandCategory) + "反馈",
                dealTime: ele.deadlineTime,
                handleTime: ele.optTime,
                handler: {
                  name: ele.userInChargeName,
                  unitName: ele.deptInChargeName,
                },
                handleResult,
                handleOpt:
                  handleMeasuresOptions.value.find(
                    (i) => i.code === ele.commandOpt
                  )?.label || "",
                handleDesc: ele.commandOptDesc,
                isOverdue:
                  new Date(ele.optTime).getTime() >
                  new Date(ele.deadlineTime).getTime(), // 是否舆逾期
              };
            } else {
              return {
                icon: processIcon.value(ele.commandCategory),
                type: processLabel.value(ele.commandCategory),
                time: ele.optTime,
                handler: ele.inChargeList
                  .map((i) => `${i.userInChargeName}（${i.deptInChargeName}）`)
                  .join("、"),
                dealTime: ele.deadlineTime,
                selfName: ele.optUserName,
              };
            }
          }
        }
      });
  }
  console.log("processList.value", processList.value);

  loading.value = false;
}

/**
 * 关闭抽屉
 */
function onClose() {
  reset();
  emit("update:modelValue", false);
}

/**
 * 取消按钮
 */
function onCancle() {
  if (props.drawerType === "view" && isEdit.value) {
    // 查看抽屉，进入编辑状态后再点击取消按钮，仅退出查看状态
    isEdit.value = false;
    resetEditData();
  } else {
    onClose();
  }
}

/**
 * 将编辑状态下修改的数据还原
 */
function resetEditData() {
  formData.value = {
    poName: props.poInfo.poName,
    poContent: props.poInfo.poContent,
    poLink: props.poInfo.poLink,
    imgList: "",
    poEvent: Array.isArray(props.poInfo.poEvent)
      ? props.poInfo.poEvent.filter((i) => i.status === "1").map((i) => i.id)
      : [],
    workUnit: props.poInfo.wechatDeptId,
    platformType:
      props.poInfo.platformType === "-" ? "" : props.poInfo.platformType,
    netizenNickname:
      props.poInfo.netizenNickname === "-" ? "" : props.poInfo.netizenNickname,
    netizenAccount:
      props.poInfo.netizenAccount === "-" ? "" : props.poInfo.netizenAccount,
    createTime: props.poInfo.createTime,
    publicTime: props.poInfo.publicTime,
    happenLocation:
      props.poInfo.happenLocation === "-"
        ? ""
        : setAddress(props.poInfo.happenLocation),
    poType: props.poInfo.poTypeId,
    poMediaType: props.poInfo.poMediaType?.toString(),
    isSensitive: props.poInfo.isSensitive,
  };

  poInfoRef.value.curImg = props.poInfo.poImg;
}

/**
 * 任务下发
 */
async function onDispatch(processName) {
  await taskDispatchDialogRef.value.openDialog({
    processName,
    poInfo: props.poInfo,
  });
}

/**
 * 提交
 */
async function onOk() {
  if (!formData.value.poName) {
    proxy.$modal.msgWarning(`请填写舆情标题`);
    return;
  }

  if (!formData.value.poContent) {
    proxy.$modal.msgWarning(`请填写舆情内容`);
    return;
  }

  const oldImgUrl =
    poInfoRef.value.curImg === "-" ? [] : poInfoRef.value.curImg; // 之前上传的img
  const newImgUrl =
    formData.value.imgList === "" ? [] : formData.value.imgList.split(","); // 新上传的img
  const photoUrl = [...oldImgUrl, ...newImgUrl].join(",");

  const params = {
    id: props.poInfo.id,
    title: formData.value.poName,
    content: formData.value.poContent,
    linkUrl: formData.value.poLink,
    photoUrl,
    eventIds: formData.value.poEvent,
    wechatDeptId:
      formData.value.workUnit === "-"
        ? ""
        : formData.value.workUnit?.toString(),
    platformTag:
      formData.value.platformType === "-" ? "" : formData.value.platformType,
    netizenNickname:
      formData.value.netizenNickname === "-"
        ? ""
        : formData.value.netizenNickname,
    netizenAccount:
      formData.value.netizenAccount === "-"
        ? ""
        : formData.value.netizenAccount,
    createTime:
      formData.value.createTime === "-" ? "" : formData.value.createTime,
    publishTime:
      formData.value.publicTime === "-" ? "" : formData.value.publicTime,
    involvedArea:
      typeof formData.value.happenLocation === "string" ||
      !formData.value.happenLocation
        ? ""
        : formData.value.happenLocation?.join(""),
    categoryTag: formData.value.poType === "-" ? "" : formData.value.poType,
    mediaType: Number(formData.value.poMediaType),
    sensitiveTag:
      formData.value.isSensitive === "-" ? "" : formData.value.isSensitive,
  };
  const res = await editPoInfo(params);
  if (res.code === 200) {
    proxy.$message.success("提交成功");
    emit("refreshData");
    onClose();
  }
}

/**
 * 舆情反馈操作后刷新数据
 */
function refreshAndClose() {
  onClose();
  refresh();
}

/**
 * 重置
 */
function reset() {
  isEdit.value = false;
  poInfoRef.value.scrollbarRef.setScrollTop(0);
  poProcessRef.value.scrollbarRef.setScrollTop(0);
  formData.value = {};
  loading.value = false;
  processList.value = [];
}

/**
 * 刷新数据
 */
function refresh() {
  getPoProcess();
  poManageStore.getMyTaskNum(); // 刷新处理中数量
  setTimeout(() => {
    emit("refreshData");
  }, 500);
}
</script>

<style>
.editInfoDrawerModal {
  background: transparent;
}
</style>
<style lang="scss" scoped>
.closeIcon {
  width: 20px;
  height: 20px;
  position: fixed;
  top: 71px;
  right: 990px;
  cursor: pointer;
}

.drawerWrapper {
  width: 100%;
  height: 100%;
  display: flex;

  .addDialogBtn,
  .cancleDialogBtn {
    width: 80px;
    height: 32px;
    line-height: 32px;
    border-radius: 6px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
  }

  .addDialogBtn {
    background: #0070ff;
    color: #ffffff;
  }

  .cancleDialogBtn {
    color: #1f2329;
  }

  .handleBtn {
    // width: 96px;
    padding: 0 20px;
    height: 32px;
    line-height: 32px;

    border-radius: 6px;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
  }

  .handleBtn--primary {
    background: #0070ff;
    color: #ffffff;
  }

  .handleBtn--sub {
    border: 1px solid #0070ff;
    background: #fff;
    color: #0070ff;
  }

  .poProcess-container {
    width: 375px;
  }

  .feedback {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #8a96a9;
    line-height: 17px;
    text-align: left;
    font-style: normal;
  }
}
</style>

<style>
.editInfoDrawer-container.el-drawer .el-drawer__body {
  padding: 0;
}
</style>
