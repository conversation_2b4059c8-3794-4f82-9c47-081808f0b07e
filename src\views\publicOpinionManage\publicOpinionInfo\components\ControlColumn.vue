<template>
  <div class="controlColumn-container">
    <el-tooltip effect="dark" content="显隐列" placement="top">
      <el-dropdown trigger="click" :hide-on-click="false" :teleported="false" :max-height="426" placement="bottom-start">
        <div class="columnConfigWrapper">
          <img src="@/assets/images/columnConfig.svg" alt="" />
        </div>
        <template #dropdown>
          <div class="labelTip">请选择列表中要展示的信息</div>
          <el-dropdown-menu>
            <el-dropdown-item>
              <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-for="item in renderColumns" :key="item.prop">
              <el-checkbox v-model="item.visible" @change="checkboxChange(item.prop)" :label="item.label" />
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-tooltip>
  </div>
</template>

<script setup>
const props = defineProps({
  // 所有复选框
  columns: {
    type: Array,
    default: []
  },
  // 哪些复选框取消勾选
  hideColumns: {
    type: Array,
    default: []
  }
});
const emit = defineEmits(["toggle"]);

const renderColumns = ref([]); // 配置可渲染的列
renderColumns.value = props.columns.map(i => ({ label: i.label, prop: i.prop, visible: true }));

// 初次,回填勾选情况
const unwatch = watch(props.hideColumns, val => {
  if (val.length) {
    renderColumns.value.forEach(ele => {
      if (val.includes(ele.prop)) {
        ele.visible = false;
      }
    });
    checkAll.value = val.length === 0;
    isIndeterminate.value = val.length > 0 && val.length < renderColumns.value.length;
  }
  unwatch();
});

const checkAll = ref(true); // 全选
const isIndeterminate = ref(false); // 中间态

/**
 * 勾选全选
 */
function handleCheckAllChange(val) {
  renderColumns.value.forEach(ele => {
    ele.visible = val;
  });
  isIndeterminate.value = false;

  const hideProps = renderColumns.value.filter(i => !i.visible).map(i => i.prop); // 需要隐藏的列
  emit("toggle", hideProps);
}

/**
 * 勾选单项
 */
function checkboxChange() {
  const hideProps = renderColumns.value.filter(i => !i.visible).map(i => i.prop); // 需要隐藏的列
  checkAll.value = hideProps.length === 0;
  isIndeterminate.value = hideProps.length > 0 && hideProps.length < renderColumns.value.length;

  emit("toggle", hideProps);
}
</script>

<style lang="scss" scoped>
.columnConfigWrapper {
  width: 32px;
  height: 32px;
  line-height: 32px;
  padding: 0 8px 0 10px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #cfd2d6;
  cursor: pointer;
}

:deep(.el-dropdown__popper) {
  width: 201px;
  .el-dropdown__list {
    padding: 13px 16px;
    .labelTip {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 13px;
      color: #8f959e;
      line-height: 18px;
      margin-bottom: 11px;
    }

    .el-dropdown-menu {
      padding: 0;
      .el-dropdown-menu__item {
        padding: 0;
        .el-checkbox__label {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #18263c;
          line-height: 36px;
        }
      }
    }
  }
}
</style>
