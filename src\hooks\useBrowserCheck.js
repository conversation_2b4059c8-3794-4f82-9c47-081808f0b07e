import Bowser from 'bowser'; // 用于检查浏览器版本
import useVersionStore from '@/store/modules/version';

export function useBrowserCheck() {
  const router = useRouter();
  const versionStore = useVersionStore();

  const browser = Bowser.getParser(window.navigator.userAgent);
  const browserInfo = browser ? browser.getBrowser() : null;

  // 手机端访问提示去电脑端操作
  if (browser.parsedResult.platform.type !== 'desktop') {
    versionStore.setIsMobile(true);
    router.push('/mobile-not-supported');
    // ElMessageBox.alert('请到电脑端进行操作。', '提示', {
    //   confirmButtonText: '确认',
    //   callback: function (action) {},
    // });
  } else if (['Blink', 'WebKit', 'Gecko'].includes(browser.parsedResult.engine.name)) {
    // Trident(IE内核)、Gecko(Firefox内核)、WebKit内核、Blink(Chrome内核)
    // 版本 100 以下
    let versionList = [];
    // chrome
    if (['Blink', 'Gecko'].includes(browser.parsedResult.engine.name)) {
      versionList = browserInfo.version.split('.');
    }
    // safari
    if (['WebKit'].includes(browser.parsedResult.engine.name)) {
      versionList = browserInfo.version.split('.'); 
    }
    const versionNum = Number(versionList[0]);
    if (
      (versionNum &&
        ['Blink', 'Gecko'].includes(browser.parsedResult.engine.name) &&
        versionNum <= 100) ||
      (versionNum && ['WebKit'].includes(browser.parsedResult.engine.name) && versionNum <= 10)
    ) {
      versionStore.setLowBrowser(true);
      router.push('/low-browser');
      // ElMessageBox.alert('浏览器版本过低，请下载最新浏览器。', '提示', {
      //   confirmButtonText: '前往下载',
      //   callback: function (action) {},
      // });
    }
  } else {
    versionStore.setIsMobile(false); // 是否是移动端操作
    versionStore.setLowBrowser(false); // 是否是低版本浏览器
  }
}
