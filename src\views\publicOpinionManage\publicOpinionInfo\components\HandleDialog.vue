<template>
  <div class="dialog-container">
    <el-dialog :model-value="modelValue" width="870px" @close="onClose">
      <template #header>
        <span class="dialogTitle">舆情内容完善</span>
      </template>

      <el-scrollbar class="h-[100%] pr-[17px]">
        <EditInfo :edit-po-info="editPoData" />
        <DialogForm
          ref="dialogFormRef"
          :form-data="formData"
          :type-text="typeText"
        />
      </el-scrollbar>

      <template #footer>
        <el-button class="cacheBtn" @click="addCache">存草稿</el-button>
        <el-button class="cancelDialogBtn" @click="onCancle">取消</el-button>
        <el-button class="addDialogBtn" :loading="submitLoading" @click="onOk"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import EditInfo from "./EditInfo.vue";
import DialogForm from "./DialogForm.vue";
import {
  editPoInfo,
  createProcess,
  getPoCache,
  addPoCache,
  delPoCache,
} from "@/api/poManage/poInfo";
import { PO_TRANSFER_LIST, TRANSFER_TYPE } from "../../config/mapRel";
import { deepClone } from "@/utils";
import { setAddress } from "@/utils/area.js";
import { PO_TRANSFER_SORT } from "../config/constant";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  typeText: {
    type: String,
    required: true,
  },
  poInfo: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["update:modelValue", "refreshData"]);

const { proxy } = getCurrentInstance();

const cachePoInfo = ref({}); // 草稿数据
const editPoData = ref({}); // 编辑数据
const dialogFormRef = ref();
const formData = ref({
  deadlineTime1: "", // lc
  charges1: [],
  deadlineTime2: "", // cz
  charges2: [],
  deadlineTime3: "", // hb
  charges3: [],
  charges4: [], // zy
});
const submitLoading = ref(false);

/**
 * 每次打开弹窗，回填编辑数据。有草稿再回填草稿
 */
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      await getCache(props.poInfo.id);

      // const poEvent =
      // Array.isArray(props.poInfo.poEvent) && props.poInfo.poEvent[0]?.status
      //   ? props.poInfo.poEvent
      //       .filter((i) => i.status === "1")
      //       .map((i) => i.id) // 仅回显进行中的事件
      //   : Array.isArray(props.poInfo.poEvent) &&
      //       !props.poInfo.poEvent[0]?.status
      //     ? props.poInfo.poEvent
      //     : [];

      const oldData = {
        ...props.poInfo,
        createTime:
          props.poInfo.createTime === "-" ? "" : props.poInfo.createTime,
        platformType:
          props.poInfo.platformType === "-" ? "" : props.poInfo.platformType,
        netizenNickname:
          props.poInfo.netizenNickname === "-"
            ? ""
            : props.poInfo.netizenNickname,
        poType: props.poInfo.poTypeId,
        workUnit: props.poInfo.wechatDeptId,
        happenLocation:
          props.poInfo.happenLocation === "-"
            ? ""
            : setAddress(props.poInfo.happenLocation),
        poEvent: Array.isArray(props.poInfo.poEvent)
          ? props.poInfo.poEvent
              .filter((i) => i.status === "1")
              .map((i) => i.id)
          : [], // 仅回显进行中的事件
        poMediaType:
          props.poInfo.poMediaType === "-" ? "" : props.poInfo.poMediaType,
      };
      editPoData.value = {
        ...deepClone(oldData),
        ...cachePoInfo.value,
      };
    }
  }
);

/**
 * 获取草稿数据
 */
async function getCache(id) {
  const res = await getPoCache(id);
  // 没有缓存，则不返回 res.data
  if (res.code === 200 && res.data) {
    cachePoInfo.value = {
      poName: res.data.title,
      platformType: res.data.platformTag,
      poLink: res.data.linkUrl,
      netizenNickname: res.data.netizenNickname,
      poType: res.data.categoryTag,
      poContent: res.data.content,
      workUnit: res.data.wechatDeptId,
      happenLocation: setAddress(res.data.involvedArea || ""),
      poEvent: res.data.eventIds,
      isSensitive: res.data.sensitiveTag + "",
      poMediaType: res.data.mediaType + "",
    };
  }
}

/**
 * 存草稿
 */
async function addCache() {
  const params = {
    id: props.poInfo.id,
    title: editPoData.value.poName,
    platformTag: editPoData.value.platformType,
    linkUrl: editPoData.value.poLink,
    netizenNickname: editPoData.value.netizenNickname,
    categoryTag: editPoData.value.poType,
    content: editPoData.value.poContent,
    wechatDeptId: editPoData.value.workUnit?.toString(),
    involvedArea:
      typeof editPoData.value.happenLocation === "string"
        ? editPoData.value.happenLocation
        : editPoData.value.happenLocation?.join(""),
    eventIds: editPoData.value.poEvent,
    sensitiveTag: editPoData.value.isSensitive,
    mediaType: editPoData.value.poMediaType,
  };
  const res = await addPoCache(params);
  if (res.code === 200) {
    proxy.$message.success("草稿保存成功");
    onClose();
  }
}

/**
 * 关闭弹窗
 */
function onClose() {
  reset();
  emit("update:modelValue", false);
}

/**
 * 取消按钮（清除草稿）
 */
async function onCancle() {
  const res = await delPoCache(props.poInfo.id);
  if (res.code === 200) {
    proxy.$message.success("草稿清除成功");
    onClose();
  }
}

/**
 * 确定
 */
async function onOk() {
  dialogFormRef.value.addFormRef.validate(async (val) => {
    if (val) {
      submitLoading.value = true;
      const isSuccess = await savePoInfo();
      if (!isSuccess) return;

      let isOk = false;
      if (props.typeText === "研判") {
        // 流转4条记录
        const promiseArr = [];
        PO_TRANSFER_SORT.forEach((ele, index) => {
          // 有值就流转
          if (formData.value[`charges${index + 1}`].length) {
            const processObj = PO_TRANSFER_LIST.find(
              (i) => i.fontLabel === ele
            );
            promiseArr.push(
              createProcessInfo(
                processObj,
                formData.value[`charges${index + 1}`],
                formData.value[`deadlineTime${index + 1}`]
              )
            );
          }
        });

        if (promiseArr.length) {
          const resArr = await Promise.all(promiseArr);
          isOk = resArr.every((ele) => ele.code === 200);
        } else {
          isOk = true;
        }
      } else {
        // 流转1条记录
        const processObj = PO_TRANSFER_LIST.find(
          (i) => i.fontLabel === props.typeText
        );
        const res = await createProcessInfo(
          processObj,
          formData.value.charges1,
          formData.value.deadlineTime1
        );
        isOk = res.code === 200;
      }

      if (isOk) {
        proxy.$message.success("处理成功");
        submitLoading.value = false;
        emit("refreshData");
        onClose();
      }
    }
  });
}

/**
 * 保存舆情信息
 */
async function savePoInfo() {
  const params = {
    id: editPoData.value.id,
    title: editPoData.value.poName,
    platformTag: editPoData.value.platformType,
    linkUrl: editPoData.value.poLink,
    netizenNickname: editPoData.value.netizenNickname,
    categoryTag: editPoData.value.poType,
    content: editPoData.value.poContent,
    wechatDeptId: editPoData.value.workUnit?.toString(),
    involvedArea:
      typeof editPoData.value.happenLocation === "string"
        ? editPoData.value.happenLocation
        : editPoData.value.happenLocation?.join(""),
    eventIds: editPoData.value.poEvent,
    sensitiveTag: editPoData.value.isSensitive,
    mediaType: editPoData.value.poMediaType,
  };
  const res = await editPoInfo(params);
  return res.code === 200;
}

/**
 * 舆情流转
 */
async function createProcessInfo(processObj, charges, deadlineTime) {
  const params = {
    reportIds: [props.poInfo.id],
    commandCategory: processObj.categoriesCode,
    command: processObj.commandsCode,
    processStep: processObj.processStepCode,
    charges: charges?.map((id) => {
      return {
        userInCharge: id,
        deptInCharge: "100",
      };
    }),
  };

  // 添加截止时间参数
  if (props.typeText !== TRANSFER_TYPE.VIEW) {
    params.deadlineTime = deadlineTime;
  }

  const res = await createProcess(params);
  return res;
}

/**
 * 重置数据
 */
function reset() {
  cachePoInfo.value = {};
  dialogFormRef.value.reset();
}
</script>

<style lang="scss" scoped>
.dialog-container {
  :deep(.el-dialog) {
    padding: 16px 0 0;
    .el-dialog__header {
      padding-left: 24px;
      .dialogTitle {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #1f2329;
      }
    }

    .el-dialog__body {
      padding: 10px 5px 0 22px;
      height: 75vh;

      .editInfo-container {
        margin-bottom: 17px;
      }
    }

    .el-dialog__footer {
      height: 100px;
      line-height: 100px;
      padding: 0 22px 0 0;

      .cacheBtn,
      .cancelDialogBtn,
      .addDialogBtn {
        width: 80px;
        height: 32px;
        line-height: 32px;
        border-radius: 5px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
      }

      .cacheBtn {
        border: 1px solid #0070ff;
        color: #0070ff;
      }
      .cancelDialogBtn {
        border: 1px solid #cfd2d6;
        color: #1f2329;
      }
      .addDialogBtn {
        background: #0070ff;
        color: #ffffff;
      }
    }
  }
}
</style>
