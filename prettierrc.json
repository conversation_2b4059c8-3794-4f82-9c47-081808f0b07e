{
  "$schema": "https://json.schemastore.org/prettierrc",
  "semi": false,
  "tabWidth": 2,
  "singleQuote": true,
  "printWidth": 100,
  "trailingComma": "none",
  "useTabs": false,
  "quoteProps": "as-needed",
  "jsxSingleQuote": true,
  "bracketSpacing": true,
  "jsxBracketSameLine": true,
  "arrowParens": "always",
  "requirePragma": false,
  "insertPragma": false,

  "proseWrap": "always",
  "htmlWhitespaceSensitivity": "ignore",
  "vueIndentScriptAndStyle": true,
  "singleAttributePerLine": true,
  "bracketSameLine": false,
  "endOfLine": "lf",
  "embeddedLanguageFormatting": "auto",
}
