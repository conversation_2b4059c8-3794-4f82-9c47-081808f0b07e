/**
 * 表格列显隐字段的映射关系
 */
export const COLUMN_FIELD_CONFIG = [
  {
    responseField: "showReportNumber", // 响应字段
    columnProp: "poId" // 列prop
  },
  {
    responseField: "showFirstReportTag",
    columnProp: "isFirst"
  },
  {
    responseField: "showTitle",
    columnProp: "poName"
  },
  {
    responseField: "showContent",
    columnProp: "poContent"
  },
  {
    responseField: "showLinkUrl",
    columnProp: "poLink"
  },
  {
    responseField: "showEvent",
    columnProp: "poEvent"
  },
  {
    responseField: "showWorkUnit",
    columnProp: "workUnit"
  },
  {
    responseField: "showMediaType",
    columnProp: "poMediaType"
  },
  {
    responseField: "showReportSource",
    columnProp: "poFrom"
  },
  {
    responseField: "showPlatformTag",
    columnProp: "platformType"
  },
  {
    responseField: "showInvolvedArea",
    columnProp: "happenLocation"
  },
  {
    responseField: "showSensitiveTag",
    columnProp: "isSensitive"
  },
  {
    responseField: "showCategoryTag",
    columnProp: "poType"
  },
  {
    responseField: "showWechatNickname",
    columnProp: "wechatNickname"
  },
  {
    responseField: "showNetizenNickname",
    columnProp: "netizenNickname"
  },
  {
    responseField: "showNetizenAccount",
    columnProp: "netizenAccount"
  },
  {
    responseField: "showPublishTime",
    columnProp: "publicTime"
  },
  {
    responseField: "showPhotoUrl",
    columnProp: "poImg"
  },
  {
    responseField: "showSourceStatus",
    columnProp: "articleStatus"
  },
  {
    responseField: "showReportProcess",
    columnProp: "handleStatus"
  },
  {
    responseField: "showCreateTime",
    columnProp: "createTime"
  },
  {
    responseField: "showTargetGroup",
    columnProp: "targetGroup"
  },
  {
    responseField: "showScore",
    columnProp: "score"
  }
];
