<template>
  <div>
    <el-dialog :model-value="modelValue" width="600px" @close="onCancel">
      <template #header>
        <span class="dialogTitle">舆情{{ processObj.fontText }}</span>
      </template>

      <el-form
        ref="handleFormRef"
        label-position="top"
        :model="handleForm"
        :rules="rules"
        status-icon
        class="handleForm"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item
              :label="`${processObj.fontText}结果`"
              prop="handleResult"
            >
              <el-radio-group v-model="handleForm.handleResult">
                <el-radio
                  v-for="r in resultOptions"
                  :key="r.code"
                  :label="r.label"
                  :value="r.code"
                />
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24" v-if="isResolveDone">
            <el-form-item
              :label="`${processObj.fontText}措施`"
              prop="handleMeasures"
            >
              <el-radio-group v-model="handleForm.handleMeasures">
                <el-radio
                  v-for="m in handleMeasuresOptions"
                  :key="m.code"
                  :label="m.label"
                  :value="m.code"
                />
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="descriptionLabel" prop="description">
              <el-input
                v-model="handleForm.description"
                show-word-limit
                maxlength="500"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 6 }"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 落查完成时，需要填写网民身份信息 -->
        <template v-if="handleForm.handleResult === 'CHECK_DONE'">
          <div class="flex justify-between mb-[8px]">
            <span class="unified-font">网民身份信息</span>
            <el-button
              link
              style="color: #0070ff"
              @click="showIdentifyInput = true"
              >智能识别</el-button
            >
          </div>
          <!-- 智能识别输入框 -->
          <template v-if="showIdentifyInput">
            <div class="input-with-actions-relative">
              <el-input
                v-model="netizenIdentityInfo"
                show-word-limit
                maxlength="500"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="请输入网民身份信息"
                class="identity-input"
                :input-style="{ paddingBottom: '38px' }"
              />
              <div class="input-buttons-abs">
                <el-button size="small" @click="showIdentifyInput = false"
                  >关闭</el-button
                >
                <el-button
                  size="small"
                  type="primary"
                  @click="handleIdentify"
                  class="!ml-[6px]"
                  >识别</el-button
                >
              </div>
            </div>
          </template>
          <!-- 网民身份信息表单 -->
          <template v-if="handleForm.handleResult === 'CHECK_DONE'">
            <el-form
              ref="netizenFormRef"
              label-position="top"
              :model="netizenForm"
              :rules="netizenRules"
              class="netizen-form"
              v-loading="netizenFormLoading"
            >
              <el-row justify="space-between">
                <el-col :span="11">
                  <el-form-item label="网民昵称" prop="nickname">
                    <el-input
                      v-model="netizenForm.nickname"
                      show-word-limit
                      maxlength="50"
                      placeholder="请输入网民昵称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item label="所属平台" prop="platform">
                    <EditPlatformType
                      v-model="netizenForm.platform"
                      :placeholder="'请选择所属平台'"
                      class="w-full"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row justify="space-between">
                <el-col :span="11">
                  <el-form-item label="真实姓名" prop="realName">
                    <el-input
                      v-model="netizenForm.realName"
                      show-word-limit
                      maxlength="10"
                      placeholder="请输入真实姓名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item label="联系方式" prop="contact">
                    <el-input
                      v-model="netizenForm.contact"
                      placeholder="请输入联系方式"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="身份证号" prop="idCard">
                    <el-input
                      v-model="netizenForm.idCard"
                      show-word-limit
                      maxlength="18"
                      placeholder="请输入身份证号"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="住址信息" prop="address">
                    <el-input
                      v-model="netizenForm.address"
                      show-word-limit
                      maxlength="100"
                      type="textarea"
                      :autosize="{ minRows: 3, maxRows: 6 }"
                      placeholder="请输入住址信息"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </template>
        </template>
      </el-form>

      <template #footer>
        <el-button class="cancleBtn" @click="onCancel">取消</el-button>
        <el-button class="submitBtn" type="primary" @click="onSubmit"
          >提交</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { PO_PROCESS_LIST } from "@/views/publicOpinionManage/config/mapRel";
import { createProcess, identifyNetizenInfo } from "@/api/poManage/poInfo";
import { addNetPerson } from "@/api/networkuser/user";
import EditPlatformType from "../../EditPlatformType.vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  poInfo: {
    type: Object,
    required: true,
  },
  handleMeasuresOptions: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits([
  "update:modelValue",
  "refreshData",
  "changeDrawerType",
]);

const { proxy } = getCurrentInstance();
const netizenFormLoading = ref(false);
const showIdentifyInput = ref(false);
const netizenIdentityInfo = ref(
  "请填写落查结果\n例，张三，网名：南山老翁，所属平台为抖音，身份证号：413026*****2131，xxxx 市xxx 小区，联系方式：181****2341"
);
const handleFormRef = ref(null);
const handleForm = ref({
  handleResult: "",
  handleMeasures: "",
  description: "",
});

// 网民信息表单
const netizenFormRef = ref(null);
const netizenForm = ref({
  nickname: "",
  platform: "",
  realName: "",
  contact: "",
  idCard: "",
  address: "",
});

// 身份证号格式校验（严格校验出生日期和校验码）
const validateIdCard = (rule, value, callback) => {
  if (value && value.length > 0) {
    const idCardReg =
      /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardReg.test(value)) {
      callback(new Error("请输入正确的身份证号"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

// 联系方式格式校验（11位手机号，仅格式校验，非必填）
const validatePhone = (rule, value, callback) => {
  if (value && value.length > 0) {
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!phoneReg.test(value)) {
      callback(new Error("请输入正确的联系方式"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

const netizenRules = {
  nickname: [],
  platform: [],
  realName: [],
  contact: [{ validator: validatePhone, trigger: "blur" }],
  idCard: [{ validator: validateIdCard, trigger: "blur" }],
  address: [],
};

const processObj = computed(() =>
  PO_PROCESS_LIST.find((i) => i.categoriesCode === props.poInfo.commandCategory)
);
const rules = computed(() => ({
  handleResult: [
    {
      required: true,
      message: `请选择${processObj.value.fontText}结果`,
      trigger: ["change", "blur"],
    },
  ],
  handleMeasures: [
    {
      required: true,
      validator: measuresValidator,
      trigger: ["change", "blur"],
    },
  ],
}));
const resultOptions = computed(() => {
  const { fontText, CONTINUE_CODE, FINISHED_CODE } = processObj.value;
  return [
    { label: "继续跟进", code: CONTINUE_CODE },
    { label: `完成${fontText}`, code: FINISHED_CODE },
  ];
});
const isResolveDone = computed(
  () =>
    props.poInfo.commandCategory === "RESOLVE" &&
    handleForm.value.handleResult === processObj.value.FINISHED_CODE
); // 选择了处置的完成处置
const descriptionLabel = computed(() => {
  const { fontText, FINISHED_CODE } = processObj.value;
  return handleForm.value.handleResult === FINISHED_CODE
    ? `${fontText}说明`
    : "进度说明";
});

// 处理弹窗打开时，处理结果需要默认选中
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      handleForm.value.handleResult = processObj.value.CONTINUE_CODE;
      netizenForm.value.nickname =
        props.poInfo.netizenNickname === "-"
          ? ""
          : props.poInfo.netizenNickname;
      netizenForm.value.platform =
        props.poInfo.platformType === "-" ? "" : props.poInfo.platformType;
    }
  }
);
// 如果是完成处置，那么处置措施需要默认选中
watch(
  () => handleForm.value.handleResult,
  (val) => {
    if (isResolveDone.value) {
      handleForm.value.handleMeasures =
        props.handleMeasuresOptions?.[0]?.code || "";
    }
  }
);

/**
 * 校验处置措施
 */
function measuresValidator(rule, value, callback) {
  if (isResolveDone.value && value === "") {
    callback(new Error("请选择处置措施"));
  } else {
    callback();
  }
}

/**
 * 取消
 */
const onCancel = () => {
  handleFormRef.value?.resetFields(); // 重置表单
  netizenFormRef.value?.resetFields(); // 重置网民表单
  showIdentifyInput.value = false;
  netizenIdentityInfo.value =
    "请填写落查结果\n例，张三，网名：南山老翁，所属平台为抖音，身份证号：413026*****2131，xxxx 市xxx 小区，联系方式：181****2341";
  emit("update:modelValue", false);
};

/**
 * 识别网民身份信息
 */
const handleIdentify = async () => {
  netizenFormLoading.value = true;
  // TODO: 实现识别逻辑
  // console.log("识别网民身份信息:", netizenIdentityInfo.value);
  const res = await identifyNetizenInfo({
    inputText: netizenIdentityInfo.value,
  });
  netizenFormLoading.value = false;
  if (res.code === 200) {
    netizenForm.value = {
      nickname: res.data.nickname,
      platform: res.data.platform,
      realName: res.data.name,
      contact: res.data.phone,
      idCard: res.data.idCard,
      address: res.data.address,
    };
  }
};

/*
 * 提交
 */
const onSubmit = async () => {
  if (
    handleForm.value.handleResult === "CHECK_DONE" &&
    netizenForm.value.realName
  ) {
    // 网民信息表单校验通过后，提交网民信息，否则不提交
    const valid = await new Promise((resolve) => {
      netizenFormRef.value.validate(resolve);
    });
    if (!valid) return;
  }

  if (handleForm.value.handleResult === "CHECK_DONE") {
    proxy
      .$confirm("确认提交落查结果？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
      .then(() => {
        doSubmit();
      });
  } else {
    doSubmit();
  }
};

// 提交实际逻辑
const doSubmit = () => {
  handleFormRef.value.validate(async (val) => {
    if (val) {
      const params = {
        reportIds: [props.poInfo.id],
        workId: props.poInfo.taskId,
        command: handleForm.value.handleResult,
        commandCategory: props.poInfo.commandCategory,
        commandOptDesc: handleForm.value.description,
      };

      if (isResolveDone.value) {
        params.commandOpt = handleForm.value.handleMeasures;
      }

      // 添加网民信息
      if (
        handleForm.value.handleResult === "CHECK_DONE" &&
        netizenForm.value.realName
      ) {
        // // 网民信息表单校验通过后，提交网民信息，否则不提交
        // const valid = await new Promise((resolve) => {
        //   netizenFormRef.value.validate(resolve);
        // });
        // if (!valid) return;
        const params = {
          nickname: netizenForm.value.nickname,
          platform: netizenForm.value.platform,
          name: netizenForm.value.realName,
          phone: netizenForm.value.contact,
          idCard: netizenForm.value.idCard,
          address: netizenForm.value.address,
          internetUserAccountList: [
            {
              account: "",
              nickName: netizenForm.value.nickname,
              status: "0",
              type: netizenForm.value.platform,
            },
          ],
        };
        await addNetPerson(params);
      }

      const res = await createProcess(params);
      if (res.code === 200) {
        proxy.$message.success("处理成功");
        // 只有点处置完成时，抽屉操作按钮和反馈按钮都消失
        if (handleForm.value.handleResult === processObj.value.FINISHED_CODE) {
          emit("changeDrawerType", "view");
          emit("changeHandleStatusIsIng", false);
        }
        if (handleForm.value.handleResult === processObj.value.CONTINUE_CODE) {
          emit("changeHandleStatusIsIng", true);
        }
        emit("refreshData");
        onCancel();
      }
    }
  });
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  padding: 24px;
  .el-dialog__header {
    padding-left: 0;
  }
}

.dialogTitle {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: #1f2329;
}

:deep(.handleForm) {
  .el-form-item--default {
    margin-bottom: 12px;
  }

  .el-form-item__label,
  .el-radio__label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
  }
}

.cancleBtn,
.submitBtn {
  width: 80px;
  height: 32px;
  line-height: 32px;
  border-radius: 6px;

  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
}
.cancleBtn {
  border: 1px solid #cfd2d6;
  color: #1f2329;
}
.submitBtn {
  background: #0070ff;
  color: #ffffff;
}

.unified-font {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1f2329;
}

.input-buttons {
  position: absolute;
  bottom: 8px;
  left: 8px;
  display: flex;
  gap: 8x;
}

.netizen-form {
  margin-top: 12px;
  // padding: 16px;
  // border: 1px solid #e4e7ed;
  // border-radius: 6px;
  // background-color: #fafafa;
}

.input-with-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}
.identity-input {
  width: 100%;
}
.input-buttons {
  margin-top: 4px;
  display: flex;
  gap: 6px;
}
.input-with-actions-relative {
  position: relative;
  width: 100%;
}
.identity-input {
  width: 100%;
}
.input-buttons-abs {
  position: absolute;
  left: 8px;
  bottom: 8px;
  display: flex;
  gap: 6px;
  z-index: 2;
}
</style>
