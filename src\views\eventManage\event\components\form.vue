<template>
  <div>
    <!-- 内容 -->
    <div class="flex justify-between">
      <el-select
        class="w-[150px]"
        v-model="selectValue"
        clearable
        @change="selectChange"
      >
        <el-option
          v-for="(item, index) in EVENT_STATUS_OPTIONS"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-button type="primary" @click="handleAdd"> 新增 </el-button>
    </div>
  </div>
</template>

<script setup>
import { EVENT_STATUS_OPTIONS } from "../config/index.js";

const emit = defineEmits(["refreshList", "handleAdd"]);

const selectValue = ref("0");

const selectChange = () => {
  console.log("selectChange");

  // 更新列表数据
  emit("refreshList");
};

const handleAdd = () => {
  emit("handleAdd");
};
</script>

<style lang="scss" scoped></style>
