<template>
  <div class="flex items-center">
    <section class="mr-[6px]">
      <el-input
        v-model="searchCondition.accountName"
        placeholder="按照帐号名称搜索"
        :prefix-icon="Search"
        style="width: 200px"
        clearable
        @change="$emit('search')"
      />
    </section>
    <section class="mr-[6px]">
      <el-select
        v-model="searchCondition.platformList"
        placeholder="请选择所属平台"
        clearable
        filterable
        multiple
        style="width: 224px"
        @change="$emit('search')"
      >
        <template #prefix>所属平台</template>
        <el-option label="全部" value="all" />
        <!-- <el-option
          v-for="item in targetGroupList"
          :key="item"
          :label="item"
          :value="item"
        /> -->
      </el-select>
    </section>
    <section class="mr-[6px]">
      <el-select
        v-model="searchCondition.tagType"
        placeholder="请选择标签类型"
        clearable
        filterable
        style="width: 224px"
        @change="$emit('search')"
      >
        <template #prefix>标签类型</template>
        <el-option label="全部" value="all" />
        <el-option
          v-for="item in tagTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </section>
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";

defineProps({
  // 搜索条件
  searchCondition: {
    type: Object,
    required: true,
  },
  tagTypeList: {
    type: Array,
    required: true,
  },
});

defineEmits(["search"]);

// const targetGroupList = ref([]);

/**
 * 获取目标群下拉项
 */
// async function getTargetGroupList() {
//   const res = await getTargetGroup();
//   if (res.code === 200) {
//     targetGroupList.value = res.data;
//   }
// }

// getTargetGroupList();
</script>

<style lang="scss" scoped></style>
