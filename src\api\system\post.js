import request from '@/utils/request'
import portalRequest from "@/utils/portalRequest.js";

// 查询岗位列表
export function listPost(query) {
  return portalRequest({
    url: '/system/post/list',
    method: 'get',
    params: query
  })
}

// 查询岗位详细
export function getPost(postId) {
  return portalRequest({
    url: '/system/post/' + postId,
    method: 'get'
  })
}

// 新增岗位
export function addPost(data) {
  return portalRequest({
    url: '/system/post',
    method: 'post',
    data: data
  })
}

// 修改岗位
export function updatePost(data) {
  return portalRequest({
    url: '/system/post',
    method: 'put',
    data: data
  })
}

// 删除岗位
export function delPost(postId) {
  return portalRequest({
    url: '/system/post/' + postId,
    method: 'delete'
  })
}
