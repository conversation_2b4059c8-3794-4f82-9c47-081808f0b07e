<template>
  <div class="w-full h-full screen-container relative">
    <!-- 遮罩层：展开应用时显示，点击遮罩层可收起应用 -->
    <div
      class="mask"
      :class="{ 'mask-show': isExpanded }"
      @click="handleFold"
    ></div>

    <div class="top-container absolute"><p>舆情态势监测大屏</p></div>
    <div class="left-border border-container absolute">
      <div
        class="border-active"
        :class="{ 'border-opacity': borderOpacity }"
      ></div>
    </div>
    <div class="right-border border-container absolute">
      <div
        class="border-active"
        :class="{ 'border-opacity': borderOpacity }"
      ></div>
    </div>

    <!-- 展开应用按钮：点击后展开应用列表并隐藏自身 -->
    <div
      class="expand-app-btn"
      :class="{ 'expand-app-btn-hide': isExpanded }"
      @click="handleExpand"
    >
      <img class="expand-app-icon" src="@/assets/screen/expand-app-icon.gif" />
      <div class="expand-app-text">展开</div>
    </div>

    <!-- 展开后的应用列表：展开时显示在最上层，包含收起按钮和应用列表内容 -->
    <div
      class="app-list-container"
      :class="{ 'app-list-container-show': isExpanded }"
    >
      <!-- 收起按钮：点击后收起应用列表 -->
      <div class="app-fold-btn" @click="handleFold">
        <img class="app-fold-icon" src="@/assets/screen/expand-app-icon.gif" />
        <div class="app-fold-text">收起</div>
      </div>
      <div class="app-list-wrapper">
        <div class="app-list-title">应用列表</div>
        <div class="appListWrapper">
          <div class="appItem" v-for="item in appMenus" :key="item.path">
            <div class="appWrapper" @click="router.push(item.path)">
              <div class="appIcon">
                <img class="navItemIcon" :src="item.icon" alt="icon" />
              </div>
              <div class="navItemTitleWrapper">
                <div class="navItemTitle">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI助手：初始只显示头像，悬浮后显示气泡 -->
    <img
      v-show="!isAiHover"
      src="@/assets/screen/ai-assistant-icon.gif"
      alt="ai-assistant-icon"
      class="ai-assistant-icon-alone"
      @mouseenter="isAiHover = true"
    />

    <transition name="fade-ai-assistant">
      <div
        v-show="isAiHover"
        class="ai-assistant-container ai-assistant-hover"
        @mouseleave="isAiHover = false"
      >
        <img
          src="@/assets/screen/ai-assistant-icon.gif"
          alt="ai-assistant-icon"
          class="ai-assistant-icon"
        />
        <div class="ai-assistant-text">我是AI智能助手~</div>
      </div>
    </transition>

    <div class="screen-main-container absolute">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { getMenuList } from "@/api/home";
import avatarIcon from "@/assets/images/avatar.svg";

const router = useRouter();

const borderTimer = ref(null);
const borderOpacity = ref(true);

// 是否展开应用列表的状态
const isExpanded = ref(false);

const appMenus = ref([]); // 应用列表

// AI助手悬浮状态
const isAiHover = ref(false);

/**
 * 获取配置的应用
 */
async function getApp() {
  const res = await getMenuList();
  if (res.code === 200) {
    appMenus.value = res.menus.map((ele) => ({
      icon: !!ele.uploadIconUrl
        ? import.meta.env.VITE_APP_BASE_API + ele.uploadIconUrl
        : avatarIcon,
      title: ele.menuName,
      path: `/${ele.redirectUrl}` || "",
    }));
    // 过滤掉Path为/opinionScreen的菜单
    appMenus.value = appMenus.value.filter(
      (item) => item.path !== "/opinionScreen"
    );
  }
}
/**
 * 展开应用
 * 1. 设置 isExpanded 为 true
 * 2. 展开按钮右移消失，应用列表淡入显示，遮罩层出现
 */
const handleExpand = () => {
  isExpanded.value = true;
  getApp();
};

/**
 * 收起应用
 * 1. 设置 isExpanded 为 false
 * 2. 应用列表淡出，展开按钮重新出现，遮罩层消失
 */
const handleFold = () => {
  isExpanded.value = false;
};

onMounted(() => {
  borderTimer.value = setInterval(() => {
    borderOpacity.value = !borderOpacity.value;
  }, 1000);
});

onBeforeUnmount(() => {
  clearInterval(borderTimer.value);
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.screen-container {
  overflow: hidden;
  background-color: #010f24;
  background-image: url(@/assets/screen/back.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;

  // 遮罩层
  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    z-index: 90;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.mask-show {
      opacity: 1;
      visibility: visible;
    }
  }

  .top-container {
    width: 100%;
    height: px2vw(148);
    background: url("@/assets/screen/back-top.png") no-repeat;
    background-size: 100% 100%;
    top: 0;
    left: 0;
    p {
      margin-top: px2vw(19);
      text-align: center;
      font-family: PangMenZhengDao;
      font-size: px2vw(37);
      color: #feffff;
      line-height: px2vw(37);
      letter-spacing: px2vw(5);
      background: linear-gradient(
        180deg,
        #ffffff 0%,
        #ffffff 50%,
        #86cbff 100%
      );
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .border-container {
    width: 33%;
    height: calc(100% - px2vw(24));
    margin: px2vw(12);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    top: 0;
    &.left-border {
      background-image: url("@/assets/screen/border-left.png");
      left: 0;
      .border-active {
        left: px2vw(4);
      }
    }
    &.right-border {
      background-image: url("@/assets/screen/border-right.png");
      right: 0;
      .border-active {
        right: px2vw(4);
      }
    }
    .border-active {
      width: px2vw(5);
      height: px2vw(17);
      background-image: url("@/assets/screen/border-active.png");
      position: absolute;
      top: 0;
      bottom: 0;
      margin-top: auto;
      margin-bottom: auto;
      opacity: 1;
      transition: opacity 1s ease;
      &.border-opacity {
        opacity: 0;
      }
    }
  }

  .expand-app-btn {
    position: absolute;
    top: 43%;
    right: px2vw(-1);
    z-index: 100;
    cursor: pointer;
    width: px2vw(82);
    height: px2vw(49);
    background: rgba(9, 55, 72, 0.52);
    border-radius: px2vw(25) 0px 0px px2vw(25);
    border: px2vw(1) solid #58ecff;
    backdrop-filter: blur(3px);
    display: flex;
    align-items: center;
    transform: translateX(0);
    transition: all 0.3s ease;

    &-hide {
      transform: translateX(100%);
      opacity: 0;
    }

    .expand-app-icon {
      transform: rotate(180deg);
      width: px2vw(40);
      height: px2vw(45);
    }

    .expand-app-text {
      font-family: PangMenZhengDao;
      font-size: px2vw(18);
      color: #feffff;
      line-height: px2vw(17);
      text-align: left;
      font-style: normal;
    }
  }

  .app-list-container {
    position: fixed;
    top: px2vw(66);
    right: px2vw(49);
    width: px2vw(510);
    height: px2vw(511+49);
    opacity: 0;
    visibility: hidden;
    transform: translateX(20px);
    transition: all 0.3s ease;
    z-index: 200;

    &-show {
      opacity: 1;
      visibility: visible;
      transform: translateX(0);
    }

    .app-fold-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: px2vw(96);
      height: px2vw(49);
      background: rgba(9, 55, 72, 0.52);
      border-radius: px2vw(25);
      border: 1px solid #58ecff;
      backdrop-filter: blur(3px);
      cursor: pointer;
      z-index: 110;
      display: flex;
      align-items: center;

      .app-fold-icon {
        width: px2vw(40);
        height: px2vw(45);
        margin-left: px2vw(2);
      }

      .app-fold-text {
        font-family: PangMenZhengDao;
        font-size: px2vw(18);
        color: #feffff;
        line-height: px2vw(17);
        text-align: left;
        font-style: normal;
      }
    }

    .app-list-wrapper {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: px2vw(511);
      background: url("@/assets/screen/app-list-bg.png") no-repeat;
      background-size: 100% 100%;
      z-index: 110;

      // padding: px2vw(100) px2vw(50) px2vw(30);

      .app-list-title {
        position: absolute;
        top: px2vw(23);
        left: px2vw(87);
        font-family: PangMenZhengDao;
        font-size: px2vw(24);
        color: #feffff;
        line-height: px2vw(27);
        text-align: right;
        font-style: normal;
      }

      .appListWrapper {
        margin: px2vw(90) px2vw(50) px2vw(30);

        padding: px2vw(20) 0;

        width: calc(100% - px2vw(100));
        height: calc(100% - px2vw(140));

        // display: flex;
        // flex-wrap: wrap;

        // gap: px2vw(20);
        display: grid;
        grid-template-columns: repeat(3, 1fr);

        gap: px2vw(30);

        overflow-x: hidden;
        overflow-y: auto;

        // background-color: #082b30;

        // 浏览器滚动条
        &::-webkit-scrollbar {
          width: 0;
        }

        .appItem {
          // height: px2vw(80);
          text-align: center;
          cursor: pointer;
          height: px2vw(100);

          // background-color: #58ecff;

          .appWrapper {
            width: 100%;
            // height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center; // 垂直居中

            .appIcon {
              margin-top: px2vw(15);
              width: px2vw(57);
              height: px2vw(45);
              background: url("@/assets/images/home/<USER>") no-repeat
                center/cover;

              &:hover {
                background: url("@/assets/images/home/<USER>") no-repeat;
                background-size: cover, 100%, 100%;
                background-position: center center;
              }

              position: relative;

              .navItemIcon {
                // margin-top: px2vw(-5);
                position: absolute;
                top: px2vw(-28);
                right: px2vw(2);
                width: px2vw(53);
                height: px2vw(54);
              }
            }

            .navItemTitleWrapper {
              margin-top: px2vw(8);
              min-width: px2vw(117);
              height: px2vw(27);

              display: flex;
              justify-content: center;
              align-items: center;

              .navItemTitle {
                font-family:
                  PingFangSC,
                  PingFang SC;
                font-weight: 500;
                font-size: px2vw(16);
                color: #ffffff;
                line-height: px2vw(22);
                text-align: center;
                font-style: normal;
              }
            }
          }
        }
      }
    }
  }

  .ai-assistant-container {
    cursor: pointer;
    z-index: 100;
    position: absolute;
    bottom: px2vw(35);
    right: px2vw(0);
    width: px2vw(240);
    height: px2vw(41);
    background: linear-gradient(
      270deg,
      rgba(10, 100, 198, 0.8) 0%,
      rgba(76, 215, 222, 0.8) 100%
    );
    border-radius: px2vw(22);
    border: px2vw(1) solid #ffffff;
    backdrop-filter: blur(px2vw(10));
    box-sizing: border-box;
    opacity: 1;
    transition:
      right 0.4s,
      opacity 0.4s;

    .ai-assistant-text {
      position: absolute;
      left: px2vw(17);
      top: px2vw(12);
      font-family: PangMenZhengDao;
      font-size: px2vw(18);
      color: #feffff;
      line-height: px2vw(21);
      text-align: right;
      font-style: normal;
      opacity: 1;
      transition: opacity 0.2s;
    }

    .ai-assistant-icon {
      position: absolute;
      right: px2vw(-8);
      bottom: px2vw(-18);
      width: px2vw(124);
      height: px2vw(124);
      z-index: 2;
      transition: right 0.4s;
    }
  }

  .ai-assistant-icon-alone {
    position: absolute;
    z-index: 100;
    right: px2vw(-55);
    bottom: px2vw(35);
    width: px2vw(124);
    height: px2vw(124);
    cursor: pointer;
    transition: filter 0.2s;
    &:hover {
      filter: brightness(1.1);
    }
  }

  // 淡入淡出动画 for ai-assistant-container
  .fade-ai-assistant-enter-active,
  .fade-ai-assistant-leave-active {
    transition: opacity 0.8s;
  }
  .fade-ai-assistant-enter-from,
  .fade-ai-assistant-leave-to {
    opacity: 0;
  }
  .fade-ai-assistant-enter-to,
  .fade-ai-assistant-leave-from {
    opacity: 1;
  }

  .screen-main-container {
    width: 100%;
    height: 100%;
    padding-bottom: px2vw(40);
  }
}
</style>
