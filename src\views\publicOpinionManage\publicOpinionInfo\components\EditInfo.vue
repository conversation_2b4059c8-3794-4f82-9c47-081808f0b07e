<template>
  <div class="editInfo-container flex flex-wrap">
    <section class="infoItem w-full!">
      <div class="poInfoLabel">舆情标题</div>
      <el-input
        v-model="editPoInfo.poName"
        placeholder="请输入舆情标题"
        :maxlength="200"
        show-word-limit
        clearable
      />
    </section>

    <section class="infoItem pr-[12px]">
      <div class="poInfoLabel">舆情编号</div>
      <el-input
        v-model="editPoInfo.poId"
        disabled
        placeholder="请输入舆情编号"
      />
    </section>

    <section class="infoItem">
      <div class="poInfoLabel">报送时间</div>
      <el-date-picker
        v-model="editPoInfo.createTime"
        type="datetime"
        value-format="YYYY-MM-DD HH:mm:ss"
        placeholder="请选择事件开始时间"
        style="width: 100%"
        disabled
      />
    </section>

    <section class="infoItem pr-[12px]">
      <div class="poInfoLabel">发布平台</div>
      <EditPlatformType v-model="editPoInfo.platformType" />
    </section>

    <section class="infoItem">
      <div class="poInfoLabel">贴文链接</div>
      <el-input
        v-model="editPoInfo.poLink"
        placeholder="请输入贴文链接"
        clearable
        class="linkInput"
      >
        <template #prefix>
          <div class="linkPrefix">
            <img
              class="w-[15px] h-[15px]"
              src="@/assets/images/poManage/link2.png"
              alt=""
            />
          </div>
        </template>
      </el-input>
    </section>

    <section class="infoItem pr-[12px]">
      <div class="poInfoLabel">网民昵称</div>
      <el-input
        v-model="editPoInfo.netizenNickname"
        placeholder="请输入网民昵称"
        clearable
      />
    </section>

    <section class="infoItem">
      <div class="poInfoLabel">舆情类型</div>
      <EditPoType v-model="editPoInfo.poType" />
    </section>

    <section class="infoItem w-full!">
      <div class="poInfoLabel">舆情内容</div>
      <el-input
        v-model="editPoInfo.poContent"
        type="textarea"
        placeholder="请输入舆情内容"
        :rows="6"
        :maxlength="5000"
        show-word-limit
        clearable
      />
    </section>

    <section class="infoItem pr-[12px]">
      <div class="poInfoLabel">上报单位</div>
      <EditWorkUnit v-model="editPoInfo.workUnit" />
    </section>

    <section class="infoItem">
      <div class="poInfoLabel">舆情属地</div>
      <el-cascader
        v-model="editPoInfo.happenLocation"
        :options="pcaTextArr"
        placeholder="请选择舆情属地"
        clearable
        style="width: 100%"
      />
    </section>

    <section class="infoItem pr-[12px]">
      <div class="poInfoLabel">舆情事件</div>
      <el-select
        v-model="editPoInfo.poEvent"
        multiple
        placeholder="请选择舆情事件"
        clearable
      >
        <el-option
          v-for="item in poManageStore.ingPoEventList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </section>

    <section class="infoItem">
      <div class="poInfoLabel">是否为敏感舆情</div>
      <el-select
        v-model="editPoInfo.isSensitive"
        placeholder="请选择是否为敏感舆情"
      >
        <el-option
          v-for="item in SENSITIVE_STATUS_LIST"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </section>

    <section class="infoItem pr-[12px]">
      <div class="poInfoLabel">贴文状态</div>
      <el-select
        v-model="editPoInfo.articleStatus"
        placeholder="请选择贴文状态"
        disabled
      >
        <el-option
          v-for="item in ARCTICLE_STATUS_LIST"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </section>

    <section class="infoItem">
      <div class="poInfoLabel">处置状态</div>
      <el-select
        v-model="editPoInfo.handleStatus"
        placeholder="请选择处置状态"
        disabled
      >
        <el-option
          v-for="item in HANDLE_STATUS_LIST"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </section>

    <section class="infoItem w-full!">
      <div class="poInfoLabel">图片</div>
      <div
        class="flex flex-wrap gap-[6px]"
        v-if="Array.isArray(editPoInfo.poImg)"
      >
        <ImagePreview
          v-for="(item, index) in editPoInfo.poImg"
          :key="index"
          :src="item"
          :width="94"
          :height="56"
        />
      </div>
      <span v-else class="poInfoValue">-</span>
    </section>

    <section class="infoItem w-full!">
      <div class="poInfoLabel">媒体类型</div>
      <el-select v-model="editPoInfo.poMediaType" placeholder="请选择媒体类型">
        <el-option
          v-for="item in mediaTypeOption"
          :key="item.value"
          :label="item.label"
          :value="Number(item.value)"
        />
      </el-select>
    </section>
  </div>
</template>

<script setup>
import EditPlatformType from "../../components/EditPlatformType.vue";
import EditPoType from "../../components/EditPoType.vue";
import EditWorkUnit from "../../components/EditWorkUnit.vue";
import { usePoManageStore } from "@/store/modules/poManage.js";
import { pcaTextArr } from "element-china-area-data";
import {
  SENSITIVE_STATUS_LIST,
  ARCTICLE_STATUS_LIST,
  HANDLE_STATUS_LIST,
} from "../../config/constant.js";
import useDictStore from "@/store/modules/dict";

const poManageStore = usePoManageStore();
poManageStore.getAllPoEventList();

const dictStore = useDictStore();
const mediaTypeOption = computed(
  () => dictStore.dict.find((ele) => ele.key === "media_type").value
);

defineProps({
  editPoInfo: {
    type: Object,
    required: true,
  },
});
</script>

<style lang="scss" scoped>
.editInfo-container {
  width: 100%;
  height: 100%;

  .infoItem {
    width: 50%;
    margin-bottom: 11px;
  }

  .poInfoLabel {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
    line-height: 32px;
    margin-bottom: 3px;
  }
  .poInfoValue {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
  }

  :deep(.linkInput) {
    .el-input__wrapper {
      padding-left: 1px;
      .linkPrefix {
        width: 38px;
        height: 32px;
        background: #eff0f1;
        border-radius: 3px 0px 0px 3px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
