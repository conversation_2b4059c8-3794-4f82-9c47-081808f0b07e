<template>
  <div class="public-opinion-record-display w-full h-full flex flex-col">
    <item-title class="w-full" :title="title" />
    <!--    <div class="w-full h-[270px]" ref="showChartRef"></div>-->
    <div class="w-[38vw] charts">
      <e-charts :id="'3'" :options="options" class="!w-full h-full" />
    </div>
  </div>
</template>

<script setup>
import ItemTitle from "@/views/eventManage/screen/component/ItemTitle.vue";
import ECharts from "@/components/ECharts/index.vue";
import * as echarts from "echarts";

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
  publicOpinionRecordDisplayData: {
    type: Array,
    default: () => [],
  },
});

const options = ref({});

watch(
  () => props.publicOpinionRecordDisplayData,
  (newV) => {
    init(newV);
  },
  {
    deep: true,
    immediate: true,
  },
);

function init(res) {
  const data = (res || [])
    .map((item) => {
      return item.opinionNum || 0;
    })
    .filter((item) => {
      return item !== 0;
    });
  let maxValue = Math.max(...data);

  let yAxisMax = Math.ceil(maxValue * 1.01);

  options.value = {
    grid: {
      right: 20,
      top: 30,
      bottom: 30,
    },
    legend: {
      show: true,
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 40,
      textStyle: {
        color: "#CCE3F9",
        fontSize: 12,
        lineHeight: 15,
      },
      top: 0,
      right: 0,
      icon: "rect",
      data: [
        {
          name: "舆情数量",
          itemStyle: {
            color: "#20E1E7",
          },
        },
        {
          name: "已处置舆情数量",
          itemStyle: {
            color: "#0095FF",
          },
        },
      ],
    },
    xAxis: {
      type: "category",
      axisTick: {
        show: false,
      },
      axisLabel: {
        margin: 12,
        color: "#C1EAFE",
      },
      data: (res || []).map((item) => {
        return item.deptName || 0;
      }),
      splitLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "rgba(47,56,67,0.4)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1, // 设置最小刻度间隔为 1
      max: yAxisMax, // 设置 y 轴最大值为动态计算的值
      splitLine: {
        lineStyle: {
          type: "dashed",
          color: "rgba(47,56,67,0.4)",
        },
      },
      axisLabel: {
        color: "#61798B",
        fontFamily: "DIN",
        fontSize: 14,
      },
    },
    series: [
      {
        data: (res || []).map((item) => {
          return item?.opinionNum || 0;
        }),
        type: "bar",
        stack: "a",
        showBackground: false,
        itemStyle: {
          // color: 'transparent',
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(32,193,254,0.00)" },
            { offset: 1, color: "rgba(32, 193, 254, 0.55" },
          ]),
        },
        barWidth: 20,
        label: { show: true, position: "top", color: "#20E1E7" },
      },
      {
        name: "舆情数量",
        data: (res || []).map((item) => {
          return 0;
        }),
        type: "bar",
        stack: "a",
        itemStyle: {
          borderColor: "#20E1E7",
          borderWidth: 3,
        },
        barWidth: 20,
      },
      {
        data: (res || []).map((item) => {
          return item?.dealNum || 0;
        }),
        type: "bar",
        stack: "b",
        itemStyle: {
          color: "transparent",
        },
        barGap: "-100%",
        barWidth: 20,
      },
      {
        name: "已处置舆情数量",
        data: (res || []).map((item) => {
          return 0;
        }),
        type: "bar",
        stack: "b",
        itemStyle: {
          borderColor: "#0095FF",
          borderWidth: 3,
        },
        barWidth: 20,
      },
    ],
  };
}
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.public-opinion-record-display {
  gap: px2vw(10);
  .charts {
    height: 100%;
  }
}
</style>
