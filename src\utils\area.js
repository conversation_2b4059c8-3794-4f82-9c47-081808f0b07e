import { pcaTextArr } from "element-china-area-data";

/**
 * 将舆情属地转化为下拉框回显形式
 */
export const setAddress = address => {
  console.log("address", address);

  // 解析地址字符串
  let addressParts;
  if (address.includes(",")) {
    addressParts = address.split(",");
  } else {
    addressParts = address.match(/(.+?[省市])(.+?[市区])(.+)/)?.slice(1) || [];
  }

  const [provinceName, cityName, districtName] = addressParts;

  // 查找省份
  const province = pcaTextArr.find(item => item.label === provinceName);
  if (province) {
    // 查找城市
    const city = province.children.find(item => item.label === cityName);
    if (city) {
      // 查找区/县
      const district = city.children.find(item => item.label === districtName);
      if (district) {
        // 设置选中值
        return [province.value, city.value, district.value];
      }
    }
  }

  return [];
};
