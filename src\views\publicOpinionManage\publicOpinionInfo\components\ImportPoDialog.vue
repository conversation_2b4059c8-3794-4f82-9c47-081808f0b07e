<template>
  <div class="dialog-container">
    <el-dialog :model-value="modelValue" width="600px" @close="onCancle">
      <template #header>
        <span class="dialogTitle">导出舆情</span>
      </template>

      <div class="poInfoLabel">选择发布时间</div>
      <el-date-picker
        v-model="publishTime"
        type="datetimerange"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        style="width: 100%"
      />

      <template #footer>
        <el-button class="cancelDialogBtn" @click="onCancle">取消</el-button>
        <el-button type="primary" class="addDialogBtn" @click="onOk">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { exportPoInRange } from "@/api/poManage/poInfo";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  searchCondition: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(["update:modelValue"]);

const publishTime = ref([]); // 发布时间

function onCancle() {
  reset();
  emit("update:modelValue", false);
}

/**
 * 导出
 */
async function onOk() {
  const downloadLoadingInstance = ElLoading.service({
    text: "正在下载数据，请稍候",
    background: "rgba(0, 0, 0, 0.7)"
  });
  const params = {
    reportNumber: props.searchCondition.poId,
    event: props.searchCondition.poEvent,
    targetGroup: props.searchCondition.targetGroup === "all" ? "" : props.searchCondition.targetGroup,
    reportSource: props.searchCondition.msgFrom === "all" ? "" : props.searchCondition.msgFrom,
    startTime: publishTime.value[0],
    endTime: publishTime.value[1]
  };
  const res = await exportPoInRange(params);
  if (res.code === 200) {
    const { filePath, fileName } = res.data;
    downloadZipFile(import.meta.env.VITE_APP_BASE_API + filePath, fileName);
  }
  downloadLoadingInstance.close();
  onCancle();
}

/**
 * 下载文件
 */
function downloadZipFile(url, fileName) {
  const link = document.createElement("a");
  link.href = url;
  link.download = fileName || "舆情信息汇总.zip"; // 设置下载文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link); // 清理 DOM
}

/**
 * 重置数据
 */
function reset() {
  publishTime.value = [];
}
</script>

<style lang="scss" scoped>
.dialog-container {
  :deep(.el-dialog) {
    padding: 16px 0 0;
    .el-dialog__header {
      padding-left: 24px;
      .dialogTitle {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #1f2329;
      }
    }

    .el-dialog__body {
      padding: 10px 24px 0;

      .poInfoLabel {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #646a73;
        margin-bottom: 10px;
      }
    }

    .el-dialog__footer {
      height: 76px;
      line-height: 76px;
      background: #ffffff;
      border-radius: 0px 0px 10px 10px;
      padding: 0 24px 0 0;
      margin-top: 12px;

      .cancelDialogBtn,
      .addDialogBtn {
        width: 80px;
        height: 32px;
        line-height: 32px;
        border-radius: 5px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
      }
      .cancelDialogBtn {
        border: 1px solid #cfd2d6;
        color: #1f2329;
      }
    }
  }
}
</style>
