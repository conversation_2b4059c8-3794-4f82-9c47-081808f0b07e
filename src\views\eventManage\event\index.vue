<template>
  <div class="w-full h-full flex flex-col gap-y-[10px]">
    <!-- 表单 -->
    <Form @handle-add="handleAdd" />

    <!-- 卡片列表 -->
    <InfiniteScrollList
      v-slot="{ row }"
      :get-list="getEventList"
      :params="listParams"
    >
      <Card :content="computedContent(row)" @handle-edit="handleEdit" />
    </InfiniteScrollList>

    <!-- 新增编辑弹框 -->
    <AddEditDialog ref="addEditDialogRef" />
  </div>
</template>

<script setup>
import InfiniteScrollList from "@/components/InfiniteScrollList/index.vue";
import Form from "./components/form.vue";
import Card from "./components/card.vue";
import { getEventList } from "@/api/event/index.js";
import { EVENT_STATUS_OPTIONS } from "./config/index.js";
import AddEditDialog from "./components/addEditDialog.vue";

const listParams = ref({
  page: 1,
  pageSize: 10,
  status: "",
});

const addEditDialogRef = ref(null);

const getStatusInfo = (status) => {
  const item = EVENT_STATUS_OPTIONS.find((ele) => ele.value === status);
  return {
    label: item.label,
    color: item.color,
    bgColor: item.bgColor,
  };
};

const computedContent = (row) => {
  return {
    ...row,
    status: getStatusInfo(row?.status),
  };
};

const openDialog = (type, row) => {
  addEditDialogRef.value.openDialog(type, row);
};

const handleEdit = (row) => {
  console.log("---->handleEdit");
  openDialog("edit", row);
};

const handleAdd = () => {
  console.log("---->handleAdd");
  openDialog("add");
};
</script>

<style lang="scss" scoped></style>
