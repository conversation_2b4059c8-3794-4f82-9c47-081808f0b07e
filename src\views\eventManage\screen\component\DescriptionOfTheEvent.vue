<template>
  <div class="description-of-the-event w-full h-full flex flex-col">
    <item-title class="w-full" :title="title" />
    <div class="description-content">
      <div class="simple-info w-full">
        <p>事发时间: {{ parseTime(descriptionOfTheEventData.beginTime) }}</p>
        <!--            <p>责任区县: {{ simpleInfo.deptName }}</p>-->
      </div>
      <div class="briefly">
        <p style="color: #8ba9bb">事件简介:</p>
        <!--        descriptionOfTheEventData.description-->
        <div class="">
          <div class="scroll-container" ref="evtDescScrollContainer">
            <div
              class="scroll-content"
              :class="{ running: isScroll }"
              ref="evtDescScrollContent"
            >
              {{ descriptionOfTheEventData.description }}
            </div>
          </div>
        </div>

        <!-- <div class="h-[230px] text-white overflow-y-auto" style="line-height: 27px;">{{ simpleInfo.description }}</div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import ItemTitle from "@/views/eventManage/screen/component/ItemTitle.vue";
import { parseTime } from "@/utils/ruoyi.js";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  descriptionOfTheEventData: {
    type: Object,
    default: () => ({}),
  },
});

const evtDescScrollContainer = ref(null);
const evtDescScrollContent = ref(null);
const isScroll = ref(false);

const descriptionOfTheEventData = computed(
  () => props.descriptionOfTheEventData,
);

watch(
  () => props.descriptionOfTheEventData,
  (newVal) => {
    init();
  },
  {
    deep: true,
  },
);

function init() {
  nextTick(() => {
    if (
      evtDescScrollContent.value.clientHeight >
      evtDescScrollContainer.value.clientHeight
    ) {
      // 外框小于内容 绑定事件
      isScroll.value = true;
      console.log(isScroll.value);
      evtDescScrollContent.value.addEventListener(
        "mouseenter",
        handleMouseEnter,
      );
      evtDescScrollContent.value.addEventListener(
        "mouseleave",
        handleMouseLeave,
      );
    }
  });
}

const handleMouseEnter = () => {
  isScroll.value = false;
  evtDescScrollContainer.value.scrollTop = 0;
};

const handleMouseLeave = () => {
  evtDescScrollContainer.value.scrollTop = 0;
  isScroll.value = true;
};

onMounted(() => {});

onBeforeUnmount(() => {
  window.removeEventListener("resize", onresize);
  evtDescScrollContainer.value.removeEventListener(
    "mouseenter",
    handleMouseEnter,
  );
  evtDescScrollContainer.value.removeEventListener(
    "mouseleave",
    handleMouseLeave,
  );
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.description-of-the-event {
  gap: px2vw(12);
}

.description-content {
  height: px2vw(400);

  .simple-info {
    font-size: px2vw(14);
    color: #feffff;
    background: url(@/assets/screen/simple-back.svg) no-repeat;
    background-size: 100% 100%;
    height: px2vw(80);
    padding-left: px2vw(19);
    padding-top: px2vw(16);
  }
}

.scroll-container {
  height: 230px;
  color: white;
  overflow: hidden;
  line-height: 27px;
  position: relative; /* 确保子元素的绝对定位相对于此元素 */
}

.briefly {
  // font-size: 14px  p-[15px] h-[270px]
  font-size: px2vw(14);
  padding: px2vw(15);
  height: px2vw(270);
}

.scroll-content {
  position: absolute; /* 使内容脱离文档流，以便动画控制 */
  top: 0;
  left: 0;
  width: 100%;
  &.running {
    animation: scroll 15s linear infinite; /* 调整滚动速度 */
  }
}

@keyframes scroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(calc(-100% + 230px)); /* 计算滚动距离 */
  }
}
</style>
