import portalRequest from '@/utils/portalRequest.js';
export function getWordCloud() {
  return portalRequest({
    url: '/api/wordcloud/data',
    method: 'get',
  });
}

export function getDataShow() {
  return portalRequest({
    url: '/api/statistic/data',
    method: 'get',
  });
}

export function addHotWordsApi(data) {
  return portalRequest({
    url: '/api/wordcloud/addHotWord',
    method: 'post',
    data,
  });
}

export function checkHotWordsApi(params) {
  return portalRequest({
    url: '/sensitive/entry/checkWord',
    method: 'get',
    params,
  });
}
