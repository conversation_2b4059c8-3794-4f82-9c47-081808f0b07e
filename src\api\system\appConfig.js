import portalRequest from "@/utils/portalRequest.js";

/** 获取应用列表 */
export function getAppList(params) {
  return portalRequest({
    url: '/backend/app_info/listAll',
    method: 'get',
    params
  })
}

/** 新增应用 */
export function addApp(data) {
  return portalRequest({
    url: '/backend/app_info/add',
    method: 'post',
    data
  })
}

/** 更新应用 */
export function updateApp(data) {
  return portalRequest({
    url: '/backend/app_info/update',
    method: 'post',
    data
  })
}

/** 获取应用详情 */
export function getAppDetail(appId) {
  return portalRequest({
    url: `/backend/app_info/appDetails/${appId}`,
    method: 'get',
  })
}

/** 删除应用 */
export function deleteApp(params) {
  return portalRequest({
    url: '/backend/app_info/delete',
    method: 'post',
    params
  })
}

/** 获取系统列表下拉选项 */
export function getSysOptions(params) {
  return portalRequest({
    url: '/api/client-sys/searchList',
    method: 'get',
    params
  })
}

/** 设置应用启用还是禁用 */
export function setAppStatus(params) {
  return portalRequest({
    url: '/backend/app_info/enableOrClose',
    method: 'post',
    params
  })
}

/** 获取一级菜单列表 */
export function getFirstMenuList() {
  return portalRequest({
    url: '/backend/menu_info/firstLevelList',
    method: 'get',
  })
}

