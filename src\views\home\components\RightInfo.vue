<template>
  <div class="rightInfo-container flex flex-col">
    <!-- 数量 -->
    <section class="numWrapper flex items-center">
      <div
        class="numItemWrapper flex items-center"
        v-for="item in numList"
        :key="item.key"
      >
        <img
          src="@/assets/images/home/<USER>"
          alt="numIcon"
          class="numImg"
        />
        <div>
          <div class="numLabel">{{ item.label }}</div>
          <div class="numValue">{{ item.value }}</div>
        </div>
      </div>
    </section>

    <!-- 舆情数据分析 -->
    <div class="appTitle">舆情数据分析</div>
    <section class="poAnalysisWrapper relative flex flex-wrap">
      <div
        v-for="item in analysisList"
        :key="item.value"
        class="analysisItemWrapper flex items-center"
      >
        <img class="analysisIcon" :src="item.icon" alt="" />
        <span class="analysisLabel">{{ item.label }}</span>
        <span class="analysisValue" :style="{ color: item.color }">{{
          item.percent
        }}</span>
      </div>
      <PieChart :chart-data="analysisList" class="absolute" />
    </section>

    <!-- 个人事项 -->
    <div class="flex justify-between">
      <div class="appTitle">个人事项</div>
      <div
        class="handleWrapper flex items-center cursor-pointer"
        @click="goHandle"
      >
        <span class="bindBtn">去处理</span>
        <img
          src="@/assets/images/home/<USER>"
          alt="noticeArrow"
          class="bindArrowIcon"
        />
      </div>
    </div>

    <!-- Tab切换： 待办/已办-->
    <div class="tab-wrapper">
      <div class="tab-list">
        <div
          v-for="(item, index) in todoTabList"
          :key="index"
          :class="[
            'tab-item',
            { 'tab-item--active': item.value === activeTabValue },
          ]"
          @click="handleTabClick(item)"
        >
          <div
            :class="[
              'tab-item-text',
              { 'tab-item-text--active': item.value === activeTabValue },
            ]"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>

    <TodoTable
      :columns="PERSON_TODO_COLUMNS"
      :interface-info="interfaceInfo"
      stripe
      @row-click="handleRowClick"
    >
      <template #poName="{ row }">
        <ImagePreview
          v-if="contentIsImg(row.poName)"
          class="image-preview-wrapper"
          :src="getImageUrl(row.poName)"
          :width="80"
          :height="40"
          @click.stop
        />
        <div v-else>
          {{ poContentF(row.poName) }}
        </div>
      </template>
    </TodoTable>
  </div>
</template>

<script setup>
import TodoTable from "./TodoTable.vue";
import PieChart from "./PieChart.vue";
import { PERSON_TODO_COLUMNS } from "../config/tableColumns.js";
import { eventOpinionSum } from "@/api/eventOpinion/index";
import { getSelfPoTaskList } from "@/api/poManage/poHandle";
import { getPoStatisticsInfo } from "@/api/home";
import { SELF_TASK_STATUS } from "@/views/publicOpinionManage/config/constant.js";
import { PO_PROCESS_LIST } from "@/views/publicOpinionManage/config/mapRel";
import poType1 from "@/assets/images/home/<USER>";
import poType2 from "@/assets/images/home/<USER>";
import poType3 from "@/assets/images/home/<USER>";
import poType4 from "@/assets/images/home/<USER>";

const { proxy } = getCurrentInstance();
const router = useRouter();
const { opinion_source_type } = proxy.useDict("opinion_source_type"); // 舆情来源

// 待办tab切换数据
const todoTabList = ref([
  { label: "待办事项", value: SELF_TASK_STATUS.NO_HANDLE },
  { label: "已办事项", value: SELF_TASK_STATUS.FINISH_HANDLE },
]);
const activeTabValue = ref(todoTabList.value[0].value);

const numList = ref([
  { label: "发现舆情总数", value: "0", key: "totalNum" },
  { label: "敏感舆情总数", value: "0", key: "sensitiveNum" },
]);
const analysisList = ref([]);

const interfaceInfo = computed(() => ({
  api: getSelfPoTaskList,
  params: {
    taskStatus: activeTabValue.value,
    disabled: "0",
  },
  getDataMap,
}));

const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.png|\.jpe?g/i.test(val)
); // 内容是否为图片

const getImageUrl = computed(() => (val) => val?.split("<br>")?.[0]);

const poContentF = computed(() => (val) => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter((i) => !contentIsImg.value(i));
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});

/**
 * 待办Tab切换函数
 * @param item 待办事项数据
 */
function handleTabClick(item) {
  activeTabValue.value = item.value;
}

function getDataMap(data) {
  return data.map((ele) => {
    return {
      taskId: ele.taskId,
      todoType:
        PO_PROCESS_LIST.find((i) => i.categoriesCode === ele.commandCategory)
          ?.fontLabel || "",
      // poName: ele.title,
      poName: ele.content,
      deadlineTime: ele.deadlineTime,
      promoter: ele.createBy,
    };
  });
}

async function getSum() {
  const res = await eventOpinionSum();
  if (res.code === 200) {
    numList.value.forEach((ele) => {
      ele.value = String(res.data[ele.key]).replace(
        /\B(?=(\d{3})+(?!\d))/g,
        ","
      );
    });
  }
}

/**
 * 获取分析数据
 */
async function getAnalysisData() {
  const res = await getPoStatisticsInfo();
  if (res.code === 200) {
    analysisList.value = res.data.map((ele, index) => {
      const iconArr = [poType1, poType2, poType3, poType4];
      const colorArr = ["#FF9CA2", "#77B0FF", "#FFD8A9", "#FFE785"];
      return {
        label:
          opinion_source_type.value.find((i) => i.value === ele.reportSource)
            ?.label || "-",
        value: ele.count,
        icon: iconArr[index],
        percent:
          (
            (ele.count / res.data.reduce((acc, cur) => acc + cur.count, 0)) *
            100
          ).toFixed(0) + "%",
        color: colorArr[index],
      };
    });
  }
}

/**
 * 跳转到个人任务界面
 */
function goHandle() {
  const otherRole =
    !proxy.$auth.hasRoleOr(["admin"]) && proxy.$auth.hasRoleOr(["other"]); // 是否是第三方角色
  const path = otherRole
    ? "/publicOpinionCollaboration/collaboration"
    : "/publicOpinionManage/publicOpinionHandle";
  router.push({
    path,
    state: {
      tab: "mine",
    },
  });
}

/**
 * 点击表格行
 */
function handleRowClick(row, pageNum, pageSize) {
  const otherRole =
    !proxy.$auth.hasRoleOr(["admin"]) && proxy.$auth.hasRoleOr(["other"]); // 是否是第三方角色
  const path = otherRole
    ? "/publicOpinionCollaboration/collaboration"
    : "/publicOpinionManage/publicOpinionHandle";
  // console.log("---->row", row);
  // console.log("---->pageNum", pageNum);
  // console.log("---->pageSize", pageSize);
  router.push({
    path,
    state: {
      tab: "mine",
      taskStatus: activeTabValue.value, // 任务状态
      pageNum,
      pageSize,
      poId: row.taskId, // 舆情任务id
    },
  });
}

getSum();
getAnalysisData();
</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;

.rightInfo-container {
  width: 100%;
  height: 100%;

  .numWrapper {
    margin: px2vw(7) 0 px2vw(38) px2vw(30);
    .numItemWrapper {
      width: px2vw(322);
      background: url("@/assets/images/home/<USER>") no-repeat
        right/px2vw(295) px2vw(87);
      &:not(:last-child) {
        margin-right: px2vw(29);
      }

      .numImg {
        width: px2vw(65);
        height: px2vw(59);
        margin-right: px2vw(28);
      }

      .numLabel {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: px2vw(15);
        color: #d9dee6;
        line-height: px2vw(21);
        letter-spacing: px2vw(2);
        margin: px2vw(7) 0;
      }
      .numValue {
        font-family: DINAlternate;
        font-weight: bold;
        font-size: px2vw(36);
        color: #dee3eb;
        line-height: px2vw(42);
        letter-spacing: px2vw(6);
        text-shadow: 0 0 px2vw(6) #538ce7;
        margin-bottom: px2vw(10);
      }
    }
  }

  .appTitle {
    width: px2vw(467);
    height: px2vw(58);
    background: url("@/assets/images/home/<USER>") no-repeat center/cover;
    padding: px2vw(13) 0 px2vw(19) px2vw(65);

    font-family: YouSheBiaoTiHei;
    font-size: px2vw(20);
    color: #eaf4ff;
    line-height: px2vw(26);
  }

  .poAnalysisWrapper {
    height: px2vw(176);
    margin: px2vw(16) px2vw(12) px2vw(42);
    background:
      url("@/assets/images/home/<USER>") no-repeat center/cover,
      url("@/assets/images/home/<USER>") no-repeat
        center/px2vw(183) px2vw(176);

    .analysisItemWrapper {
      width: px2vw(248);
      height: 50%;

      &:nth-of-type(2n + 1) {
        margin-right: px2vw(183);
      }

      .analysisIcon {
        width: px2vw(14);
        height: px2vw(14);
        margin-left: px2vw(42);
        margin-right: px2vw(16);
      }

      .analysisLabel {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: px2vw(15);
        color: #b5bfc8;
        line-height: px2vw(21);
        margin-right: px2vw(22);
      }

      .analysisValue {
        font-family: DINAlternate;
        font-weight: bold;
        font-size: px2vw(22);
        line-height: px2vw(26);
      }
    }
  }

  .tab-wrapper {
    margin-top: px2vw(10);
    margin-bottom: px2vw(10);

    .tab-list {
      display: flex;
      gap: px2vw(5);

      .tab-item {
        width: px2vw(119.6);
        height: px2vw(35);

        display: flex;
        justify-content: center;
        align-items: center;

        cursor: pointer;

        .tab-item-text {
          font-family: YouSheBiaoTiHei;
          font-size: px2vw(16);
          color: #7c839c;
          line-height: px2vw(21);
          text-align: left;
          font-style: normal;
        }

        .tab-item-text--active {
          color: #eaf4ff;
        }
      }

      .tab-item--active {
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: cover;
      }
    }
  }

  .image-preview-wrapper {
    margin-top: px2vw(6);
    margin-bottom: px2vw(5);
  }

  .poContentWrapper {
    white-space: normal;
    word-break: break-all;

    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1; /* 控制几行打点 */
    line-clamp: 1;
    overflow: hidden;
  }

  .handleWrapper {
    margin-right: px2vw(16);
    .bindBtn {
      font-family: YouSheBiaoTiHei;
      font-size: px2vw(15);
      color: #60a4ff;
      line-height: px2vw(20);
      margin-right: px2vw(6);
    }
    .bindArrowIcon {
      width: px2vw(15);
      height: px2vw(19);
    }
  }

  .todoTable-container {
    margin-top: px2vw(7);
    flex: 1;
    height: 100px;
  }
}
</style>
