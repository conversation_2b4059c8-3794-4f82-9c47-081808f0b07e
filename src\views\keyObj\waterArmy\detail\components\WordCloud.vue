<template>
  <div class="w-full h-full">
    <div id="echarts" ref="chartRef" class="w-full h-full" />
  </div>
</template>

<script setup>
const props = defineProps({
  renderData: {
    type: Object,
    default: () => ({}),
  },
});
import echarts from "@/components/ECharts/echarts.js";
import { debounce, deepClone } from "@/utils";

/*
renderData格式
[
  { name: "雨伞", value: 8 },
  { name: "晴天", value: 28 },
  { name: "电话", value: 24 },
  { name: "手机", value: 23 },
  { name: "下雨", value: 22 },
]
*/

const colors = [
  "#007BFF", // 深蓝
  "#FD7E14", // 橙
  "#28A745", // 绿
  "#17A2B8", // 浅蓝
  "#FFC107", // 黄
  "#6F42C1", // 紫（如果有）
];

const wordCloudData = computed(() => {
  return props.renderData.map((item, idx) => ({
    ...item,
    textStyle: { color: colors[idx % colors.length] },
  }));
});

// 当前被渲染的 div 实例，用来初始化 echarts 实例
const chartRef = ref(null);

// echart 实例，不要使用 ref、reactive 响应式变量，会和 echarts 的交互造成冲突，比如 tooltip 不显示
const chartInstance = shallowRef(null);

const baseOptions = {
  tooltip: {
    show: true,
    position: "top",
    textStyle: {
      fontSize: 14,
      fontFamily: "PingFangSC, PingFang SC, sans-serif",
    },
  },
  series: [
    {
      type: "wordCloud",
      gridSize: 25,
      shape: "circle",
      sizeRange: [20, 30],
      rotationRange: [0, 0],
      left: "center",
      top: "center",
      right: null,
      bottom: null,
      width: "90%",
      height: "80%",
      drawOutOfBound: false,
      textStyle: {
        fontFamily: "PingFangSC, PingFang SC, sans-serif",
        emphasis: {
          shadowBlur: 10,
          shadowColor: "#2ac",
        },
      },
      data: [],
    },
  ],
};

function setChartOption() {
  if (chartInstance.value) {
    const option = {
      ...baseOptions,
      series: [
        {
          ...baseOptions.series[0],
          data: wordCloudData.value,
        },
      ],
    };
    chartInstance.value.setOption(option, true);
  }
}

// 初始化图表，并设置第一个数据项为默认值
function initChart() {
  if (!chartRef.value) return;
  chartInstance.value = echarts.getInstanceByDom(chartRef.value);
  if (!chartInstance.value) {
    chartInstance.value = echarts.init(chartRef.value);
  }
}

// options变化时重新加载options
watch(
  () => props.renderData,
  () => {
    setChartOption();
  },
  { deep: true }
);

/**
 * 根据当前窗口大小计算缩放比例
 * @param baseWidth 基础宽度
 * @param baseHeight 基础高度
 */
function getScaleRatio(baseWidth = 1920, baseHeight = 1080) {
  const winWidth = window.innerWidth;
  const winHeight = window.innerHeight;

  const ratioW = winWidth / baseWidth;
  const ratioH = winHeight / baseHeight;

  // 取较小的比例，以保证图形不超出可视区域
  return Math.min(ratioW, ratioH);
}

// 重新调整图表大小的方法
function resizeChart() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 使用防抖避免频繁resize
const debounceResize = debounce(resizeChart, 300);

onMounted(() => {
  initChart();
  setChartOption();
  resizeChart();
  window.addEventListener("resize", debounceResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", debounceResize);
});
</script>

<style scoped lang="less"></style>
