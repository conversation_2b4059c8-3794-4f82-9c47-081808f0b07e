<template>
  <div class="flex items-center">
    <section class="mr-[6px]">
      <el-input
        v-model="searchCondition.accountName"
        placeholder="按照帐号名称搜索"
        :prefix-icon="Search"
        style="width: 240px"
        clearable
        @change="$emit('search')"
      />
    </section>
    <section class="mr-[6px]">
      <el-select
        v-model="searchCondition.platformList"
        placeholder="请选择所属平台"
        clearable
        filterable
        multiple
        collapse-tags
        collapse-tags-tooltip
        style="width: 240px"
        @change="$emit('search')"
      >
        <template #prefix>所属平台</template>
        <el-option
          v-for="item in platformList"
          :key="item.value"
          :label="item.label"
          :value="item.label"
        />
      </el-select>
    </section>
    <section class="mr-[6px]">
      <el-select
        v-model="searchCondition.tagType"
        placeholder="请选择标签类型"
        clearable
        filterable
        style="width: 240px"
        @change="$emit('search')"
      >
        <template #prefix>标签类型</template>
        <el-option label="全部" value="all" />
        <el-option
          v-for="item in tagTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </section>
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";

defineProps({
  // 搜索条件
  searchCondition: {
    type: Object,
    required: true,
  },
  tagTypeList: {
    type: Array,
    required: true,
  },
  platformList: {
    type: Array,
    required: true,
  },
});

defineEmits(["search"]);
</script>

<style lang="scss" scoped></style>
