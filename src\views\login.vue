<template>
  <div class="login-container">
    <div class="w-[429px] h-[53px] mb-[60px]">
      <img
        class="w-full h-full"
        src="@/assets/images/login/title.svg"
        alt="标题"
      />
    </div>
    <div>
      <el-form
        ref="loginRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <el-form-item prop="username">
          <el-input
            @focus="usernameInputBgShow = true"
            @blur="usernameInputBgShow = false"
            v-model="loginForm.username"
            :class="{
              'input-bg': usernameInputBgShow,
            }"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="账号"
          >
            <template #prefix>
              <el-icon><Username /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            @focus="passwordInputBgShow = true"
            @blur="passwordInputBgShow = false"
            v-model="loginForm.password"
            :class="{
              'input-bg': passwordInputBgShow,
            }"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="密码"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon
                icon-class="password"
                class="el-input__icon input-icon"
              />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input
            @focus="codeInputBgShow = true"
            @blur="codeInputBgShow = false"
            v-model="loginForm.code"
            :class="{
              'input-bg': codeInputBgShow,
            }"
            size="large"
            auto-complete="off"
            placeholder="验证码"
            style="width: 67%"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon
                icon-class="validCode"
                class="el-input__icon input-icon"
              />
            </template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img" />
          </div>
        </el-form-item>
        <el-checkbox
          v-model="loginForm.rememberMe"
          class="text-[#fff]"
          style="margin: 0px 0px 25px 0px"
        >
          记住密码
        </el-checkbox>
        <el-form-item style="width: 100%">
          <el-button
            class="login-button"
            :loading="loading"
            size="large"
            type="primary"
            style="width: 100%"
            @click.prevent="handleLogin"
            color="red"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div style="float: right" v-if="register">
            <router-link class="link-type" :to="'/register'">
              立即注册
            </router-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from "@/store/modules/user";
import { isHttp } from "@/utils/validate";
import usePermissionStore from "@/store/modules/permission";
import defaultSettings from "@/settings";
import { useRoute, useRouter } from "vue-router";
import Username from "@/assets/images/login/svg/Username.vue";
import { usePoManageStore } from "@/store/modules/poManage.js";

const { title } = defaultSettings;
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const usernameInputBgShow = ref(false);
const passwordInputBgShow = ref(false);
const codeInputBgShow = ref(false);
const loginRef = ref(null);

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});
// const loginForm = ref({
//   username: "admin",
//   password: "admin123",
//   rememberMe: false,
//   code: "",
//   uuid: ""
// });

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);

function handleLogin() {
  console.log("handleLogin");
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), {
          expires: 30,
        });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore
        .login(loginForm.value)
        .then(async () => {
          const query = route.query;
          const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
            if (cur !== "redirect") {
              acc[cur] = query[cur];
            }
            return acc;
          }, {});
          console.log("getInfo前");

          userStore.getInfo().then(async () => {
            await usePoManageStore().judgeIsWangAn(); // 判断是否是网安端

            usePermissionStore()
              .generateRoutes()
              .then(async (accessRoutes) => {
                // 根据roles权限生成可访问的路由表
                accessRoutes.forEach((route) => {
                  if (!isHttp(route.path)) {
                    router.addRoute(route); // 动态添加可访问路由表
                  }
                });
                await userStore.getIsWJLeader();

                const { isWJLeader } = userStore;
                console.log("登录isWJLeader", isWJLeader);

                if (isWJLeader) {
                  router.push({ path: "/opinionScreen" });
                } else {
                  router.push({
                    path: redirect.value || "/",
                    query: otherQueryParams,
                  });
                }
              });
          });

          // router.push({ path: redirect.value || "/", query: otherQueryParams });
        })
        .catch(() => {
          loading.value = false;
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode();
          }
        });
    }
  });
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value =
      res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

getCode();
getCookie();
</script>

<style lang="scss" scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: url("@/assets/images/login/login-bg.png") no-repeat;
  background-size: cover;
  padding-top: 330px;
  padding-bottom: 302px;
}

.login-form {
  border-radius: 6px;
  background: #ffffff00;
  width: 419px;
  padding: 0 25px 5px 25px;
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}

.input-bg {
  :deep(.el-input__wrapper) {
    background-color: #123a94 !important;
  }
}

.login-button {
  background: linear-gradient(
    180deg,
    #3b78f6 0%,
    #0443d3 100%
  ); /* 这里可以修改为你想要的渐变色 */
  border: none; /* 去掉边框 */
  color: white; /* 文字颜色 */
}

:deep(.el-input__wrapper) {
  background-color: #01236b !important;
  box-shadow: none !important;
  border: 1px solid #18409f;
  padding: 0;
}

:deep(.el-form-item__error) {
  color: #ff6d6d;
}

:deep(.el-input__inner::placeholder) {
  color: #fff;
}

:deep(.el-input__inner) {
  // color: #fff !important;
  width: 100%;
  background: transparent;
  // border: 1px solid #18409f;
  color: #2f5cc6;
  padding: 0 40px;
}

:deep(.el-input__prefix) {
  position: absolute;
  left: 15px;
}

:deep(.el-input__inner:focus) {
  height: 100%;
  background-image: url(@/assets/home/<USER>
  border: none;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: 50%;
  color: #fff;
}
</style>
