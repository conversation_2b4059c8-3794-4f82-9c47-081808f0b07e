<template>
    <el-breadcrumb separator-icon="ArrowRight" class="bd-flder">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbItems"
        :key="index"
        @click="selectItem(item.id)"
      >
        {{ item.label }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </template>
  
  <script setup>
  import { ref, reactive, watch } from 'vue';
  import { ElBreadcrumb, ElBreadcrumbItem } from 'element-plus';
  
  const props = defineProps({
    treeData: {
      type: Array,
      required: true,
    },
    currentId: {
      type: [Number, String],
      required: true,
    },
  });
  
  const emit = defineEmits(['update:currentId']);
  
  const breadcrumbItems = ref([]);
  const generateBreadcrumb = (id, tree) => {
    const path = [];
    let currentNode = findNodeById(id, tree);
    while (currentNode) {
      path.unshift(currentNode);
      currentNode = findParentNode(currentNode, tree);
    }
    return path.map(item => ({ label: item.label, id: item.id }));
  };
  
  const findNodeById = (id, nodes) => {
    for (const node of nodes) {
      if (node.id === id) {
        return node;
      } else if (node.children && node.children.length > 0) {
        const foundNode = findNodeById(id, node.children);
        if (foundNode) {
          return foundNode;
        }
      }
    }
    return null;
  };
  
  const findParentNode = (node, tree) => {
    for (const parentNode of tree) {
      if (parentNode.children && parentNode.children.includes(node)) {
        return parentNode;
      } else if (parentNode.children && parentNode.children.length > 0) {
        const foundParent = findParentNode(node, parentNode.children);
        if (foundParent) {
          return foundParent;
        }
      }
    }
    return null;
  };
  
  watch(
    () => props.currentId,
    () => {
      breadcrumbItems.value = generateBreadcrumb(props.currentId, props.treeData);
      if (breadcrumbItems.value.length > 0) {
        const currentItem = breadcrumbItems.value[breadcrumbItems.value.length - 1];
        const parentItems = breadcrumbItems.value.slice(0, -1);
        breadcrumbItems.value = parentItems.map(parent => ({ label: parent.label, id: parent.id })).concat(currentItem);
      }
    },
  );
  onMounted(() => {
    breadcrumbItems.value = generateBreadcrumb(props.currentId, props.treeData);
  });
  const selectItem = (id) => {
    emit('update:currentId', id);
  };
  </script>
  
  <style lang="scss" scoped>
  .bd-flder {
    :deep(.el-breadcrumb__item) {
     .el-breadcrumb__inner {
        color: #505050;
      }
    }
    :deep(.el-breadcrumb__item:last-child) {
     .el-breadcrumb__inner {
        color: #fff;
      }
    }
  }
  </style>