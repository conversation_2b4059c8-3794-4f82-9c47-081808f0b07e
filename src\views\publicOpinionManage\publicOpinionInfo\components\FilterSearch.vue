<template>
  <div class="flex items-center">
    <section class="mr-[6px]">
      <el-select v-model="searchCondition.targetGroup" placeholder="请选择目标群" clearable style="width: 224px" @change="$emit('search')">
        <template #prefix>目标群</template>
        <el-option label="全部" value="all" />
        <el-option v-for="item in targetGroupList" :key="item" :label="item" :value="item" />
      </el-select>
    </section>
    <section class="mr-[6px]">
      <el-input
        v-model="searchCondition.happenLocation"
        placeholder="请输入舆情属地"
        :prefix-icon="Search"
        style="width: 200px"
        clearable
        @change="$emit('search')"
      />
    </section>
    <section class="mr-[6px]">
      <el-select v-model="searchCondition.articleStatus" placeholder="请选择贴文删除状态" clearable style="width: 224px" @change="$emit('search')">
        <template #prefix>贴文删除状态</template>
        <el-option label="全部" value="all" />
        <el-option v-for="item in ARCTICLE_STATUS_LIST" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </section>
    <section>
      <el-date-picker
        v-model="searchCondition.createTime"
        type="datetimerange"
        start-placeholder="开始报送时间"
        end-placeholder="结束报送时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        style="width: 380px"
        @change="$emit('search')"
      />
    </section>
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";
import { ARCTICLE_STATUS_LIST } from "../../config/constant.js";
import { getTargetGroup } from "@/api/poManage/poInfo.js";

defineProps({
  // 搜索条件
  searchCondition: {
    type: Object,
    required: true
  }
});

defineEmits(["search"]);

const targetGroupList = ref([]);

/**
 * 获取目标群下拉项
 */
async function getTargetGroupList() {
  const res = await getTargetGroup();
  if (res.code === 200) {
    targetGroupList.value = res.data;
  }
}

getTargetGroupList();
</script>

<style lang="scss" scoped></style>
