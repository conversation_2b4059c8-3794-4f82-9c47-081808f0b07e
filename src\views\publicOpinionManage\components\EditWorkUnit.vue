<template>
  <div>
    <el-tree-select
      v-model="workUnitF"
      :placeholder="placeholder"
      clearable
      :data="deptOptions"
      :props="{ value: 'id', label: 'label', children: 'children' }"
      value-key="id"
      check-strictly
      @change="handleSelectChange"
    />
  </div>
</template>

<script setup>
import { AllDeptTree } from "@/api/poManage/poInfo";

const props = defineProps({
  modelValue: {
    type: [Number, null],
    required: true,
  },
  placeholder: {
    type: String,
    default: "请选择上报单位",
  },
});

const emit = defineEmits(["update:modelValue", "handleSelectChange"]);

const workUnitF = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit("update:modelValue", val);
  },
});

const handleSelectChange = () => {
  emit("handleSelectChange");
};

const deptOptions = ref([]);

/**
 * 查询部门下拉树结构
 */
async function getDeptTree() {
  const res = await AllDeptTree();
  if (res.code === 200) {
    deptOptions.value = res.data;
  }
}

getDeptTree();
</script>

<style lang="scss" scoped></style>
