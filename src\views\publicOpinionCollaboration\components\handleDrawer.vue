<template>
  <el-drawer
    class="editInfoDrawer-container"
    v-model="handleVisible"
    :title="dialogTitle"
    :with-header="false"
    :size="1000"
    @close="onClose"
  >
    <img
      class="closeIcon"
      src="@/assets/images/poManage/close.png"
      alt=""
      @click="onClose"
    />

    <div class="drawerWrapper">
      <div class="flex w-full">
        <div class="w-3/5 h-full pt-[16px] pr-[10px] pb-[18px] pl-[26px] flex">
          <!-- 舆情详情 -->
          <Detail
            :show-operation="showOperation"
            :publicOpinionInfo="publicOpinionInfo"
            @showHandleDialog="showDialog('处置')"
            @showDialog="showDialog('转阅')"
            ref="detailRef"
          />
        </div>

        <div class="w-2/5 h-full bg-[#F9FAFC]">
          <!-- 舆情流程 -->
          <!-- <Process :processInfo="processInfo" @closeDrawer="onClose" /> -->
          <PoProcess
            ref="poProcessRef"
            :loading="loading"
            :process-list="processList"
          />
        </div>
      </div>
    </div>
  </el-drawer>

  <!-- 转阅弹窗 -->
  <DispatchDialog
    v-model="showDispatchDialog"
    :type-text="'转阅'"
    :po-info="publicOpinionInfo"
  />

  <!-- 处置弹框 -->
  <HandleDialog
    v-model="showHandleDialog"
    ref="handleDialogRef"
    @submit="onSubmit"
  />
</template>

<script setup>
import Detail from "./detail.vue";
// import Process from "./process.vue";
import PoProcess from "@/views/publicOpinionManage/components/EditInfoDrawer/components/PoProcess.vue";
import DispatchDialog from "@/views/publicOpinionManage/publicOpinionInfo/components/HandleDialog.vue";
import HandleDialog from "./handleDialog.vue";
import {
  toHandleOpinion,
  getProcessInfo,
  getHandleMeasuresOptions,
} from "@/api/publicOpinionCollaboration/index.js";
import {
  PO_PROCESS_LIST,
  PROCESS_TYPE,
  VIEW_PROCESS_CODE,
} from "@/views/publicOpinionManage/config/mapRel";
import { HANDLE2_STATUS } from "@/views/publicOpinionManage/config/constant";
import { HANDLE_RESULT_CODE } from "@/views/publicOpinionCollaboration/config/constant.js";
import { deepClone } from "@/utils";
import { createProcess } from "@/api/poManage/poInfo";

const { proxy } = getCurrentInstance();
const handleVisible = ref(false);

const handleMeasuresOptions = ref([]); // 处置措施选项

const publicOpinionInfo = ref({}); // 舆情详情
const processList = ref([]); // 舆情流程
const loading = ref(false);

const poProcessRef = ref(null);

const dialogType = ref("");
const dialogTitle = computed(() => {
  return dialogType.value === "handle" ? "舆情处理" : "查看";
});

const showOperation = computed(() => {
  return (
    dialogType.value === "handle" &&
    !HANDLE2_STATUS.FINISH_HANDLE.includes(
      publicOpinionInfo.value.handleStatus2
    )
  );
});

const detailRef = ref(null);

const showDispatchDialog = ref(false); // 舆情转阅
const showHandleDialog = ref(false); // 舆情处置

const handleDialogRef = ref(null);
const emit = defineEmits(["refreshData"]);

const processLabel = computed(
  () => (val) =>
    PO_PROCESS_LIST.find((i) => i.categoriesCode === val)?.fontLabel || ""
);
const processIcon = computed(
  () => (val) =>
    PO_PROCESS_LIST.find((i) => i.categoriesCode === val)?.icon || ""
);

/**
 * 获取处置措施
 */
async function getSelectOptions() {
  const res = await getHandleMeasuresOptions();
  if (res.code === 200) {
    handleMeasuresOptions.value = res.data || [];
  }
}

/**
 * 获取舆情流程数据
 */
async function getPoProcess() {
  loading.value = true;
  const res = await getProcessInfo({ reportId: publicOpinionInfo.value.id });
  if (res.code === 200) {
    processList.value = res.data
      .filter((ele) => VIEW_PROCESS_CODE.includes(ele.command))
      .map((ele) => {
        if (ele.commandCategory === PROCESS_TYPE.NEW) {
          return {
            icon: processIcon.value(ele.commandCategory),
            type: processLabel.value(ele.commandCategory),
            time: ele.optTime,
          };
        } else if (ele.commandCategory === PROCESS_TYPE.VIEW) {
          return {
            icon: processIcon.value(ele.commandCategory),
            type: processLabel.value(ele.commandCategory),
            time: ele.optTime,
            sendTo: ele.inChargeList
              .map((i) => `${i.userInChargeName}（${i.deptInChargeName}）`)
              .join("、"),
            selfName: ele.optUserName,
          };
        } else {
          // LC HB CZ
          const continueCode = PO_PROCESS_LIST.filter(
            (i) => i.CONTINUE_CODE
          ).map((i) => i.CONTINUE_CODE);
          const finishCode = PO_PROCESS_LIST.filter((i) => i.FINISHED_CODE).map(
            (i) => i.FINISHED_CODE
          );
          const feedbackProcess = [...continueCode, ...finishCode];
          if (feedbackProcess.includes(ele.command)) {
            let handleResult = "";
            if (continueCode.includes(ele.command)) {
              handleResult = "继续跟进";
            } else {
              handleResult =
                "完成" +
                PO_PROCESS_LIST.find(
                  (i) =>
                    i.CONTINUE_CODE === ele.command ||
                    i.FINISHED_CODE === ele.command
                ).fontText;
            }
            return {
              icon: processIcon.value(ele.commandCategory),
              type: processLabel.value(ele.commandCategory) + "反馈",
              dealTime: ele.deadlineTime,
              handleTime: ele.optTime,
              handler: {
                name: ele.userInChargeName,
                unitName: ele.deptInChargeName,
              },
              handleResult,
              handleOpt:
                handleMeasuresOptions.value.find(
                  (i) => i.code === ele.commandOpt
                )?.label || "",
              handleDesc: ele.commandOptDesc,
              isOverdue:
                new Date(ele.optTime).getTime() >
                new Date(ele.deadlineTime).getTime(), // 是否舆逾期
            };
          } else {
            return {
              icon: processIcon.value(ele.commandCategory),
              type: processLabel.value(ele.commandCategory),
              time: ele.optTime,
              handler: ele.inChargeList
                .map((i) => `${i.userInChargeName}（${i.deptInChargeName}）`)
                .join("、"),
              dealTime: ele.deadlineTime,
              selfName: ele.optUserName,
            };
          }
        }
      });
  }
  loading.value = false;
}

/**
 * 将待查看转为已查看，同时刷新相关数据
 */
async function toViewed() {
  const params = {
    reportIds: [publicOpinionInfo.value.id],
    workId: publicOpinionInfo.value.taskId,
    command: "VIEW_DONE",
  };

  const res = await createProcess(params);
  if (res.code === 200) {
    getPoProcess();
    emit("refreshData");
  }
}

/*
 * 打开抽屉
 */
const openDrawer = async (type, row) => {
  console.log("---->type", type);

  handleVisible.value = true;
  dialogType.value = type;

  console.log("---->dialogType.value", dialogType.value);
  // 舆情详情
  if (row?.id) {
    publicOpinionInfo.value = deepClone(row);

    // 舆情流程   更换为舆情管理那里的处理
    // const res = await getProcessInfo({ reportId: row?.id });
    // processInfo.value =
    //   res.data?.map((item) => {
    //     let changeStr;
    //     if (item?.inChargeList?.length > 0) {
    //       changeStr = item?.inChargeList
    //         ?.map((i) => {
    //           if (i?.deptInChargeName && i?.userInChargeName) {
    //             return i?.deptInChargeName + "（" + i?.userInChargeName + "）";
    //           } else {
    //             return undefined; // 后面会自动显示'-'
    //           }
    //         })
    //         .join("、");
    //     }
    //     return {
    //       ...item,
    //       chargeListStr: changeStr,
    //     };
    //   }) || [];

    // console.log("---->processInfo.value", processInfo.value);

    await getSelectOptions();

    // [处置-我的任务]如果是转阅-待查看，将待查看变成已查看
    if (
      publicOpinionInfo.value.commandCategory === PROCESS_TYPE.VIEW &&
      HANDLE2_STATUS.WAIT_VIEW.includes(publicOpinionInfo.value.handleStatus2)
    ) {
      toViewed();
    } else {
      getPoProcess();
    }
  }
};

/*
 * 打开弹框
 */
const showDialog = (type) => {
  switch (type) {
    case "转阅":
      showDispatchDialog.value = true;
      break;
    case "处置":
      showHandleDialog.value = true;
      break;
    default:
      break;
  }
};

/*
 * 舆情处置提交
 */
const onSubmit = (formData) => {
  handleDialogRef.value.handleFormRef.validate(async (val) => {
    if (val) {
      const params = {
        workId: publicOpinionInfo.value.workId,
        reportId: publicOpinionInfo.value.id,
        command: formData.handleResult,
        commandOpt: formData.handleMeasures,
        commandOptDesc: formData.description,
      };
      const res = await toHandleOpinion(params);
      if (res.code === 200) {
        proxy.$modal.msgSuccess(res.msg);

        if (formData.handleResult === HANDLE_RESULT_CODE.FINISHED_CODE) {
          dialogType.value = "view"; // 改成查看状态
        }

        // onClose();
        showHandleDialog.value = false;
        getPoProcess();
        emit("refreshData");
      }
    }
  });
};

/*
 * 抽屉关闭
 */
const onClose = () => {
  handleVisible.value = false;
};

defineExpose({ openDrawer });
</script>

<style lang="scss" scoped>
.closeIcon {
  width: 20px;
  height: 20px;
  position: fixed;
  top: 71px;
  right: 990px;
  cursor: pointer;
}
.drawerWrapper {
  width: 100%;
  height: 100%;
  display: flex;
}
</style>

<style>
.editInfoDrawer-container.el-drawer .el-drawer__body {
  padding: 0;
}
</style>
