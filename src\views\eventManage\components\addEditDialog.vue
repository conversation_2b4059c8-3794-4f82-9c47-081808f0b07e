<template>
  <el-dialog
    v-model="addEditVisible"
    @closed="onCancel"
    :title="dialogTitle"
    width="700"
  >
    <el-form
      ref="addEditFormRef"
      :model="dialogForm"
      label-width="140"
      :rules="ADD_EDIT_RULES"
    >
<!--      <el-form-item label="事件名称" prop="name">
        <el-input
            clearable
            v-model="dialogForm.name"
            maxlength="50"
            show-word-limit
            placeholder="请输入事件名称"
        />
      </el-form-item>
      <el-form-item label="事件类型" prop="type">
        <el-select
            clearable
            v-model="dialogForm.type"
            placeholder="请选择事件类型"
        >
          <el-option
              v-for="item in event_type"
              :key="item.value"
              :label="item?.label"
              :value="item?.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="事件开始时间" prop="beginTime">
        <el-date-picker
            v-model="dialogForm.beginTime"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetime"
            :disabled-date="disabledDateStartRange"
            placeholder="请选择事件开始时间"
        />
      </el-form-item>
      <el-form-item label="事件概述" props="description">
        <el-input
            v-model="dialogForm.description"
            :rows="4"
            :autosize="{ minRows: 4, maxRows: 20 }"
            show-word-limit
            maxlength="500"
            type="textarea"
            placeholder="请输入事件概述"
        />
      </el-form-item>
      <el-form-item label="是否公开" prop="type">
        <el-radio-group v-model="dialogForm.resource">
          <el-radio value="Sponsor">是</el-radio>
          <el-radio value="Venue">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="事件属地" prop="ownDeptId">
        <el-select
            clearable
            v-model="dialogForm.ownDeptId"
            placeholder="请选择事件属地"
        >
          <el-option
              v-for="item in deptList"
              :key="item.deptId"
              :label="item?.deptName"
              :value="item?.deptId"
          />
        </el-select>
      </el-form-item>-->

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="事件名称" prop="name">
            <el-input
                clearable
                v-model="dialogForm.name"
                maxlength="50"
                show-word-limit
                placeholder="请输入事件名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件类型" prop="type">
            <el-select
                clearable
                v-model="dialogForm.type"
                placeholder="请选择事件类型"
            >
              <el-option
                  v-for="item in event_type"
                  :key="item.value"
                  :label="item?.label"
                  :value="item?.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件开始时间" prop="beginTime">
            <el-date-picker
                v-model="dialogForm.beginTime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                :disabled-date="disabledDateStartRange"
                placeholder="请选择事件开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="事件概述" props="description">
            <el-input
                v-model="dialogForm.description"
                :rows="4"
                :autosize="{ minRows: 4, maxRows: 20 }"
                show-word-limit
                maxlength="500"
                type="textarea"
                placeholder="请输入事件概述"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否公开" prop="isOpen">
            <el-radio-group v-model="dialogForm.isOpen">
              <el-radio :value="true">是</el-radio>
              <el-radio :value="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件属地" prop="ownDeptId" v-if="!dialogForm.isOpen">
            <el-tree-select
                v-model="dialogForm.ownDeptId"
                :data="deptList"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择事件属地"
                check-strictly
            />
          </el-form-item>



        </el-col>
      </el-row>
      <el-form-item label="配置舆情采集策略"> </el-form-item>
      <el-row>
        <div class="w-6/7 flex flex-col relative">
          <el-form-item label="选择方案" prop="solutionId">
            <el-select
              clearable
              v-model="dialogForm.solutionId"
              placeholder="请选择方案"
            >
              <el-option
                v-for="item in solutionOptions"
                :key="item.id"
                :label="item?.name"
                :value="item?.id"
              />
            </el-select>
            <span>若无可用方案，请点击创建方案按钮进行舆情采集方案的创建 </span>
          </el-form-item>
<!--          <div class="absolute top-[35px] left-[140px] text-[12px]">-->
<!--           -->
<!--          </div>-->
        </div>

        <div class="w-1/7 h-[32px] flex justify-center items-center">
          <el-button type="primary" link @click="handleAddScheme"
            >创建方案</el-button
          >
        </div>
      </el-row>

<!--      <el-form-item label="选择钉钉群" prop="dingdingGroup">-->
<!--        <el-select-->
<!--          clearable-->
<!--          v-model="dialogForm.dingdingGroup"-->
<!--          placeholder="请选择钉钉群"-->
<!--        >-->
<!--          <el-option-->
<!--              v-for="(item, index) in dingTalkGroupOptions"-->
<!--              :key="index"-->
<!--              :label="item"-->
<!--              :value="item"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->

      <el-form-item label="选择微信群" prop="weixinGroup">
        <el-select
          clearable
          v-model="dialogForm.weixinGroup"
          placeholder="请选择微信群"
        >
          <el-option
            v-for="(item, index) in wechatGroupOptions"
            :key="index"
            :label="item.groupName"
            :value="item.groupId"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <div class="w-full flex justify-end">
          <el-button @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onSubmit"> 提交 </el-button>
        </div>
      </el-form-item>
    </el-form>

    <!-- 创建方案对话框 -->
    <el-dialog title="创建方案" v-model="planOpen" width="700" append-to-body>
      <el-form :model="planForm" :rules="planRules" ref="planRef" label-width="120px" class="form-container">
        <el-form-item label="方案名称" prop="name">
          <el-input v-model="planForm.name" placeholder="请输入方案名称"/>
        </el-form-item>
        <el-form-item label="监控方案设置">
          <span class="tips">提示：关键词之间为并且关系，满足所有关键词的舆情信息才可被收集，填写一个关键词后，需要用点下方的"且"按钮对关键词进行分隔，关键词方可生效</span>
        </el-form-item>
        <el-form-item label="监控关键词" prop="keyword">
          <el-input v-model="planForm.keyword" placeholder="请输入监控关键词"/>
          <!--<br />
          <div style="display:flex; flex-direction: column">
            <div class="tips">示例：特斯拉&小米&车辆相撞&人员伤亡</div>
            <el-button type="primary" @click="handleSplice" style="width: 60px">且</el-button>
          </div>-->

        </el-form-item>
        <el-form-item>
          <div style="display:flex; flex-direction: column">
            <div class="tips">示例：特斯拉&小米&车辆相撞&人员伤亡</div>
            <el-button type="primary" @click="handleSplice" style="width: 60px">且</el-button>
          </div>

        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPlanForm">确 定</el-button>
          <el-button @click="planCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ADD_EDIT_RULES } from "../config/index.js";
import {
  addEvent,
  editEvent,
  listSolution,
  findGroups
} from "@/api/eventManagement/index.js";
import {useDict} from "@/utils/dict.js";
import { addPlan } from "@/api/plan/plan.js"

import { deptTreeSelect } from "@/api/system/user";

const { event_type } = useDict('event_type')
import { getDeptListData } from "@/api/system/dept";
const emit = defineEmits(["refreshList"]);
const addEditFormRef = ref(null); // 表单组件
const addEditVisible = ref(false);
const dialogForm = ref({
  name: "",
  type: "",
  beginTime: "",
  description: "",
  solutionId: "",
  weixinGroup: "",
  dingdingGroup: "",
  isOpen: true
});
const planOpen = ref(false)
const checkKeyword = (rule, value, callback) =>{
  let list = [' ','，']
  if (value == '' || value == null) {
    callback(new Error('关键词不得为空'))
  } else {
    list.forEach(e => {
      if (value.toString().indexOf(e) > -1) {
        callback(new Error('关键词无法解析'))
      }
    })
    callback()
  }
}
const data = reactive({
  planForm: null,
  planRules: {
    name: [
      { required: true, message: '方案名称不可为空', trigger: 'submit' },
    ],
    keyword: [
      { required: true, validator: checkKeyword, trigger: 'submit'}
    ]
  }
})
const {
  planForm,
  planRules
} = toRefs(data)

const solutionOptions = ref([]);
const dingTalkGroupOptions = ref([]);
const wechatGroupOptions = ref([]);
const deptList = ref([])

const { proxy } = getCurrentInstance();

const dialogTitle = ref('')

function submitPlanForm() {
  proxy.$refs["planRef"].validate(valid => {
    if (valid) {
      addPlan(planForm.value).then(res => {
        if(res.code === 200) {
          ElMessage({
            type: 'success',
            message: res.msg,
          })
          planOpen.value = false
          getSolutionOptions()
        }else {
          ElMessage({
            type: 'danger',
            message: res.msg,
          })
        }
      })
    }
  });
}
function planCancel() {
  planOpen.value = false
}
//插入
function handleSplice() {
  if(planForm.value.keyword.length > 0) {
    let str = planForm.value.keyword.split('').reverse().join()
    if(str[0] !== '&') {
      planForm.value.keyword +='&'
    }
  }
}
function resetPlanForm() {
  planForm.value = {
    name: '新方案'
  }
}
/**
 * 禁用开始时间选择框的日期
 */
const disabledDateStartRange = (date) => {
  return date.getTime() > new Date().getTime();
};

// 弹框打开事件
const openDialog = (title, row) => {
  resetForm();
  addEditVisible.value = true;
  dialogTitle.value = title
  getSolutionOptions()
  getGroupOptions()
  getDeptList()
  // 编辑
  if (row && row.id) {
    dialogForm.value = row;
  }
};
const resetForm = () => {
  dialogForm.value = {
    name: "",
    type: "",
    beginTime: "",
    description: "",
    solutionId: "",
    wechatGroup: "",
    dingTalkGroup: "",
    status: '1',
    isOpen: true
  };
  if (proxy.$refs.addEditFormRef) {
    proxy.resetForm("addEditFormRef");
  }
};

const handleAddScheme = () => {
  // proxy.$router.push('/smartPatrol/smartPatrolPlan')
  planOpen.value = true
  resetPlanForm()
};

const onCancel = () => {
  addEditVisible.value = false;
};
const onSubmit = () => {
  addEditFormRef.value.validate(async (val) => {
    if (val) {
      console.log("表单数据", dialogForm.value);
      const params = {
        ...dialogForm.value,
      };
      if (dialogForm.value.id) {
        const res = await editEvent(params);
        if (res.code === 200) {
          proxy.$message.success(res.msg);
          addEditVisible.value = false;
          emit("refreshList");
        }
      } else {
        const res = await addEvent(params);
        if (res.code === 200) {
          proxy.$message.success(res.msg);
          addEditVisible.value = false;
          emit("refreshList");
        }
      }
    }
  });
};

function getSolutionOptions() {
  listSolution().then(res => {
    solutionOptions.value = res.data
  })
}
function getGroupOptions() {
  findGroups(1).then(res => {
    wechatGroupOptions.value = res.data
  })
  findGroups(2).then(res => {
    dingTalkGroupOptions.value = res.data
  })
}

//获取事件属地列表
function getDeptList() {
  getDeptListData().then(res => {
    if(res.code === 200) {
      deptList.value = res.data
      // deptList.value = proxy.handleTree(res.data, "deptId");
    }
  }).catch(err => {})
}

defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
.addTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1f2329;
  line-height: 22px;
}

.addDialogBtn {
  width: 80px;
  height: 32px;
  line-height: 20px;
  background: #01b197;
  border-radius: 5px;
  font-size: 14px;
  color: #ffffff;
}
.cancelDialogBtn {
  width: 80px;
  height: 32px;
  line-height: 32px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #cfd2d6;
  font-size: 14px;
  color: #1f2329;
}

:deep(.el-input__prefix) {
  width: 0;
  display: none;
}

:deep(.el-input__inner) {
  padding: 0;
}


</style>
