<template>
  <div class="w-full">
    <FilterSearch
      class="mb-[10px]"
      show-task-status
      :search-condition="searchCondition"
      :msg-from-option="opinion_source_type"
      @search="refreshData"
    />

    <!-- 舆情表格 -->
    <PublicOpinionTable
      ref="publicOpinionTableRef"
      :interface-info="interfaceInfo"
      :table-columns="PUBLIC_OPINION_INFO_COLUMNS(['selection', 'score'])"
      :operation-column-width="120"
      :show-mul-select="false"
      rowKey="taskId"
      @refreshData="refreshData"
    >
      <template #operation="{ row }">
        <el-button style="color: #0070ff" link @click.stop="showDrawer(row)">{{
          operationBtnText(row)
        }}</el-button>
      </template>
    </PublicOpinionTable>

    <!-- 查看抽屉 -->
    <EditInfoDrawer
      v-model="showInfoDrawer"
      :drawer-type="drawerType"
      :po-info="poInfo"
      @refreshData="refreshData"
    />
  </div>
</template>

<script setup>
import FilterSearch from "@/views/publicOpinionManage/components/FilterSearch.vue";
import PublicOpinionTable from "@/views/publicOpinionManage/components/PublicOpinionTable.vue";
import EditInfoDrawer from "@/views/publicOpinionManage/components/EditInfoDrawer/index.vue";
import { PUBLIC_OPINION_INFO_COLUMNS } from "@/views/publicOpinionManage/config/tableColumns";
import { getSelfPoTaskList } from "@/api/poManage/poHandle";
import { usePoManageStore } from "@/store/modules/poManage.js";
import { PROCESS_TYPE } from "@/views/publicOpinionManage/config/mapRel";
import { HANDLE2_STATUS } from "@/views/publicOpinionManage/config/constant";

const { proxy } = getCurrentInstance();
const { opinion_source_type, opinion_type, media_type } = proxy.useDict(
  "opinion_source_type",
  "opinion_type",
  "media_type"
); // 舆情来源 舆情类型 媒体类型
const poManageStore = usePoManageStore();

// 搜索条件
const searchCondition = ref({
  taskStatus: "all",
  poId: "",
  poEvent: "",
  msgFrom: "all",
  targetGroup: "all",
});
const publicOpinionTableRef = ref();
const showInfoDrawer = ref(false);
const drawerType = ref("");
const poInfo = ref({});

const interfaceInfo = computed(() => {
  let params = {};
  if (history.state.poId) {
    // 从首页来，根据传递的参数查询数据
    const { taskStatus, pageNum, pageSize } = history.state;
    params = { taskStatus, pageNum, pageSize };
  }

  return {
    api: getSelfPoTaskList,
    params: { disabled: "0", ...params },
    getDataMap,
  };
});

const operationBtnText = computed(
  () =>
    ({ handleStatus2, commandCategory, feedBackId }) => {
      // console.log("handleStatus2", handleStatus2);
      // console.log("commandCategory", commandCategory);

      // console.log("PROCESS_TYPE.VIEW", PROCESS_TYPE.VIEW);
      // console.log("HANDLE2_STATUS.FINISH_HANDLE", HANDLE2_STATUS.FINISH_HANDLE);

      // console.log("poManageStore.isWangAn", poManageStore.isWangAn);

      if (poManageStore.isWangAn) {
        if (feedBackId) {
          return "审核";
        } else {
          return commandCategory !== PROCESS_TYPE.VIEW &&
            !HANDLE2_STATUS.FINISH_HANDLE.includes(handleStatus2)
            ? "处理"
            : "查看";
        }
      } else {
        return "查看";
      }
    }
);

onMounted(() => {
  // 从首页来，回填任务状态
  if (history.state.poId) {
    const { taskStatus } = history.state;
    searchCondition.value.taskStatus = taskStatus;
  }
});

const unwatch = watchEffect(() => {
  // 从首页来，展示详情抽屉
  if (history.state.poId) {
    if (!publicOpinionTableRef.value?.tableData.length) return;
    const { tableData } = publicOpinionTableRef.value;
    const { poId } = history.state;
    const poInfoObj = tableData.find((ele) => ele.taskId === poId);
    showDrawer(poInfoObj);
    unwatch();
  }
});

/**
 * 数据整理
 */
function getDataMap(data) {
  return data.map((ele) => {
    return {
      id: ele.id,
      taskId: ele.taskId,
      poId: ele.reportNumber,
      isFirst: ele.firstReportTag, // 0否 1是
      poName: ele.title,
      poContent: ele.content,
      poLink: ele.linkUrl,
      poEvent: ele.event?.length ? ele.event : "-",
      workUnit: ele.workUnit || "-", // 上报单位
      poMediaType:
        media_type.value.find((i) => Number(i.value) === ele.mediaType)
          ?.label || "-", // 媒体类型
      poFrom:
        opinion_source_type.value.find((i) => i.value === ele.reportSource)
          ?.label || "-",
      platformType: ele.platformTag || "-",
      happenLocation: ele.involvedArea || "-",
      isSensitive: ele.sensitiveTag + "", // 0否 1是
      poType:
        opinion_type.value.find((i) => i.value === ele.categoryTag)?.label ||
        "-",
      wechatNickname: ele.wechatNickname || "-", // 微信报送昵称
      netizenNickname: ele.netizenNickname || "-", // 网名昵称
      netizenAccountId: ele.netizenAccountId, // 网名id
      netizenAccount: ele.netizenAccount || "-", // 网名账号
      publicTime: ele.publishTime || "-",
      poImg: ele.photoUrl ? ele.photoUrl.split(",") : "-",
      articleStatus: ele.sourceStatus,
      handleStatus: ele.reportProcess,
      createTime: ele.createTime || "-",
      targetGroup: ele.targetGroup || "-",
      isInvalid: ele.disabled,
      handleUnit: ele.deptName,
      handler: ele.userName,
      handleStatus2: ele.processStep, // 当前任务所处阶段
      commandCategory: ele.commandCategory, // 当前任务的类别
      linkUrlCount: ele.linkUrlCount,
      feedBackId: ele.feedBackId,
      newWorkUnit: ele.newWorkUnit,
      deptName: ele.deptName, // 处置单位
      newWorkUnitId: ele.newWorkUnitId,
    };
  });
}

/**
 * 刷新表格数据
 */
function refreshData() {
  const params = {
    disabled: "0",
    taskStatus:
      searchCondition.value.taskStatus === "all"
        ? ""
        : searchCondition.value.taskStatus,
    reportNumber: searchCondition.value.poId,
    event: searchCondition.value.poEvent,
    reportSource:
      searchCondition.value.msgFrom === "all"
        ? ""
        : searchCondition.value.msgFrom,
    targetGroup:
      searchCondition.value.targetGroup === "all"
        ? ""
        : searchCondition.value.targetGroup,
  };
  publicOpinionTableRef.value.getTableData(params);
}

/**
 * 展示查看抽屉
 */
function showDrawer(row) {
  poInfo.value = row;
  drawerType.value =
    operationBtnText.value(row) === "查看" ? "onlyView" : "handle";
  showInfoDrawer.value = true;
}
</script>

<style lang="scss" scoped>
.publicOpinionTable-container {
  height: calc(100% - 42px);
}
</style>
