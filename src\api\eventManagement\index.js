import portalRequest from "@/utils/portalRequest.js";

// 事件的 增删改查
// 查询
export function listEvent(query) {
  query.status = query.status === '-1' ? '' : query.status;
  return portalRequest({
    url: `/event/manage/list`,
    method: "get",
    params: query,
  });
}

// 删除
export function removeEvent(ids) {
  return portalRequest({
    url: `/event/manage/remove`,
    method: "post",
    data: ids
  });
}

// 编辑
export function editEvent(data) {
  return portalRequest({
    url: `/event/manage/modify`,
    method: "post",
    data: data
  });
}

// 添加
export function addEvent(data) {
  return portalRequest({
    url: `/event/manage/add`,
    method: "post",
    data: data
  });
}

// 暂停事件
export function updateStatus(data) {
  return portalRequest({
    url: `/event/manage/status`,
    method: "post",
    data: data
  });
}

// 选择方案
export function listSolution(query) {
  return portalRequest({
    url: `/solution/manage/list`,
    method: "get",
    params: query,
  });
}

// 钉钉&微信群
export function findGroups(type) {
  return portalRequest({
    url: `/event/manage/find/group?groupType=${type}`,
    method: "get",
  });
}

// 事件下舆情信息
export function eventDetails(query) {
  return portalRequest({
    url: `/event/manage/info/details`,
    method: "get",
    params: query,
  });
}

// 移除事件下舆情信息
export function removeEventOpinion(data) {
  return portalRequest({
    url: `/event/manage/remove/opinion`,
    method: "post",
    data: data,
  });
}

// 添加事件下舆情信息
export function addEventOpinion(data) {
  return portalRequest({
    url: `/event/manage/add/opinion`,
    method: "post",
    data: data,
  });
}
// 查询事件统计数据
export function eventSumInfo(id) {
  return portalRequest({
    url: `/event/manage/sum/info?eventId=${id}`,
    method: "get"
  });
}

// 获取实时舆情有关信息
export function singleOpinionInfo(id) {
    return portalRequest({
      url: `/event/single/opinion/get/info?eventId=${id}`,
      method: "get",
    });
}
// 获取平台贴文分布有关信息
export function singleOpinionPlatformNum(id) {
    return portalRequest({
      url: `/event/single/opinion/platform/num?eventId=${id}`,
      method: "get",
    });
}
// 事件说明
export function singleOpinionSimple(id) {
    return portalRequest({
      url: `/event/single/opinion/simple?eventId=${id}`,
      method: "get",
    });
}
// 获取敏感贴文处理情况
export function singleOpinionSensitive(id) {
    return portalRequest({
      url: `/event/single/opinion/sensitive?eventId=${id}`,
      method: "get",
    });
}
// 获取敏感贴文处理情况
export function singleOpinionVideo(id) {
    return portalRequest({
      url: `/event/single/opinion/video?eventId=${id}`,
      method: "get",
    });
}
// 获取事件情况汇总
export function singleOpinionSum(id) {
    return portalRequest({
      url: `/event/single/opinion/get/sum?eventId=${id}`,
      method: "get",
    });
}
// 获取舆情热度趋势
export function singleOpinionTimeRange(id) {
    return portalRequest({
      url: `/event/single/opinion/time/range?eventId=${id}`,
      method: "get",
    });
}
// 获取舆情战绩展示
export function singleOpinionShow(id) {
    return portalRequest({
      url: `/event/single/opinion/show?eventId=${id}`,
      method: "get",
    });
}

//态势感知视频
export function singleOpinionSensitiveVideo() {

}
