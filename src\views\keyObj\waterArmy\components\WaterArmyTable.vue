<template>
  <div class="WaterArmyTable-container" ref="tableContainerRef">
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :loading="tableLoading"
      :show-default-btn="false"
      :show-operation-column="true"
      :operation-column="operationColumnWidth"
      no-padding
      :max-height="tableHeightF"
      :highlight-current-row="false"
      :row-key="rowKey"
      @handle-selection-change="handleSelectionChange"
      :isStripe="false"
    >
      <template #homePageLink="{ row }">
        <el-link
          v-if="row.homePageLink"
          class="linkText truncate"
          :underline="false"
          :href="row.homePageLink"
          target="blank"
          @click.stop
          ><div class="flex gap-[10px] items-center">
            <span class="truncate max-w-[160px]">{{ row.homePageLink }}</span
            ><img
              src="@/assets/images/networkuser/water-link.png"
              alt="link"
              class="w-[15px] h-[15px]"
            /></div
        ></el-link>
        <div v-else>-</div>
      </template>
      <template #judgeLabel="{ row }">
        <div class="grid grid-cols-1 gap-[10px]">
          <div
            v-for="(item, index) in row.judgeLabel"
            :key="index"
            :class="[
              'judge-label-wrapper',
              {
                'act-expception': item === 'ACT',
                'ip-expception': item === 'IP',
              },
            ]"
          >
            {{ getDict(item, tagTypeList) || "-" }}
          </div>
        </div>
      </template>
      <template #eventInfoList="{ row }">
        <el-dropdown trigger="click" v-if="row.eventInfoList?.length > 1">
          <template #default>
            <div
              class="text-[14px] flex items-center gap-[10px] text-[#0070ff] cursor-pointer"
            >
              <span class="truncate max-w-[150px]">{{
                row.eventInfoList[0]?.eventName
              }}</span>

              <el-icon><CaretBottom /></el-icon>
            </div>
          </template>

          <template #dropdown>
            <el-dropdown-menu>
              <el-scrollbar max-height="220px">
                <el-dropdown-item
                  v-for="(item, index) in row.eventInfoList"
                  :key="index"
                  @click="handleClickEvent(item)"
                >
                  <OverflowTooltip :content="item.eventName">
                    <div class="truncate max-w-[122px]">
                      {{ item.eventName }}
                    </div>
                  </OverflowTooltip>
                </el-dropdown-item>
              </el-scrollbar>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <div
          v-else-if="row.eventInfoList?.[0]?.eventName"
          class="text-[14px] text-[#0070ff] truncate cursor-pointer w-[150px]"
          @click="handleClickEvent(row.eventInfoList[0])"
        >
          {{ row.eventInfoList[0]?.eventName }}
        </div>
        <div v-else>-</div>
      </template>

      <template #operation="{ row }">
        <slot name="operation" :row="row">
          <el-button link type="primary" @click="toDetail(row)">查看</el-button>
          <el-button
            link
            type="primary"
            @click.stop="$emit('removeWaterArmy', row)"
            >移除水军</el-button
          >
        </slot>
      </template>
    </CommonTable>

    <div class="flex justify-between items-center">
      <!-- 批量操作 -->
      <div class="flex items-center text-[14px]">
        <span class="mr-[30px]" v-if="showMulSelect"
          >已选择{{ selectedMemberCount }}项</span
        >
      </div>
      <!-- 分页 -->
      <pagination
        v-model:page="pageObj.pageNum"
        v-model:limit="pageObj.pageSize"
        :total="pageObj.total"
        @pagination="$emit('refreshData')"
      />
    </div>
  </div>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
import { useFetchTableData } from "@/hooks/useFetchTableData";
import OverflowTooltip from "@/components/OverflowTooltip/index.vue";
import { useTableHeight } from "@/hooks/useTableHeight";
import { useRouter } from "vue-router";
import { getDict } from "@/utils/dict.js";

const props = defineProps({
  // 获取数据的接口相关信息
  interfaceInfo: {
    type: Object,
    default: () => ({}),
  },
  // 表格列配置
  tableColumns: {
    type: Array,
    default: () => [],
  },
  // 操作栏宽度
  operationColumnWidth: {
    type: Number,
    default: 220,
  },
  // 是否展示多选列
  showMulSelect: {
    type: Boolean,
    default: false,
  },
  // 表格最大高度
  tableMaxHeight: {
    type: Number,
    default: 0,
  },
  rowKey: {
    type: [String, Function],
    default: "id",
  },
  tagTypeList: {
    type: Array,
    required: true,
  },
});
const emit = defineEmits(["refreshData", "removeWaterArmy"]);

// 如果存在tab切换，重新计算表格高度
const activeTabName = inject("activeTabName", ref(""));
watch(activeTabName, async (val) => {
  if (val) {
    await nextTick(); // 等待元素渲染完成
    getTableHeight();
  }
});

const router = useRouter();

const { proxy } = getCurrentInstance();
const { tableData, tableLoading, pageObj, getTableData } = useFetchTableData(
  props.interfaceInfo
);

const tableContainerRef = ref();
const { tableHeight, getTableHeight } = useTableHeight(
  tableContainerRef,
  32 + 10
);
const tableHeightF = computed(() =>
  props.tableMaxHeight ? props.tableMaxHeight : tableHeight.value
); // 兼容固定高度和自适应高度
const selectedMemberList = ref([]); // 已选人员信息
const selectedMemberCount = computed(() => selectedMemberList.value.length); // 已选个数

/**
 * 跳转详情
 * @param row 水军信息
 */
const toDetail = (row) => {
  router.push({
    name: "WaterArmyDetail",
    state: {
      waterArmyId: row.id,
    },
  });
};

/**
 * 点击事件
 * @param item 事件信息
 */
const handleClickEvent = (item) => {
  router.push({
    path: "/eventManage",
    state: {
      eventId: item.id,
    },
  });
};

/**
 * 表格多选
 */
function handleSelectionChange(selectedList) {
  selectedMemberList.value = selectedList;
  emit("mulSelect", selectedList);
}

defineExpose({
  tableData,
  getTableData,
});
</script>

<style lang="scss" scoped>
.WaterArmyTable-container {
  width: 100%;
  height: 100%;

  :deep(.linkText) {
    display: inline;
    // font-family:
    //   PingFangSC,
    //   PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #0070ff;

    &:visited {
      color: #0070ff;
    }
    .el-link__inner {
      display: inline;
    }
  }

  :deep(.el-dropdown-menu__item) {
    width: auto;
  }

  .judge-label-wrapper {
    width: 123px;
    text-align: center;
    padding: 10px 20px;
    // background-color: #f9d4d8;
    // color: #d9001b;
  }

  .act-expception {
    background-color: #f9d4d8;
    color: #d9001b;
  }

  .ip-expception {
    background-color: #c9e7ff;
    color: #0070ff;
  }
}

.operationWrapper {
  display: flex;
  align-items: center;
  :deep(.el-dropdown) {
    margin-left: 12px;
  }
}

.pagination-container {
  margin-top: 0;
}
</style>
