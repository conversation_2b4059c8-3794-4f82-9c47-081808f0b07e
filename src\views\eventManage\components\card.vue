<template>
  <div class="card-container flex flex-col justify-between text-[12px]" :class="{active: isActive}" @click="handleClick">
    <div class="model-container">
      <img src="@/assets/images/eventCard.svg" alt="" />
      <div class="card-title">
       <div class="title-content text-[#333333]">{{ content.name || "暂无事件名" }}</div>
      </div>
      <el-dropdown
          trigger="click"
          placement="bottom"
          @command="handleCommand"
      >
        <el-icon class="cursor-pointer">
          <MoreFilled/>
        </el-icon>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-if="content.status === '1'" :command="'pause'">暂停事件</el-dropdown-item>
            <el-dropdown-item v-else-if="content.status === '2'" :command="'restart'">重启事件</el-dropdown-item>
            <el-dropdown-item :command="'delete'"
            >删除事件
            </el-dropdown-item
            >
            <el-dropdown-item :command="'edit'">编辑事件</el-dropdown-item>
            <el-dropdown-item v-if="content.status !== '3'" :command="'finish'">结束事件</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="model-container tag-model">
      <el-tag :type="statusClass(content.status)" style="width: 52px; height: 20px" class="text-[12px]">{{getDict(content.status, event_status)}}</el-tag>
      <el-button
          type="primary"
          link
          @click="toSituationAware"
          class="text-[12px]"
          style="color: #0070FF"
      >态势感知</el-button>
    </div>


    <div class="model-container">
      <div class="flex flex-col gap-y-[10px] text-[#333333]">
        <span style="color: #646A73;">创建时间</span>
        <span>{{ content?.createTime || "暂无时间" }}</span>
      </div>
      <div class="flex flex-col gap-y-[10px] text-[#333333]" style="z-index: 10;">
        <span style="color: #646A73;">创建单位</span>
        <span>{{ content?.deptName || "暂无单位" }}</span>
      </div>
    </div>

    <!--  <div class="h-[120px] flex flex-col justify-between">
        <div class="flex flex-col gap-y-[18px]">
          <div class="flex gap-x-[10px]">
            <div>
              <img src="@/assets/images/eventCard.svg" alt="" />
            </div>
            <div class="text-[#333333] card-title">
              {{ content.name || "暂无事件名" }}
            </div>
          </div>
          <el-tag :type="statusClass(content.status)" style="width: 60px">{{getDict(content.status, event_status)}}</el-tag>
        </div>

        <div class="flex flex-col gap-y-[10px] text-[#333333]">
          <span style="color: #646A73;">创建时间</span>
          <span>{{ content?.createTime || "暂无时间" }}</span>
        </div>
      </div>-->
     <!--   <div class="h-[120px] flex flex-col justify-between" style="z-index: 10;">
          <div class="flex flex-col gap-y-[19px]">
            <div class="flex justify-end">
              <el-dropdown
                trigger="click"
                placement="bottom"
                @command="handleCommand"
              >
                <el-icon class="cursor-pointer"><MoreFilled /></el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="content.status === '1'" :command="'pause'">暂停事件</el-dropdown-item>
                    <el-dropdown-item v-else-if="content.status === '2'" :command="'restart'">重启事件</el-dropdown-item>
                    <el-dropdown-item :command="'delete'"
                      >删除事件</el-dropdown-item
                    >
                    <el-dropdown-item :command="'edit'">编辑事件</el-dropdown-item>
                    <el-dropdown-item v-if="content.status !== '3'" :command="'finish'">结束事件</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <el-button
              type="primary"
              link
              @click="toSituationAware"
              class="text-[12px]"
              style="color: #0070FF"
              >态势感知</el-button
            >
          </div>

          <div class="flex flex-col gap-y-[10px] text-[#333333]" style="z-index: 10;">
            <span style="color: #646A73;">创建单位</span>
            <span>{{ content?.deptName || "暂无单位" }}</span>
          </div>
        </div>-->
<!--        <img class="card-back" src="@/assets/images/eventCardBack.svg" alt="" />-->
  </div>
</template>

<script setup>
  import {removeEvent, updateStatus} from "@/api/eventManagement";
  import {useDict, getDict} from "@/utils/dict.js";

  const props = defineProps({
    content: {
      type: Object,
      default: () => {
      },
    },
    isActive: {
      type: Boolean,
      default: false,
    }
  });

  const {proxy} = getCurrentInstance();
  const emit = defineEmits(["refreshList", "handleEdit", "handleClick"]);

  const {event_type, event_status} = useDict("event_type", 'event_status')

  const statusClass = computed(() => (value) => {
    return event_status.value.find(item => item.value === value)?.elTagType || 'primary'
  })

  const confirm = (api, params, txt) => {
    proxy.$modal
        .confirm(`是否确认${txt}该事件?`)
        .then(function () {
          return api(params);
        })
        .then(() => {
          emit("refreshList");
          proxy.$modal.msgSuccess(`${txt}成功`);
        })
        .catch(() => {
        });
  };

  const handlePause = () => {
    confirm(updateStatus, {id: props.content?.id, status: '2'}, "暂停");
  };
  const handleRestart = () => {
    confirm(updateStatus, {id: props.content?.id, status: '1'}, "重启");
  };

  const handleDelete = () => {
    confirm(removeEvent, [props.content?.id], "删除");
  };

  const handleEdit = () => {

    emit("handleEdit", {
      ...props?.content,
      weixinGroup: props.content.weixinGroup ? parseInt(props.content.weixinGroup) : null
    });
  };

  const handleFinish = () => {
    confirm(updateStatus, {id: props.content?.id, status: '3'}, "结束");
  };

  function handleCommand(command) {
    switch (command) {
      case "pause":
        handlePause();
        break;
      case "restart":
        handleRestart();
        break;
      case "delete":
        handleDelete();
        break;
      case "edit":
        handleEdit();
        break;
      case "finish":
        handleFinish();
        break;
      default:
        break;
    }
  }

  const toSituationAware = () => {
    console.log("---->toSituationAware");
    // proxy.$router.push({
    //   path: "/eventManage/screen",
    //   query: {eventId: props.content?.id, eventName: props.content?.name || '暂无事件名'},
    // });
    window.open(`${window.location.href}/screen?eventId=${props.content?.id}&eventName=${props.content?.name || '暂无事件名'}`, "_blank");
  };

  const handleClick = () => {
    emit("handleClick", props.content)
  }
</script>

<style lang="scss" scoped>
  .card-container {
    flex-shrink: 0;
    width: 100%;
    /*height: 150px;*/
    //margin: 2px;
    padding: 14px 15px 16px;
    border: 1px solid #DBE0E6;
    box-shadow: 0 2px 14px 0 rgba(220, 228, 228, 0.68);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    overflow: hidden;
    position: relative;
    z-index: 10;

    &:hover {
      background: #F4F8FF;

      &:before {
        content: '';
        width: 3px;
        height: 100%;
        background-color: #1356F0;
        position: absolute;
        left: 0;
        top: 0;
      }
    }

    &.active {
      background: #F4F8FF;

      &:before {
        content: '';
        width: 3px;
        height: 100%;
        background-color: #1356F0;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }

  .card-back {
    width: 110px;
    height: 110px;
    position: absolute;
    right: -15px;
    bottom: -15px;
    z-index: 1;
  }

  .cursor-pointer {
    transform: rotate(90deg);
  }

  .model-container {
    width: inherit;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .event-card-icon {
      height: 0.96rem;
      width: 0.93rem;
    }

    .card-title {
      flex: 1;
      min-width: 0; //重点样式
      white-space: nowrap;
      margin-left: 0.25rem;

      .title-content {
        overflow-x: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .tag-model {
    margin: 18px 0 26px;
  }

  :deep(.el-button.is-link) {
    padding: 0;
  }
</style>
