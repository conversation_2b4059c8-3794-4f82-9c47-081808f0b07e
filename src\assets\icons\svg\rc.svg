<?xml version="1.0" encoding="UTF-8"?>
<svg width="59px" height="48px" viewBox="0 0 59 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63 (92445) - https://sketch.com -->
    <title>日程</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="27.4679886%" y1="26.9293219%" x2="70.2116379%" y2="71.1889543%" id="linearGradient-1">
            <stop stop-color="#3183FF" offset="0%"></stop>
            <stop stop-color="#2D7BF8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#0773FD" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#006FFE" stop-opacity="0.638139205" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="27.4679886%" y1="26.9293219%" x2="69.7321707%" y2="70.692482%" id="linearGradient-3">
            <stop stop-color="#4A92FF" offset="0%"></stop>
            <stop stop-color="#3180FF" offset="100%"></stop>
        </linearGradient>
        <filter x="-7.0%" y="-7.0%" width="113.9%" height="114.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#E5F0FF" offset="0%"></stop>
            <stop stop-color="#CBDCFF" offset="100%"></stop>
        </linearGradient>
        <path d="M37.6842287,12.7808368 C38.3685262,12.7808368 38.9238981,13.2188533 38.9238981,13.7576963 L38.9238981,16.4543905 C38.9238981,16.9932335 38.3685262,17.43125 37.6842287,17.43125 C36.9999311,17.43125 36.4445592,16.9932335 36.4445592,16.4543905 L36.4445592,13.7576963 C36.4445592,13.2180269 36.9999311,12.7808368 37.6842287,12.7808368 Z M28.6619146,13.7576963 C28.6619146,13.2180269 28.1057163,12.7808368 27.4222452,12.7808368 C26.7379477,12.7808368 26.1825758,13.2188533 26.1825758,13.7576963 L26.1825758,16.4543905 C26.1825758,16.9932335 26.7379477,17.43125 27.4222452,17.43125 C28.1065427,17.43125 28.6619146,16.9932335 28.6619146,16.4543905 L28.6619146,13.7576963 Z M33.8495179,13.7576963 C33.8495179,13.2180269 33.294146,12.7808368 32.6098485,12.7808368 C31.9263774,12.7808368 31.3701791,13.2188533 31.3701791,13.7576963 L31.3701791,16.4543905 C31.3701791,16.9932335 31.9263774,17.43125 32.6098485,17.43125 C33.294146,17.43125 33.8495179,16.9932335 33.8495179,16.4543905 L33.8495179,13.7576963 Z M39.7916667,15.5023244 L41.501584,15.5023244 C42.0685262,15.5023244 42.5272039,15.8990186 42.5272039,16.3874483 L42.5272039,31.7304236 C42.5272039,32.2188533 42.0685262,32.6155475 41.501584,32.6155475 L23.7172865,32.6155475 C23.1503444,32.6155475 22.6916667,32.2188533 22.6916667,31.7304236 L22.6916667,16.3874483 C22.6916667,15.8990186 23.1503444,15.5031508 23.7172865,15.5031508 L25.4280303,15.5031508 L25.4280303,16.3874483 C25.4280303,17.3659607 26.3462121,18.1576963 27.47927,18.1576963 C28.6131543,18.1576963 29.5313361,17.3659607 29.5313361,16.3874483 L29.5313361,15.5031508 L30.5569559,15.5031508 L30.5569559,16.3874483 C30.5569559,17.3659607 31.4759642,18.1576963 32.609022,18.1576963 C33.7420799,18.1576963 34.6610882,17.3659607 34.6610882,16.3874483 L34.6610882,15.5031508 L35.686708,15.5031508 L35.686708,16.3874483 C35.686708,17.3659607 36.6057163,18.1576963 37.7387741,18.1576963 C38.871832,18.1576963 39.7908402,17.3659607 39.7908402,16.3874483 L39.7908402,15.5031508 L39.7916667,15.5023244 Z M27.4834022,30.5370351 C27.955303,30.5370351 28.3387741,30.2014979 28.3387741,29.7866219 L28.3387741,27.7866219 C28.3387741,27.3717459 27.955303,27.0362087 27.4834022,27.0362087 C27.0106749,27.0362087 26.6280303,27.3717459 26.6280303,27.7857955 L26.6280303,29.7857955 C26.6280303,30.2014979 27.0106749,30.5370351 27.4834022,30.5370351 L27.4834022,30.5370351 Z M30.9057163,30.5370351 C31.3784435,30.5370351 31.7610882,30.2014979 31.7610882,29.7866219 L31.7610882,25.7857955 C31.7610882,25.3709194 31.3784435,25.0353822 30.9057163,25.0353822 C30.432989,25.0353822 30.0503444,25.3709194 30.0503444,25.784969 L30.0503444,29.7866219 C30.0503444,30.2014979 30.432989,30.5370351 30.9057163,30.5370351 L30.9057163,30.5370351 Z M34.3280303,30.5370351 C34.8007576,30.5370351 35.1834022,30.2014979 35.1834022,29.7866219 L35.1834022,23.784969 C35.1834022,23.370093 34.8007576,23.0345558 34.3280303,23.0345558 C33.855303,23.0345558 33.4726584,23.370093 33.4726584,23.784969 L33.4726584,29.7866219 C33.4726584,30.2014979 33.855303,30.5370351 34.3280303,30.5370351 Z M37.7503444,30.5370351 C38.2238981,30.5370351 38.6065427,30.2014979 38.6065427,29.7866219 L38.6065427,21.7833161 C38.6065427,21.370093 38.2230716,21.0337293 37.7503444,21.0337293 C37.27927,21.0337293 36.8949725,21.3692665 36.8949725,21.7833161 L36.8949725,29.7866219 C36.8949725,30.2014979 37.2784435,30.5370351 37.7503444,30.5370351 Z" id="path-6"></path>
        <filter x="-30.2%" y="-30.3%" width="160.5%" height="160.5%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0392156863   0 0 0 0 0.443137255   0 0 0 0 0.984313725  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="统一工作平台" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-250.000000, -63.000000)">
            <g id="日程" transform="translate(246.000000, 60.000000)">
                <path d="M27.8045042,26.7901513 L51.3603894,17.1968494 C52.3833765,16.7802307 53.5504076,17.2717893 53.9670264,18.2947764 C54.1642282,18.7789958 54.1639744,19.3211856 53.9663193,19.8052202 L44.3249541,43.4158214 C43.2051834,46.158012 41.0268349,48.3327282 38.2827794,49.4479211 L14.7737053,59.0020838 C13.7504132,59.417953 12.5837426,58.9255394 12.1678734,57.9022474 C11.9714614,57.4189538 11.9716595,56.8780442 12.1684256,56.3948947 L21.7659059,32.8287496 C22.8834019,30.0847926 25.0605472,27.9076474 27.8045042,26.7901513 Z" id="矩形" fill="url(#linearGradient-1)" opacity="0.657366071" transform="translate(33.071522, 38.095768) rotate(-315.000000) translate(-33.071522, -38.095768) "></path>
                <polygon id="路径-5" fill="url(#linearGradient-2)" points="18.5977437 31.6331242 5 3 60.2873563 3 48.5617695 31.6331242"></polygon>
                <path d="M27.8418605,20.8865956 L51.3977457,11.2932937 C52.4207328,10.8766749 53.5877639,11.3682335 54.0043827,12.3912206 C54.2015845,12.8754401 54.2013307,13.4176298 54.0036757,13.9016644 L44.3623105,37.5122656 C43.2425397,40.2544562 41.0641912,42.4291724 38.3201357,43.5443653 L14.8110616,53.0985281 C13.7877696,53.5143972 12.6210989,53.0219837 12.2052297,51.9986916 C12.0088177,51.5153981 12.0090158,50.9744884 12.2057819,50.4913389 L21.8032622,26.9251938 C22.9207583,24.1812368 25.0979035,22.0040916 27.8418605,20.8865956 Z" id="矩形" fill="url(#linearGradient-3)" opacity="0.674339658" transform="translate(33.108879, 32.192212) rotate(-315.000000) translate(-33.108879, -32.192212) "></path>
                <g id="tracking" transform="translate(22.666667, 12.750000)"></g>
                <path d="M33.2715554,26.2801911 L38.3808209,24.1994064 C39.403808,23.7827876 40.5708392,24.2743462 40.9874579,25.2973333 C41.1846597,25.7815528 41.184406,26.3237425 40.9867509,26.8077771 L38.8897641,31.9430581 C37.7699933,34.6852487 35.5916448,36.8599649 32.8475893,37.9751578 L27.7532737,40.0455039 C26.7299817,40.4613731 25.563311,39.9689595 25.1474418,38.9456675 C24.9510298,38.4623739 24.951228,37.9214643 25.147994,37.4383147 L27.2329572,32.3187894 C28.3504532,29.5748324 30.5275984,27.3976871 33.2715554,26.2801911 Z" id="矩形" fill="#026FFF" opacity="0.767694382" filter="url(#filter-4)" transform="translate(33.071522, 32.118756) rotate(-315.000000) translate(-33.071522, -32.118756) "></path>
                <g id="形状" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                    <use fill="url(#linearGradient-5)" xlink:href="#path-6"></use>
                </g>
            </g>
        </g>
    </g>
</svg>