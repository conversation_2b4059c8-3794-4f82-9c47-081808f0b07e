import request from '@/utils/request'
import portalRequest from "@/utils/portalRequest.js";
// 用户获取消息
export function getMessage() {
    return portalRequest({
        url: '/system/notice/getUserNoticeList',
        method: 'post',
    })
}
// 读消息
export function readMessage(data) {
    return request({
        url: '/system/notice/read',
        method: 'post',
        data
    })
}
// 删除消息
export function deleteMessage(data) {
    return request({
        url: '/system/notice/removeUserNotice',
        method: 'post',
        data
    })
}

// 查看消息详情
export function getMessageDetail(data) {
    return request({
        url: '/system/notice/info',
        method: 'post',
        data
    })
}
