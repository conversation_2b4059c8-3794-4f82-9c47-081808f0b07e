<template>
  <div class="home-header">
    <div class="home-logo">
      <el-image
        style="width: 20px; height: 20px"
        :src="logo"
        :fit="'contain'"
      />
      <div class="home-logo-title">
        <span>{{ title }}</span>
      </div>
    </div>
    <!-- 右侧导航设置 -->
    <div class="right-menu">
      <!-- <el-button @click="toControl" text bg>控制台</el-button> -->

      <div class="avatar-container">
        <el-dropdown
          @command="handleCommand"
          class="right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>
<script setup>
import useUserStore from "@/store/modules/user";
import logo from "@/assets/logo/logo.svg";

const userStore = useUserStore();
const router = useRouter();

const title = ref(import.meta.env.VITE_APP_TITLE);
const toControl = () => {
  router.push(`/index`);
};
function handleCommand(command) {
  switch (command) {
    case "logout":
      logout();
      break;
    default:
      break;
  }
}
/**
 * 注销
 */
function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = "/patrol-opinion/index";
      });
    })
    .catch(() => {});
}
</script>
<style lang="scss" scoped>
.home-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  height: 50px;
  box-shadow: 0 0 1px #0003;
  background: #0f285f8e;
  .home-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: "YouSheBiaoTiHei";
    font-size: 18px;
    margin-left: 10px;
  }
  .right-menu {
    height: 100%;
    display: flex;
    align-items: center;
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 10px;

      .avatar-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        gap: 5px;
        span {
          font-size: 13px;
        }
        .user-avatar {
          cursor: pointer;
          width: 30px;
          height: 30px;
          border-radius: 20px;
          border: 1px solid #ffffff;
          box-shadow: #5a5e66 1px 2px 5px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 12px;
        }
      }
    }
  }
}
</style>
