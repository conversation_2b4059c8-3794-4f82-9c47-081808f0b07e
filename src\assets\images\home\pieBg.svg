<?xml version="1.0" encoding="UTF-8"?>
<svg width="187px" height="186px" viewBox="0 0 187 186" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63 (92445) - https://sketch.com -->
    <title>饼图顶部</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="83.5862069" cy="83.5862069" r="83.5862069"></circle>
        <filter x="-9.6%" y="-8.4%" width="119.1%" height="119.1%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.851649454   0 0 0 0 0.895833799   0 0 0 0 0.988620924  0 0 0 0.498661495 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-12.0%" y="-10.8%" width="123.9%" height="123.9%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="10" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="11.2557771%" y1="20.0379852%" x2="82.1404187%" y2="87.8599799%" id="linearGradient-4">
            <stop stop-color="#FDFFFF" offset="0%"></stop>
            <stop stop-color="#F2F5FF" offset="55.3143001%"></stop>
            <stop stop-color="#DBE9FD" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="84.0862069" cy="83.0862069" rx="70.5" ry="70"></ellipse>
        <filter x="-5.0%" y="-3.6%" width="109.9%" height="110.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.851649454   0 0 0 0 0.895833799   0 0 0 0 0.988620924  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-9.9%" y="-8.6%" width="119.9%" height="120.0%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="10" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="智巡舆情管理系统" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-1292.000000, -347.000000)">
            <g id="编组-3" transform="translate(1286.000000, 337.000000)">
                <g id="饼图顶部" transform="translate(16.413793, 17.413793)">
                    <g id="椭圆形" opacity="0.0929594494">
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <use fill="#F6FAFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-1"></use>
                    </g>
                    <g id="椭圆形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use fill="url(#linearGradient-4)" fill-rule="evenodd" xlink:href="#path-5"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-5"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>