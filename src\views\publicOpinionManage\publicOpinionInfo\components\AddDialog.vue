<template>
  <el-dialog
    class="dialog-container"
    :model-value="modelValue"
    width="1200px"
    @close="onCancle"
  >
    <template #header>
      <span class="addTitle">舆情报送</span>
    </template>

    <el-form
      ref="addFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
    >
      <el-form-item label="舆情编号" prop="poId">
        <el-input
          v-model="formData.poId"
          disabled
          placeholder="请输入舆情编号"
        />
      </el-form-item>
      <el-form-item label="舆情标题" prop="poName">
        <el-input
          v-model="formData.poName"
          placeholder="请输入舆情标题"
          :maxlength="200"
          show-word-limit
          clearable
        />
      </el-form-item>
      <el-form-item label="舆情内容" prop="poContent">
        <el-input
          v-model="formData.poContent"
          type="textarea"
          placeholder="请输入舆情内容"
          :rows="6"
          :maxlength="5000"
          show-word-limit
          clearable
        />
      </el-form-item>
      <el-form-item label="贴文链接" prop="poLink">
        <el-input
          v-model="formData.poLink"
          placeholder="请输入贴文链接"
          clearable
        />
      </el-form-item>
      <el-form-item label="舆情属地" prop="happenLocation">
        <el-cascader
          v-model="formData.happenLocation"
          :options="pcaTextArr"
          placeholder="请选择舆情属地"
          clearable
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="是否为敏感舆情" prop="isSensitive">
        <el-radio-group v-model="formData.isSensitive">
          <el-radio
            v-for="item in SENSITIVE_STATUS_LIST"
            :key="item.value"
            :value="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item label="舆情类型" prop="poType">
        <EditPoType v-model="formData.poType" class="w-full" />
      </el-form-item>
      <el-form-item label="关联舆情事件" prop="poEvent">
        <el-select
          v-model="formData.poEvent"
          multiple
          placeholder="请选择关联舆情事件"
          clearable
        >
          <el-option
            v-for="item in poManageStore.ingPoEventList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="媒体类型" prop="poMediaType">
        <el-select
          v-model="formData.poMediaType"
          placeholder="请选择媒体类型"
          clearable
        >
          <el-option
            v-for="item in poMediaTypeOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布平台" prop="platformType">
        <EditPlatformType v-model="formData.platformType" class="w-full" />
      </el-form-item>
      <el-form-item label="网民昵称" prop="netizenNickname">
        <el-input
          v-model="formData.netizenNickname"
          placeholder="请输入网民昵称"
          :maxlength="50"
          :show-word-limit="true"
          clearable
        />
      </el-form-item>
      <el-form-item label="发布时间" prop="publicTime">
        <el-date-picker
          v-model="formData.publicTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择发布时间"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="图片" prop="imgList">
        <FileUpload
          v-model="formData.imgList"
          :limit="5"
          :is-show-tip="false"
          :file-type="['jpg', 'png']"
          :file-size="10"
          file-list-type="Array"
        >
          <el-button type="default" class="uploadBtn">
            <img
              src="@/assets/images/poManage/upload.png"
              alt="upload"
              class="w-[12px] h-[12px] mr-[4px]"
            />
            点击上传
          </el-button>
        </FileUpload>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button class="addDialogBtn" @click="onOk">报送</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { pcaTextArr } from "element-china-area-data";
import {
  SENSITIVE_STATUS,
  SENSITIVE_STATUS_LIST,
} from "../../config/constant.js";
import { getPoNum, addPoInfo } from "@/api/poManage/poInfo";
import { usePoManageStore } from "@/store/modules/poManage.js";
import EditPoType from "../../components/EditPoType.vue";
import EditPlatformType from "../../components/EditPlatformType.vue";

const poManageStore = usePoManageStore();
poManageStore.getAllPoEventList();

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  poMediaTypeOption: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["update:modelValue", "refreshData"]);

const { proxy } = getCurrentInstance();

const addFormRef = ref();
const formData = reactive({
  poId: "", // 舆情编号
  poName: "", // 舆情标题
  poContent: "", // 舆情内容
  poLink: "", // 贴文链接
  happenLocation: [], // 舆情属地
  isSensitive: SENSITIVE_STATUS.NO, // 是否为敏感舆情
  poType: "", // 舆情类型
  poEvent: "", // 关联舆情事件
  platformType: "", // 发布平台
  netizenNickname: "", // 网民昵称
  publicTime: "", // 发布时间
  imgList: "", // 图片
  poMediaType: "", // 媒体类型
});
const rules = reactive({
  poId: [{ required: true, trigger: "change", message: "请填写舆情编号" }],
  poName: [{ required: true, trigger: "change", message: "请填写舆情标题" }],
  poContent: [{ required: true, trigger: "change", message: "请填写舆情内容" }],
  publicTime: [{ validator: publicTimeV, trigger: "change" }],
});

// 每次打开弹窗，调接口获取舆情编号
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      const res = await getPoNum();
      if (res.code === 200) {
        formData.poId = res.msg;
      }
    }
  }
);

/**
 * 发布时间校验
 */
function publicTimeV(rule, value, callback) {
  if (new Date(value).getTime() >= new Date().getTime()) {
    callback(new Error("发布时间必须小于当前时间"));
  } else {
    callback();
  }
}

/**
 * 取消
 */
function onCancle() {
  reset();
  emit("update:modelValue", false);
}

function onOk() {
  addFormRef.value.validate(async (val) => {
    if (val) {
      const params = {
        reportNumber: formData.poId,
        title: formData.poName,
        content: formData.poContent,
        linkUrl: formData.poLink,
        involvedArea: formData.happenLocation.join(""),
        sensitiveTag: formData.isSensitive,
        categoryTag: formData.poType,
        eventIds: formData.poEvent,
        platformTag: formData.platformType,
        netizenNickname: formData.netizenNickname,
        publishTime: formData.publicTime,
        photoUrl: formData.imgList,
        mediaType: formData.poMediaType,
      };
      const res = await addPoInfo(params);
      if (res.code === 200) {
        proxy.$message.success("报送成功");
        emit("refreshData");
        onCancle();
      }
    }
  });
}

/**
 * 重置数据
 */
function reset() {
  addFormRef.value.resetFields(); // 重置表单
}
</script>

<style lang="scss" scoped>
.dialog-container {
  .addTitle {
    font-size: 16px;
    color: #333333;
  }

  .addDialogBtn {
    width: 80px;
    height: 32px;
    line-height: 32px;
    background: #0052d9;
    border-radius: 5px;
    font-size: 13px;
    color: #ffffff;
  }

  .uploadBtn {
    width: 94px;
    height: 30px;
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
    border-radius: 3px;
    border: 1px solid #d8d8d8;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #333333;
  }
}
</style>

<style lang="scss">
.el-dialog__body {
  padding-right: 10px;
}
</style>
