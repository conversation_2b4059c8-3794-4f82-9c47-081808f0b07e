<template>
  <div class="app-container">
    <div class="back-btn" @click="backBtn">
      <img
        src="@/assets/images/back-icon.png"
        alt=""
        style="width: 42px; height: 42px; margin-right: 5px"
      />
      返回
    </div>
    <div class="body-container">
      <section class="model-container">
        <div class="app-title">
          基础信息
          <div class="btn-box">
            <el-button @click="getBasicAndEventList('basic')" icon="Refresh"
              >刷新</el-button
            >
          </div>
        </div>
        <div class="card-container">
          <div class="basic-info-wrapper">
            <div class="form-item">
              <div class="form-item-label">账号名称</div>
              <div class="form-item-value">
                {{ basicInfo.accountName || "-" }}
              </div>
            </div>
            <div class="form-item">
              <div class="form-item-label">账号ID</div>
              <div class="form-item-value">
                {{ basicInfo.accountId || "-" }}
              </div>
            </div>
            <div class="form-item">
              <div class="form-item-label">IP属地</div>
              <div class="form-item-value">
                {{ basicInfo.ipAddress || "-" }}
              </div>
            </div>
            <div class="form-item">
              <div class="form-item-label">最后更新</div>
              <div class="form-item-value">
                {{ basicInfo.lastUpdate || "-" }}
              </div>
            </div>
            <div class="form-item">
              <div class="form-item-label">主页链接地址</div>
              <div class="form-item-value">
                <el-link
                  v-if="basicInfo.homePageLink"
                  class="linkText truncate"
                  :underline="false"
                  :href="basicInfo.homePageLink"
                  target="blank"
                  @click.stop
                  ><div class="flex gap-[10px] items-center">
                    <span class="truncate max-w-[325px]">{{
                      basicInfo.homePageLink
                    }}</span
                    ><img
                      src="@/assets/images/networkuser/water-link.png"
                      alt="link"
                      class="w-[15px] h-[15px]"
                    /></div
                ></el-link>
                <div v-else>-</div>
              </div>
            </div>
            <div class="form-item">
              <div class="form-item-label">粉丝数</div>
              <div class="form-item-value">{{ basicInfo.fanCount || "-" }}</div>
            </div>
            <div class="form-item">
              <div class="form-item-label">发文（作品）数</div>
              <div class="form-item-value">
                {{ basicInfo.postCount || "-" }}
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="model-container">
        <div class="app-title">水军画像</div>
        <div class="card-container mb-[18px]">
          <div class="card-header-info">
            <div class="card-header-left">作者概述</div>
          </div>
          <div class="author-overview-content">
            该账号近 {{ authorOverview.dayNum }} 天共发文
            {{ authorOverview.opinionNum }} 篇，主要参与
            {{ authorOverview.eventNum }} 个事件。该作者的发文 IP 主要集中在
            {{ authorOverview.ipList?.join("、") }}，其中发文已删帖
            {{ authorOverview.delOpinionNum }} 篇，未删帖
            {{ authorOverview.notDelOpinionNum }} 篇。
          </div>
        </div>
        <div class="flex justify-between gap-[20px]">
          <div class="card-container mb-[18px] pr-[8px] flex-1">
            <div class="card-header-info">
              <div class="card-header-left">发文词云</div>
            </div>
            <div class="word-cloud-wrapper relative">
              <template v-if="wordCloudData?.length > 0">
                <WordCloud :render-data="wordCloudData" />
              </template>
              <template v-else>
                <img
                  src="@/assets/images/empty-img.png"
                  alt=""
                  class="w-[150px] h-[150px] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                />
              </template>
            </div>
          </div>
          <div class="card-container mb-[18px] flex-1 relative">
            <div class="card-header-info">
              <div class="card-header-left">参与事件</div>
            </div>
            <div v-if="eventList?.length > 0" class="event-list-wrapper">
              <div
                v-for="(item, index) in eventList"
                :key="index"
                class="event-item"
                @click="handleClickEvent(item)"
              >
                <div class="event-item-icon">
                  <img
                    src="@/assets/images/networkuser/water-event-link.png"
                    alt=""
                  />
                </div>

                <div class="event-item-title">
                  <OverflowTooltip :content="item.eventName">
                    <div class="truncate max-w-[328px]">
                      {{ item.eventName }}
                    </div>
                  </OverflowTooltip>
                </div>
              </div>
            </div>
            <img
              v-else
              src="@/assets/images/empty-img.png"
              alt=""
              class="w-[150px] h-[150px] absolute top-3/5 left-1/2 -translate-x-1/2 -translate-y-1/2"
            />
          </div>
        </div>
        <div class="card-container">
          <div class="card-header-info">
            <div class="card-header-left">发文信息</div>
            <div class="card-header-right">
              <el-select
                v-model="postTime"
                clearable
                class="w-[100px]"
                @change="handlePostTimeChange"
              >
                <el-option
                  v-for="item in POST_TIME_LIST"
                  :key="item?.value"
                  :label="item?.label"
                  :value="item?.value"
                />
              </el-select>
            </div>
          </div>
          <div class="post-info-content">
            <div class="post-item-left">
              <span class="font-bold">用户轨迹</span>
              <div
                class="h-[323px] w-[180px] overflow-y-auto pr-[15px] relative"
              >
                <el-timeline v-if="userTrack.length > 0">
                  <el-timeline-item
                    v-for="(item, index) in userTrack"
                    :key="index"
                    :timestamp="item.timestamp"
                  >
                    {{ item.province }}
                  </el-timeline-item>
                </el-timeline>
                <img
                  v-else
                  src="@/assets/images/empty-img.png"
                  alt=""
                  class="w-[150px] h-[150px] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                />
              </div>
            </div>
            <div class="post-item-right">
              <span class="font-bold">历史发文</span>
              <HistoryArticleTable
                ref="HistoryArticleTableRef"
                :table-columns="HISTORY_ARTICLE_TABLE_COLUMNS"
                :interface-info="interfaceInfo"
                :table-max-height="281"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup name="AddNetPerson">
import HistoryArticleTable from "./components/HistoryArticleTable.vue";
import WordCloud from "./components/WordCloud.vue";
import OverflowTooltip from "@/components/OverflowTooltip/index.vue";
import { Search } from "@element-plus/icons-vue";
import { getToken } from "@/utils/auth";
import { deepClone } from "@/utils";
import { getDict } from "@/utils/dict";
import {
  getWaterArmyList,
  getWaterArmyHistoryArticle,
  getUserTrackData,
  getWaterArmyWordCloud,
  getWaterArmyAuthorOverview,
} from "@/api/networkuser/user";
import { POST_TIME_LIST } from "../config/constant";
import { HISTORY_ARTICLE_TABLE_COLUMNS } from "../config/tableColumns";

const { proxy } = getCurrentInstance();
const router = useRouter();

const basicInfo = ref({
  accountName: "",
  accountId: "",
  ipAddress: "",
  lastUpdate: "",
  homePageLink: "",
  fanCount: "",
  postCount: "",
}); // 水军基础信息

const interfaceInfo = computed(() => ({
  api: getWaterArmyHistoryArticle,
  params: {
    waterArmyId: history.state.waterArmyId,
    dayNum: postTime.value,
  },
}));
const postTime = ref("3"); // 发文时间
const userTrack = ref([]); // 用户轨迹
const HistoryArticleTableRef = ref(null); // 历史发文表格ref
const historyArticleTableData = ref([]); // 历史发文表格数据
const eventList = ref([]); // 参与事件列表
const wordCloudData = ref([]); // 发文词云数据
const authorOverview = ref({
  dayNum: 0,
  opinionNum: 0,
  eventNum: 0,
  ipList: [],
  delOpinionNum: 0,
  notDelOpinionNum: "",
}); // 作者概述

/**
 * 获取作者概述
 */
const getAuthorOverview = async () => {
  const res = await getWaterArmyAuthorOverview({
    waterArmyId: history.state.waterArmyId,
  });
  authorOverview.value = res.data;
};

/**
 * 获取发文词云数据
 */
const getWordCloudData = async () => {
  const res = await getWaterArmyWordCloud({
    waterArmyId: history.state.waterArmyId,
  });
  wordCloudData.value = res.data;
};

/**
 * 获取基础信息和参与事件列表
 */
const getBasicAndEventList = async (scope = "all") => {
  proxy.$modal.loading("正在刷新中，请稍候...");
  const res = await getWaterArmyList({
    id: history.state.waterArmyId,
  });
  basicInfo.value = {
    accountName: res.rows?.[0]?.accountName,
    accountId: res.rows?.[0]?.accountId,
    ipAddress: res.rows?.[0]?.ipAddress,
    lastUpdate: res.rows?.[0]?.userUpdateTime,
    homePageLink: res.rows?.[0]?.linkUrl,
    fanCount: res.rows?.[0]?.fans,
    postCount: res.rows?.[0]?.works,
  };
  if (scope !== "basic") {
    eventList.value = res.rows?.[0]?.eventList?.map((item) => ({
      eventName: item.name,
      id: item.id,
    }));
  }
  proxy.$modal.closeLoading();
};

/**
 * 发文时间改变事件
 */
const handlePostTimeChange = async () => {
  const params = {
    waterArmyId: history.state.waterArmyId,
    dayNum: postTime.value,
  };
  await getUserTrackInfo();
  HistoryArticleTableRef.value.getTableData(params);
};

/**
 * 点击事件
 * @param item 事件信息
 */
const handleClickEvent = (item) => {
  router.push({
    path: "/eventManage",
    state: {
      eventId: item.id,
    },
  });
};

/**
 * 获取用户轨迹信息
 */
const getUserTrackInfo = async () => {
  const res = await getUserTrackData({
    waterArmyId: history.state.waterArmyId,
    dayNum: postTime.value,
  });
  userTrack.value = res.data.map((row) => {
    const key = Object.keys(row)[0]; // 获取 key（日期）
    const value = row[key]; // 获取 value（地址）

    return {
      timestamp: key,
      province: value,
    };
  });
};

function backBtn() {
  router.back();
}

onMounted(async () => {
  await getBasicAndEventList();
  await getWordCloudData();
  await getUserTrackInfo();
  await getAuthorOverview();
});
</script>

<style lang="scss" scoped>
.col-bottom {
  margin-bottom: 1.67rem;
}

.card-container {
  width: inherit;
  display: flex;
  /*align-items: center;*/
  border-radius: 9px;
  border: 1px solid #eeeeef;
  padding: 16px;

  display: flex;
  flex-direction: column;

  .basic-info-wrapper {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    row-gap: 20px;

    .form-item {
      display: flex;
      flex-direction: column;
      gap: 20px;

      font-family:
        PingFangSC,
        PingFang SC;
      color: #333333;
      line-height: 20px;

      .form-item-label {
        font-size: 15px;
        font-weight: 600;
      }

      .form-item-value {
        font-size: 15px;

        :deep(.linkText) {
          display: inline;
          // font-family:
          //   PingFangSC,
          //   PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #0070ff;

          &:visited {
            color: #0070ff;
          }
          .el-link__inner {
            display: inline;
          }
        }
        font-weight: 400;
      }
    }
  }

  .card-header-info {
    margin-bottom: 22px;
    width: 100%;
    height: 38px;
    display: flex;
    align-items: start;
    justify-content: space-between;
    position: relative;
    border-bottom: none;
    &::after {
      content: "";
      position: absolute;
      left: -16px;
      right: -16px;
      bottom: 0;
      height: 1px;
      background: #eeeeef;
      width: calc(100% + 32px);
      pointer-events: none;
    }

    .card-header-left {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }

    .card-header-right {
    }
  }

  .author-overview-content {
    width: 100%;
    font-weight: 400;
    font-size: 16px;
    color: #1f2329;
    line-height: 25px;

    white-space: wrap;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 25px;
  }

  .post-info-content {
    width: 100%;
    display: flex;
    gap: 20px;
    justify-content: space-between;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 20px;

    .post-item-left {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .post-item-right {
      width: 85%;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
  }

  .event-list-wrapper {
    width: 100%;
    // height: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
    max-height: 255px;
    overflow-y: auto;
    padding-right: 8px;

    .event-item {
      background: #f4f8ff;
      box-shadow: 0px 2px 14px 0px rgba(220, 228, 228, 0.68);
      border-radius: 3px;
      border: 1px solid #dbe0e6;
      padding: 17px 15px;

      display: grid;
      grid-template-columns: 12px 1fr;
      gap: 6px;
      align-items: center;
      position: relative;

      cursor: pointer;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        width: 3px;
        height: 100%;
        background-color: #1356f0;
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
      }

      .event-item-icon {
        width: 12px;
        height: 12px;
        margin-bottom: 3px;
      }
      .event-item-title {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #1f2329;
        line-height: 17px;
        text-align: left;
        font-style: normal;

        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
      }
    }
  }

  .word-cloud-wrapper {
    height: 250px;
  }
}

.back-btn {
  height: 64px;
  border-bottom: 1px solid #e6e9ed;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 700;
  font-size: 16px;
  color: #1f2329;
  // margin-bottom: 22px;
  cursor: pointer;
  padding: 13px 10px 10px;
}

.app-container {
  position: relative;
  padding: 0;
}

.body-container {
  padding: 0 22px;
  margin-top: 22px;
}

.model-container {
  display: flex;
  flex-direction: column;
  /*align-items: center;*/
  margin-bottom: 22px;

  .app-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding-left: 9px;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 6700;
    font-size: 16px;
    color: #333333;
    line-height: 20px;
    text-align: left;
    font-style: normal;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      width: 3px;
      height: 16px;
      background-color: #1356f0;
      border-radius: 2px;
    }

    .btn-box {
      display: flex;
      align-items: center;
    }
  }
}
</style>
