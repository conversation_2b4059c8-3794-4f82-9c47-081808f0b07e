<template>
  <div class="pieChart-container" ref="pieChartRef"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, onUnmounted, ref } from "vue";

const props = defineProps({
  chartData: {
    type: Array,
    default: () => []
  }
});

const pieChartRef = ref(null);
const myChart = ref(null);

const options = computed(() => {
  return {
    grid: {
      top: 28,
      left: 28,
      right: 28,
      bottom: 28
    },
    series: [
      {
        type: "pie",
        radius: ["60%", "75%"], // 调整这个值来改变图表的大小
        center: ["50%", "50%"], // 调整这个值来改变图表的位置
        minAngle: 5, // 最小角度为 5 度
        label: { show: false },
        emphasis: {
          disabled: true,
          itemStyle: {
            color: "inherit",
            borderRadius: 5
          }
        },
        data: props.chartData.map(i => ({ name: i.name, value: i.value })),
        color: [
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(239, 126, 131, 1)" // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(248, 129, 137, 0)" // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          },
          {
            type: "linear",
            x: 1,
            y: 1,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(85, 146, 255, 1)" // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(182, 182, 182, 0)" // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255, 216, 169, 1)" // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(255, 216, 169, 0)" // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          },
          {
            type: "linear",
            x: 0,
            y: 1,
            x2: 1,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255, 232, 132, 1)" // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(255, 232, 132, 0)" // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        ]
      }
    ]
  };
});

// 重新调整图表大小的方法
function resizeChart() {
  if (myChart.value) {
    myChart.value.resize();
  }
}

watch(
  () => options.value,
  () => {
    if (myChart.value) {
      myChart.value.clear();
      myChart.value.setOption(options.value);
    }
  },
  { deep: true }
);

onMounted(() => {
  myChart.value = echarts.init(pieChartRef.value);
  myChart.value.setOption(options.value);

  // 立即调整一次大小，确保图表正确显示
  resizeChart();

  // 添加窗口大小改变时的事件监听器
  window.addEventListener("resize", () => {
    resizeChart();
  });
});

// 移除事件监听器
onUnmounted(() => {
  window.removeEventListener("resize", resizeChart);
});
</script>

<style scoped lang="scss">
.pieChart-container {
  width: 100%;
  height: 100%;
}
</style>
