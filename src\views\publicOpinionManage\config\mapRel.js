import fbIcon from "@/assets/images/poManage/fb.png";
import zyIcon from "@/assets/images/poManage/zy.png";
import lcIcon from "@/assets/images/poManage/lc.png";
import hbIcon from "@/assets/images/poManage/hb.png";
import czIcon from "@/assets/images/poManage/cz.png";
import { usePoManageStore } from "@/store/modules/poManage.js";
const poManageStore = usePoManageStore();

/**
 * 流转类型
 */
export const TRANSFER_TYPE = {
  VIEW: "转阅",
  CHECK: poManageStore.isWangAn ? "落查" : "LC",
  RESOLVE: poManageStore.isWangAn ? "处置" : "CZ",
  REPORT: poManageStore.isWangAn ? "核报" : "HB",
  SCORE: "赋分",
  DELPO: "删帖",
  INVALID: "无效",
};

/**
 * 舆情流转时传参的映射关系
 */
export const PO_TRANSFER_LIST = [
  {
    fontLabel: TRANSFER_TYPE.VIEW,
    processStepCode: "TO_VIEW", // 舆情流转阶段
    commandsCode: "VIEW_ASSIGN", // 舆情流转命令
    categoriesCode: "VIEW", // 舆情流转命令分类
  },
  {
    fontLabel: TRANSFER_TYPE.CHECK,
    processStepCode: "TO_CHECK",
    commandsCode: "CHECK_ASSIGN",
    categoriesCode: "CHECK",
  },
  {
    fontLabel: TRANSFER_TYPE.RESOLVE,
    processStepCode: "TO_RESOLVE",
    commandsCode: "RESOLVE_ASSIGN",
    categoriesCode: "RESOLVE",
  },
  {
    fontLabel: TRANSFER_TYPE.REPORT,
    processStepCode: "TO_REPORT",
    commandsCode: "REPORT_ASSIGN",
    categoriesCode: "REPORT",
  },
];

/**
 * 流程类型
 */
export const PROCESS_TYPE = {
  NEW: "NEW",
  VIEW: "VIEW",
};

/**
 * 舆情流程的映射关系
 */
export const PO_PROCESS_LIST = [
  {
    icon: fbIcon,
    categoriesCode: "NEW", // 舆情流转命令分类
    fontLabel: "贴文发布", // 前端展示的文本
  },
  {
    icon: zyIcon,
    categoriesCode: "VIEW",
    fontLabel: `舆情${TRANSFER_TYPE.VIEW}`,
  },
  {
    icon: lcIcon,
    categoriesCode: "CHECK",
    fontLabel: `舆情${TRANSFER_TYPE.CHECK}`,
    fontText: TRANSFER_TYPE.CHECK,
    CONTINUE_CODE: "CHECK_FOLLOW", // 进行中
    FINISHED_CODE: "CHECK_DONE", // 已完成
  },
  {
    icon: hbIcon,
    categoriesCode: "REPORT",
    fontLabel: `舆情${TRANSFER_TYPE.REPORT}`,
    fontText: TRANSFER_TYPE.REPORT,
    CONTINUE_CODE: "REPORT_FOLLOW",
    FINISHED_CODE: "REPORT_DONE",
  },
  {
    icon: czIcon,
    categoriesCode: "RESOLVE",
    fontLabel: `舆情${TRANSFER_TYPE.RESOLVE}`,
    fontText: TRANSFER_TYPE.RESOLVE,
    CONTINUE_CODE: "RESOLVE_FOLLOW",
    FINISHED_CODE: "RESOLVE_DONE",
  },
];

// 查看弹窗需要展示的流转类型
export const VIEW_PROCESS_CODE = poManageStore.isWangAn
  ? [
      "NEW",
      "VIEW_ASSIGN",
      "CHECK_ASSIGN",
      "REPORT_ASSIGN",
      "RESOLVE_ASSIGN",
      "CHECK_FOLLOW",
      "REPORT_FOLLOW",
      "RESOLVE_FOLLOW",
      "VIEW_DONE",
      "CHECK_DONE",
      "REPORT_DONE",
      "RESOLVE_DONE",
      "DISPATCH",
    ]
  : ["NEW", "VIEW_ASSIGN", "CHECK_ASSIGN", "REPORT_ASSIGN", "RESOLVE_ASSIGN"];
