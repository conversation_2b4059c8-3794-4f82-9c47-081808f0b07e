import { isArray } from "lodash";

function sortBaseColumns(baseColumns, sortData) {
  // 分离出 type 为 selection 的项
  const selectionColumn = baseColumns.find(
    (column) => column.type === "selection"
  );
  const otherColumns = baseColumns.filter(
    (column) => column.type !== "selection"
  );

  // 创建一个 prop 到 sort 的映射对象
  const sortMap = {};
  sortData.forEach((item) => {
    sortMap[item.prop] = item.sort;
  });

  // 对其他列进行排序
  otherColumns.sort((a, b) => {
    const sortA = sortMap[a.prop] || Infinity;
    const sortB = sortMap[b.prop] || Infinity;
    return sortA - sortB;
  });

  // 如果存在 selection 项，将其放在首位
  if (selectionColumn) {
    return [selectionColumn, ...otherColumns];
  }
  return otherColumns;
}

/**
 * 舆情信息-表格列
 */
export const PUBLIC_OPINION_INFO_COLUMNS = (
  delColumn = [],
  selectable,
  currentSortColumns = []
) => {
  const baseColumns = [
    {
      type: "selection",
      label: "",
      selectable,
    },
    {
      prop: "poId",
      label: "舆情编号",
      minWidth: "110px",
      align: "left",
      slotName: "poId",
      showOverflowTooltip: false,
    },
    {
      prop: "createTime",
      label: "报送时间",
      minWidth: "100px",
      align: "left",
      slotName: "createTime",
    },
    {
      prop: "platformType",
      label: "发布平台",
      align: "left",
      minWidth: "100px",
    },
    {
      prop: "poLink",
      label: "贴文链接",
      minWidth: "220px",
      align: "left",
      slotName: "poLink",
    },
    {
      prop: "netizenNickname",
      label: "网民昵称",
      align: "left",
      minWidth: "140px",
      slotName: "netizenNickname",
    },
    {
      prop: "poContent",
      label: "舆情内容",
      minWidth: "230px",
      align: "left",
      slotName: "poContent",
    },
    {
      prop: "poType",
      label: "舆情类型",
      minWidth: "140px",
    },
    {
      prop: "poImg",
      label: "图片",
      minWidth: "160px",
      slotName: "poImg",
    },
    {
      prop: "workUnit",
      label: "上报单位",
      align: "left",
      minWidth: "150px",
      slotName: "workUnit",
    },
    {
      prop: "happenLocation",
      label: "舆情属地",
      align: "left",
      minWidth: "140px",
    },
    {
      prop: "isFirst",
      label: "是否首报",
      minWidth: "80px",
      slotName: "isFirst",
    },
    {
      prop: "poName",
      label: "舆情标题",
      minWidth: "150px",
      align: "left",
      slotName: "poName",
    },

    {
      prop: "poEvent",
      label: "舆情事件",
      minWidth: "270px",
      align: "left",
      slotName: "poEvent",
      showOverflowTooltip: false,
    },
    {
      prop: "poMediaType",
      label: "媒体类型",
      align: "left",
      minWidth: "140px",
      slotName: "poMediaType",
    },
    {
      prop: "poFrom",
      label: "来源",
      align: "left",
      minWidth: "140px",
    },
    {
      prop: "isSensitive",
      label: "是否为敏感舆情",
      minWidth: "140px",
      slotName: "isSensitive",
    },

    {
      prop: "wechatNickname",
      label: "微信报送昵称",
      align: "left",
      width: "140px",
    },
    {
      prop: "netizenAccount",
      label: "网民账号",
      align: "left",
      minWidth: "140px",
    },
    {
      prop: "publicTime",
      label: "发布时间",
      minWidth: "100px",
      align: "left",
      slotName: "publicTime",
    },
    {
      prop: "articleStatus",
      label: "贴文状态",
      minWidth: "100px",
      slotName: "articleStatus",
    },
    {
      prop: "handleStatus",
      label: "处置状态",
      minWidth: "100px",
      slotName: "handleStatus",
    },
    {
      prop: "targetGroup",
      label: "目标群",
      minWidth: "120px",
    },
    {
      prop: "score",
      label: "分值",
      minWidth: "100px",
      slotName: "score",
    },
    {
      prop: "handleUnit",
      label: "处理单位",
      align: "left",
      minWidth: "100px",
    },
    {
      prop: "handler",
      label: "处理人",
      minWidth: "140px",
    },
    {
      prop: "handleStatus2",
      label: "处理状态",
      minWidth: "100px",
      slotName: "handleStatus2",
    },
  ];

  if (currentSortColumns != []) {
    const sortColumns = sortBaseColumns(baseColumns, currentSortColumns);
    return sortColumns.filter(
      (ele) => !delColumn.includes(ele.prop || ele.type)
    );
  } else {
    return baseColumns.filter(
      (ele) => !delColumn.includes(ele.prop || ele.type)
    );
  }
};

/**
 * 舆情信息批量处理-表格列
 */
export const PUBLIC_OPINION_INFO_HANDLE_COLUMNS = [
  // {
  //   prop: "poId",
  //   label: "舆情编号",
  //   width: "110px",
  //   align: "left",
  //   slotName: "poId",
  // },
  {
    prop: "poContent",
    label: "舆情内容",
    align: "left",
    slotName: "poContent",
  },
  {
    prop: "poType",
    label: "舆情类型",
    minWidth: "140px",
  },
  {
    prop: "platformType",
    label: "发布平台",
    align: "left",
    width: "100px",
  },
  {
    prop: "netizenNickname",
    label: "网民昵称",
    align: "left",
    width: "140px",
    slotName: "netizenNickname",
  },
  // {
  //   prop: "poFrom",
  //   label: "来源",
  //   align: "left",
  //   width: "140px",
  // },
  {
    prop: "publicTime",
    label: "发布时间",
    width: "100px",
    align: "left",
    slotName: "publicTime",
  },
  {
    prop: "happenLocation",
    label: "事发地",
    align: "left",
    minWidth: "140px",
  },
];

/**
 * 舆情流程-表格列
 */
export const PUBLIC_OPINION_PROCESS_COLUMNS = [
  {
    prop: "createTime",
    label: "创建时间",
    width: "160px",
    align: "left",
  },
  {
    prop: "eventName",
    label: "事项名称",
  },
  {
    prop: "handleUnit",
    label: "处理单位",
    align: "left",
  },
  {
    prop: "handler",
    label: "处理人",
    width: "100px",
  },
  {
    prop: "curStatus",
    label: "当前状态",
    width: "100px",
    slotName: "curStatus",
  },
  {
    prop: "deadlineTime",
    label: "截止时间",
    width: "160px",
    align: "left",
  },
  {
    prop: "isOverdue",
    label: "是否逾期",
    width: "100px",
    slotName: "isOverdue",
  },
];

/**
 * 舆情类型-新增-表格列
 */
export const PUBLIC_OPINION_TYPE_ADD_COLUMNS = [
  {
    prop: "poType",
    label: "舆情类型",
    align: "left",
    fixed: true,
    slotName: "poType",
  },
];

/**
 * 平台类型-新增-表格列
 */
export const PLATFORM_TYPE_ADD_COLUMNS = [
  {
    prop: "platformName",
    label: "平台类型",
    align: "left",
    fixed: true,
    slotName: "platformName",
  },
];
