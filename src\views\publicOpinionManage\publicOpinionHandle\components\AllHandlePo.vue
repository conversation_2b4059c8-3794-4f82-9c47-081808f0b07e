<template>
  <div class="w-full">
    <FilterSearch
      class="mb-[10px]"
      :search-condition="searchCondition"
      :msg-from-option="opinion_source_type"
      @search="refreshData"
    />

    <!-- 舆情表格 -->
    <PublicOpinionTable
      ref="publicOpinionTableRef"
      :interface-info="interfaceInfo"
      :table-columns="
        PUBLIC_OPINION_INFO_COLUMNS([
          'selection',
          'handleUnit',
          'handler',
          'handleStatus2',
          'score',
        ])
      "
      :operation-column-width="160"
      :show-mul-select="false"
      @refreshData="refreshData"
    >
      <template #operation="{ row }">
        <el-button style="color: #0070ff" link @click.stop="showDrawer(row)"
          >查看</el-button
        >
        <el-button style="color: #0070ff" link @click.stop="showDialog(row)"
          >处置流程</el-button
        >
      </template>
    </PublicOpinionTable>

    <!-- 查看抽屉 -->
    <EditInfoDrawer
      v-model="showInfoDrawer"
      drawer-type="onlyView"
      :po-info="poInfo"
    />

    <!-- 流程弹窗 -->
    <ProcessDialog v-model="showProcessDialog" :po-id="poId" />
  </div>
</template>

<script setup>
import FilterSearch from "@/views/publicOpinionManage/components/FilterSearch.vue";
import PublicOpinionTable from "@/views/publicOpinionManage/components/PublicOpinionTable.vue";
import EditInfoDrawer from "@/views/publicOpinionManage/components/EditInfoDrawer/index.vue";
import ProcessDialog from "./ProcessDialog.vue";
import { PUBLIC_OPINION_INFO_COLUMNS } from "@/views/publicOpinionManage/config/tableColumns";
import { getPoInfoList } from "@/api/poManage/poInfo";
import { HANDLE_STATUS } from "@/views/publicOpinionManage/config/constant.js";

const { proxy } = getCurrentInstance();
const { opinion_source_type, opinion_type, media_type } = proxy.useDict(
  "opinion_source_type",
  "opinion_type",
  "media_type"
); // 舆情来源 舆情类型 媒体类型

// 搜索条件
const searchCondition = ref({
  poId: "",
  poEvent: "",
  msgFrom: "all",
  targetGroup: "all",
});
const publicOpinionTableRef = ref();
const showInfoDrawer = ref(false);
const poInfo = ref({});
const showProcessDialog = ref(false);
const poId = ref("");

const interfaceInfo = computed(() => ({
  api: getPoInfoList,
  params: { reportProcess: HANDLE_STATUS.FINISH_HANDLE, disabled: "0" }, // 已处置且有效
  getDataMap,
}));

/**
 * 数据整理
 */
function getDataMap(data) {
  return data.map((ele) => {
    return {
      id: ele.id,
      poId: ele.reportNumber,
      isFirst: ele.firstReportTag, // 0否 1是
      poName: ele.title,
      poContent: ele.content,
      poLink: ele.linkUrl,
      poEvent: ele.event?.length ? ele.event : "-",
      workUnit: ele.workUnit || "-", // 上报单位
      poMediaType: media_type.value.find(i => Number(i.value) === ele.mediaType)?.label || "-", // 媒体类型
      poFrom:
        opinion_source_type.value.find((i) => i.value === ele.reportSource)
          ?.label || "-",
      platformType: ele.platformTag || "-",
      happenLocation: ele.involvedArea || "-",
      isSensitive: ele.sensitiveTag + "", // 0否 1是
      poType:
        opinion_type.value.find((i) => i.value === ele.categoryTag)?.label ||
        "-",
      wechatNickname: ele.wechatNickname || "-", // 微信报送昵称
      netizenNickname: ele.netizenNickname || "-", // 网名昵称
      netizenAccountId: ele.netizenAccountId, // 网名id
      netizenAccount: ele.netizenAccount || "-", // 网名账号
      publicTime: ele.publishTime || "-",
      poImg: ele.photoUrl ? ele.photoUrl.split(",") : "-",
      articleStatus: ele.sourceStatus,
      handleStatus: ele.reportProcess,
      createTime: ele.createTime || "-",
      targetGroup: ele.targetGroup || "-",
      isInvalid: ele.disabled,
      linkUrlCount: ele.linkUrlCount,
    };
  });
}

/**
 * 刷新表格数据
 */
function refreshData() {
  const params = {
    disabled: "0",
    reportProcess: HANDLE_STATUS.FINISH_HANDLE,
    reportNumber: searchCondition.value.poId,
    event: searchCondition.value.poEvent,
    reportSource:
      searchCondition.value.msgFrom === "all"
        ? ""
        : searchCondition.value.msgFrom,
    targetGroup:
      searchCondition.value.targetGroup === "all"
        ? ""
        : searchCondition.value.targetGroup,
  };
  publicOpinionTableRef.value.getTableData(params);
}

/**
 * 展示查看抽屉
 */
function showDrawer(row) {
  poInfo.value = row;
  showInfoDrawer.value = true;
}

/**
 * 展示流程弹窗
 */
function showDialog(row) {
  poId.value = row.id;
  showProcessDialog.value = true;
}
</script>

<style lang="scss" scoped>
.publicOpinionTable-container {
  height: calc(100% - 42px);
}
</style>
