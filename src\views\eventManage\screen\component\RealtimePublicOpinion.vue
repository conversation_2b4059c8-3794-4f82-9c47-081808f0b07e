<template>
  <div class="realtime-opinion w-full h-full flex flex-col">
    <item-title class="w-full" :title="title" />
    <div class="info-container h-full w-full" style="overflow: hidden">
      <vue3-seamless-scroll
        :list="realtimePublicOpinionData"
        hover
        class="!h-full"
        :step="0.2"
        :wheel="true"
      >
        <div
          v-for="(item, index) in realtimePublicOpinionData"
          :key="item.id"
          class="info-item"
        >
          <div class="info-item-title">{{ parseTime(item.publishTime) }}</div>
          <div class="line-clamp-2">{{ item.content }}</div>
          <div v-if="item.sensitiveTag" class="info-item-tag">敏感</div>
        </div>
      </vue3-seamless-scroll>
    </div>
  </div>
</template>

<script setup>
import ItemTitle from "@/views/eventManage/screen/component/ItemTitle.vue";
import { parseTime } from "@/utils/ruoyi.js";

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
  realtimePublicOpinionData: {
    type: Object,
    default: () => ({}),
  },
});

const realtimePublicOpinionData = computed(() => {
  return props.realtimePublicOpinionData;
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.realtime-opinion {
  gap: px2vw(21);
}

.info-item {
  margin-bottom: px2vw(12);
  height: px2vw(94);
  padding: px2vw(16);
  background: url(@/assets/screen/info-back.png) no-repeat;
  background-size: 100% 100%;
  color: #a3c1d1;
  font-size: px2vw(13);
  position: relative;
  .info-item-title {
    margin-bottom: px2vw(8);
    font-size: px2vw(14);
    color: #feffff;
  }
  .info-item-tag {
    width: px2vw(62);
    height: px2vw(25);
    line-height: px2vw(25);
    text-align: center;
    font-size: px2vw(15);
    color: #feffff;
    position: absolute;
    right: 0;
    top: 0;
    background: url(@/assets/screen/info-tag.png) no-repeat;
    background-size: 100% 100%;
  }
}
</style>
