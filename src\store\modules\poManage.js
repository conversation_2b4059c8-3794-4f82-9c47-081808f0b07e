import { getAllEventList, getProcessStep, getCommands, getCategories } from "@/api/poManage/poInfo";
import { getTodoPoNum } from "@/api/poManage/poHandle";
import { getConfigKey } from "@/api/system/config";

export const usePoManageStore = defineStore("poManage", () => {
  const allPoEventList = ref([]); // 所有的舆情事件
  const ingPoEventList = computed(() => allPoEventList.value.filter(i => i.status === "1")); // 进行中的舆情事件

  /**
   * 获取所有的舆情事件
   */
  async function getAllPoEventList() {
    const res = await getAllEventList({ pageNum: 1, pageSize: 9999 });
    if (res.code === 200) {
      allPoEventList.value = res.data?.map(e => ({ label: e.name, value: e.id, status: e.status })) || [];
    }
  }

  const myTaskNum = ref(0); // 我的待办任务数量

  /**
   * 获取我的待办任务数量
   */
  async function getMyTaskNum() {
    const res = await getTodoPoNum();
    if (res.code === 200) {
      myTaskNum.value = res.data;
    }
  }

  const poProcessStep = ref([]); // 舆情流转阶段
  const poCommands = ref([]); // 舆情流转命令
  const poCategories = ref([]); // 舆情流转命令分类

  /**
   * 获取舆情流转配置
   */
  async function getPoProcessConfig() {
    const promiseArr = [getProcessStep(), getCommands(), getCategories()];
    const resArr = await Promise.all(promiseArr);
    if (resArr.every(ele => ele.code === 200)) {
      poProcessStep.value = resArr[0].data;
      poCommands.value = resArr[1].data;
      poCategories.value = resArr[2].data;
    }
  }

  const isWangAn = ref(false); // 是否是网安端

  /**
   * 判断是否是网安端
   */
  async function judgeIsWangAn() {
    const res = await getConfigKey("isWangAn");
    if (res.code === 200) {
      isWangAn.value = res.msg === "true";
    }
  }

  return {
    allPoEventList,
    ingPoEventList,
    getAllPoEventList,
    myTaskNum,
    getMyTaskNum,
    poProcessStep,
    poCommands,
    poCategories,
    getPoProcessConfig,
    isWangAn,
    judgeIsWangAn
  };
});
