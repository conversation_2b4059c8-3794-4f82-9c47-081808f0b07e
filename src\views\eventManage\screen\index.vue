<template>
  <div
    id="screen-container"
    class="screen-container w-screen h-screen bg-[#010F24] overflow-hidden"
  >
    <div class="screen-header w-full fixed top-0 flex justify-center z-[99999]">
      <div
        class="font-PangMenZhengDao text-white header-title text-none-select"
      >
        {{ route.query.eventName }}
      </div>
    </div>
    <div class="left-border border-container">
      <div class="border-active left"></div>
    </div>
    <div class="right-border border-container">
      <div class="border-active right"></div>
    </div>

    <div class="screen-main-container w-full h-full text-white flex z-[99]">
      <div class="left-box h-full flex flex-col items-center">
        <div
          class="realtime-public-opinion-box w-full flex flex-col items-center flex-1 h-1/2 min-h-1/3"
        >
          <realtime-public-opinion
            class="h-full w-full"
            title="实时舆情展示"
            :realtime-public-opinion-data="realtimePublicOpinionData"
          />
        </div>
        <div
          class="public-opinion-video-display-box w-full flex flex-col items-center h-1/4"
        >
          <public-opinion-video-display
            class="h-full w-full"
            title="舆情视频展示"
            :publicOpinionVideoDisplay="publicOpinionVideoDisplayData"
          />
        </div>
        <div class="platform-posting-box w-full flex flex-col items-center">
          <DistributionOfPostsPlatform
            title="平台贴文分布"
            :platformListRawData="platformList"
            class="h-full w-full"
          />
        </div>
      </div>
      <div class="center-box flex-1 h-full w-full flex flex-col items-center">
        <div class="summary-of-events-box w-full h-1/3">
          <summary-of-events
            title="事件情况汇总"
            :summaryOfEventsData="summaryOfEventsData"
          />
        </div>
        <div class="public-opinion-heat-trend-box w-full h-1/3">
          <public-opinion-heat-trend
            title="舆情热度趋势"
            :publicOpinionHeatTrendData="publicOpinionHeatTrendData"
          />
        </div>
        <div class="public-opinion-record-display-box w-full flex-1 pb-[1.5vw]">
          <public-opinion-record-display
            class="h-full w-full"
            title="舆情战绩展示"
            :publicOpinionRecordDisplayData="publicOpinionRecordDisplayData"
          />
        </div>
      </div>
      <div class="right-box h-full flex flex-col items-center w-full">
        <div class="description-of-the-event-box h-1/3 !w-full overflow-hidden">
          <description-of-the-event
            class="h-full w-full"
            title="事件说明"
            :descriptionOfTheEventData="descriptionOfTheEventData"
          />
        </div>
        <div
          class="handling-of-sensitive-posts-box w-full h-2/3 overflow-hidden"
        >
          <handling-of-sensitive-posts
            title="敏感贴文处置情况"
            class="h-full w-full"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import RealtimePublicOpinion from "@/views/eventManage/screen/component/RealtimePublicOpinion.vue";
import {
  singleOpinionInfo,
  singleOpinionPlatformNum,
  singleOpinionSensitive,
  singleOpinionShow,
  singleOpinionSimple,
  singleOpinionSum,
  singleOpinionTimeRange,
  singleOpinionVideo,
} from "@/api/eventManagement/index.js";
import { useRoute } from "vue-router";
import PublicOpinionVideoDisplay from "@/views/eventManage/screen/component/PublicOpinionVideoDisplay.vue";
import DistributionOfPostsPlatform from "@/views/eventManage/screen/component/DistributionOfPostsPlatform.vue";
import SummaryOfEvents from "@/views/eventManage/screen/component/SummaryOfEvents.vue";
import PublicOpinionHeatTrend from "@/views/eventManage/screen/component/PublicOpinionHeatTrend.vue";
import * as echarts from "echarts";
import PublicOpinionRecordDisplay from "@/views/eventManage/screen/component/PublicOpinionRecordDisplay.vue";
import DescriptionOfTheEvent from "@/views/eventManage/screen/component/DescriptionOfTheEvent.vue";
import HandlingOfSensitivePosts from "@/views/eventManage/screen/component/HandlingOfSensitivePosts.vue";

const { proxy } = getCurrentInstance();
const route = useRoute();

const realtimePublicOpinionData = ref({});
const publicOpinionVideoDisplayData = ref([]);
const platformList = ref([]);
const summaryOfEventsData = ref({});
const publicOpinionHeatTrendData = ref({});
const publicOpinionRecordDisplayData = ref([]);
const descriptionOfTheEventData = ref({});

async function getRealtimePublicOpinionData() {
  const res = await singleOpinionInfo(route.query.eventId);
  realtimePublicOpinionData.value = res.data;
}

async function getPublicOpinionVideoDisplayData() {
  const res = await singleOpinionVideo(route.query.eventId);
  publicOpinionVideoDisplayData.value = res.data;
}

async function getPlatformNum() {
  const res = await singleOpinionPlatformNum(route.query.eventId);
  platformList.value = res.data;
}

async function getSummaryOfEventsData() {
  const res = await singleOpinionSum(route.query.eventId);
  summaryOfEventsData.value = res.data;
}

async function getPublicOpinionHeatTrendData() {
  const res = await singleOpinionTimeRange(route.query.eventId);
  publicOpinionHeatTrendData.value = res.data;
}

async function getDescriptionOfTheEventData() {
  const res = await singleOpinionSimple(route.query.eventId);
  descriptionOfTheEventData.value = res.data;
}

async function getPublicOpinionRecordDisplayData() {
  const res = await singleOpinionShow(route.query.eventId);
  console.log(res.data);
  publicOpinionRecordDisplayData.value = res.data;
}

async function init() {
  await Promise.all([
    getRealtimePublicOpinionData(),
    getPublicOpinionVideoDisplayData(),
    getPlatformNum(),
    getSummaryOfEventsData(),
    getPublicOpinionHeatTrendData(),
    getPublicOpinionRecordDisplayData(),
    getDescriptionOfTheEventData(),
  ]);
}

onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
@use "sass:math";
@use "@/assets/styles/func.scss" as *;

#screen-container {
  overflow: hidden;
}
.screen-container {
  .screen-header {
    height: px2vw(148);
    background: url("@/assets/screen/back-top.png") no-repeat;
    background-size: 100% 100%;
    padding-top: px2vw(17);

    .header-title {
      font-size: px2vw(37);
    }
  }

  .border-container {
    width: 33%;
    height: calc(100% - px2vw(24));
    margin: px2vw(12);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    z-index: 0;
    &.left-border {
      background-image: url("@/assets/images/left-border.png");
      left: 0;
    }
    &.right-border {
      background-image: url("@/assets/images/right-border.png");
      right: 0;
    }
    .border-active {
      width: px2vw(5);
      height: px2vw(17);
      background-image: url("@/assets/screen/border-active.png");
      position: absolute;
      top: 0;
      bottom: 0;
      margin-top: auto;
      margin-bottom: auto;
      z-index: 20;
      opacity: 1;
      animation: blink 2s infinite;
      &.left {
        left: px2vw(4);
      }
      &.right {
        right: px2vw(4);
      }
    }
  }

  .screen-main-container {
    .left-box {
      width: px2vw(525);
      padding: px2vw(88) 0 px2vw(88) px2vw(52);
      gap: px2vw(26);
    }

    .center-box {
      padding: px2vw(105) px2vw(45) 0 px2vw(45);
      gap: px2vw(5);
    }

    .right-box {
      width: px2vw(525);
      min-width: px2vw(525);
      padding: px2vw(88) px2vw(52) px2vw(48) 0;
      gap: px2vw(26);
    }

    .platform-posting-box {
      height: px2vw(188);
    }

    .public-opinion-heat-trend-box {
      //height: px2vw(268);
    }
  }
}
</style>
