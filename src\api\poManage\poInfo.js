import request from "@/utils/request";

// 查询舆情信息列表
export function getPoInfoList(query) {
  return request({
    url: "/business/opinionReport/list",
    method: "get",
    params: query,
  });
}

// 查询舆情信息列表(带谣言-该接口返回的是负责单位下的所有舆情)
export function getPoInfoListWithRumor(query) {
  return request({
    url: "/business/opinionReport/list/rumor",
    method: "get",
    params: query,
  });
}

// 查询舆情信息列表(带谣言-该接口返回的是所属单位下的所有舆情)
export function getPoInfoListWithRumorAll(query) {
  return request({
    url: "/business/opinionReport/list/rumor/all",
    method: "get",
    params: query,
  });
}

// 获取新增舆情编号
export function getPoNum() {
  return request({
    url: "/business/opinionReport/get/number",
    method: "get",
  });
}

// 查询事件列表(分页)
export function getAllEventList(query) {
  return request({
    url: "/event/manage/list",
    method: "get",
    params: query,
  });
}

// 新增舆情信息
export function addPoInfo(data) {
  return request({
    url: "/business/opinionReport",
    method: "post",
    data,
  });
}

// 修改舆情信息
export function editPoInfo(data) {
  return request({
    url: `/business/opinionReport`,
    method: "put",
    data,
  });
}

// 无效舆情信息
export function disablePoInfo(data) {
  return request({
    url: `/business/opinionReport/disable/`,
    method: "put",
    data,
  });
}

// 新建流转（转阅/CZ/LC/HB）
export function createProcess(data) {
  return request({
    url: `/process/create`,
    method: "post",
    data,
  });
}

// 批量修改事件和舆情属地
export function batchEditEventAndLocation(data) {
  return request({
    url: `/business/opinionReport/editRumorStatusInIds`,
    method: "put",
    data,
  });
}

// 查询舆情流转阶段配置
export function getProcessStep() {
  return request({
    url: "/process/command-config/list-process-step",
    method: "get",
  });
}

// 查询舆情流转命令配置
export function getCommands() {
  return request({
    url: "/process/command-config/list-commands",
    method: "get",
  });
}

// 查询舆情流转命令分类配置
export function getCategories() {
  return request({
    url: "/process/command-config/list-categories",
    method: "get",
  });
}

// 查询流转记录列表
export function getProcessList(query) {
  return request({
    url: "/process/list",
    method: "get",
    params: query,
  });
}

// 查询部门下拉树结构（无权限控制）
export function AllDeptTree() {
  return request({
    url: "/system/user/alldeptTree",
    method: "get",
  });
}

// 获取用户显示字段配置
export function getUserShowFieldConfig() {
  return request({
    url: "/business/opinionReport/userShowFieldConfig",
    method: "get",
  });
}

// 用户显示字段配置
export function changeUserShowFieldConfig(data) {
  return request({
    url: `/business/opinionReport/userShowFieldConfig`,
    method: "post",
    data,
  });
}

// 导出时间范围内的舆情信息列表
export function exportPoInRange(data) {
  return request({
    url: `/business/opinionReport/exportInRange`,
    method: "post",
    data,
  });
}

// 谣言鉴定
export function changeRumorStatus(data) {
  return request({
    url: `/business/opinionReport/editRumorStatus`,
    method: "put",
    data,
  });
}

// 删帖判别
export function changeSourceStatus(data) {
  return request({
    url: `/business/opinionReport/editSourceStatus`,
    method: "put",
    data,
  });
}

// 获取链接报送记录
export function getLinkReportRecords(id) {
  return request({
    url: `/business/opinionReport/linkReportRecord/${id}`,
    method: "get",
  });
}

// 删帖判别
export function batchDelPo(data) {
  return request({
    url: `/business/opinionReport/editSourceStatus`,
    method: "put",
    data,
  });
}

// 获取目标群
export function getTargetGroup() {
  return request({
    url: "/business/opinionReport/getTargetGroup",
    method: "get",
  });
}

// 获取部门下用户列表
export function getDeptUserList(params) {
  return request({
    url: "/system/user/deptChildrenAndUserList",
    method: "get",
    params,
  });
}

// 获取派出所下用户列表
export function getPoliceDeptUserList(params) {
  return request({
    url: "/system/user/deptChildrenAndUserListByType",
    method: "get",
    params,
  });
}

// 舆情反馈操作
export function opinionFeedback(params) {
  return request({
    url: "/business/opinionTask/startProcessInstance",
    method: "post",
    data: params,
  });
}

// 反馈审核操作
export function feedbackAudit(params) {
  return request({
    url: "/business/opinionTask/complete",
    method: "post",
    data: params,
  });
}

// 查询指定部门父部门和祖先部门以及人员接口
export function getDeptParentUserList(params) {
  return request({
    url: "/system/user/deptParentAndUserList",
    method: "get",
    params,
  });
}

// 查询平台类型列表
export function getPlatformList() {
  return request({
    url: "/business/platform/list",
    method: "get",
  });
}

// 新增平台类型
export function addPlatform(data) {
  return request({
    url: "/business/platform",
    method: "post",
    data,
  });
}

// 删除平台类型
export function delPlatform(params) {
  return request({
    url: "/business/platform",
    method: "delete",
    params,
  });
}

// 编辑平台类型
export function updatePlatform(params) {
  return request({
    url: "/business/platform",
    method: "put",
    params,
  });
}

// 获取舆情信息草稿信息
export function getPoCache(id) {
  return request({
    url: `/business/opinionCache/${id}`,
    method: "get",
  });
}

// 新增舆情信息草稿
export function addPoCache(data) {
  return request({
    url: "/business/opinionCache",
    method: "post",
    data,
  });
}

// 删除舆情信息草稿
export function delPoCache(id) {
  return request({
    url: `/business/opinionCache/${id}`,
    method: "delete",
  });
}

// 删帖判定
export function delPoJudge() {
  return request({
    url: `/business/opinionReport/batchDeleteByUrl`,
    method: "get",
  });
}

// 获取舆情处理人列表-反馈审核
export function getHandlerList(params) {
  return request({
    url: `/system/user/deptUserList`,
    method: "get",
    params,
  });
}

// 批量无效操作
export function batchInvalidPo(ids) {
  return request({
    url: `/business/opinionReport/disable/${ids}`,
    method: "put",
  });
}

// 删除操作
export function opinionDelete(id) {
  return request({
    url: `/business/opinionReport/${id}`,
    method: "delete",
  });
}

// 识别网民身份信息
export function identifyNetizenInfo(params) {
  return request({
    url: `/networkuser/manage/smartIdentify`,
    method: "get",
    params,
  });
}

// 标记水军
export function changeWaterArmy(data) {
  // return request({
  //   url: "/waterarmy/add",
  //   method: "post",
  //   data,
  // });
  return {
    code: 200,
  };
}
