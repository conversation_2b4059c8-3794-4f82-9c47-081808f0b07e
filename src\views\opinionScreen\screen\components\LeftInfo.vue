<template>
  <div class="leftInfo-container">
    <div class="po-show-header">
      <div class="show-header-left">
        <div class="header-left-content">
          <img
            class="left-content-icon"
            src="@/assets/screen/show-header-left-icon.png"
            alt=""
          />
          <OverflowTooltip :content="deptName">
            <div class="left-content-text">{{ deptName }}</div>
          </OverflowTooltip>
        </div>
      </div>
      <div class="show-header-right">
        <div class="header-right-label">统计周期：</div>
        <div class="header-right-tab">
          <div
            v-for="(item, index) in timeRangeList"
            :key="index"
            class="right-tab-item"
            :class="{ active: item.active }"
            @click="handleTimeRangeClick(item)"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="screen-title po-show-title"
      :class="{ 'title-opacity': titleOpacity }"
    >
      舆情信息展示
    </div>
    <div class="flex-1" style="overflow: hidden">
      <vue3-seamless-scroll
        :list="infoList"
        hover
        style="height: 100%"
        :step="0.2"
        :wheel="true"
        @count="handleCount"
      >
        <div v-for="(item, index) in infoList" :key="item.id" class="info-item">
          <div class="flex justify-between">
            <div class="info-item-title">
              {{ parseTime(item.publishTime) || "-" }}
            </div>
            <div v-if="item.address" class="info-item-address">
              <img
                class="item-address-icon"
                src="@/assets/screen/info-address-icon.png"
                alt=""
              />
              <OverflowTooltip :content="item.address">
                <div class="item-address-text">
                  {{ item.address }}
                </div>
              </OverflowTooltip>
            </div>
          </div>

          <div class="line-clamp-2">{{ item.content }}</div>
          <!-- <div v-if="item.sensitiveTag" class="info-item-tag">敏感</div> -->
        </div>
      </vue3-seamless-scroll>
    </div>

    <div
      class="screen-title po-type-title"
      :class="{ 'title-opacity': titleOpacity }"
    >
      舆情类型分布
    </div>
    <div class="po-type-wrapper">
      <div
        v-for="(item, index) in typeList"
        :key="index"
        class="type-item"
        :class="{ 'has-border': index !== 3 && index !== 6 }"
      >
        <div class="type-item-title">
          {{ getDict(item.opinionType, opinion_type) }}
        </div>
        <div
          class="type-item-num cursor-pointer"
          @click="goToDetail('category', item.opinionType)"
        >
          {{ item.opinionNum }}
        </div>
      </div>
    </div>

    <div
      class="screen-title platform-title"
      :class="{ 'title-opacity': titleOpacity }"
    >
      平台贴文分布
    </div>
    <div class="platform-wrapper">
      <div class="platform-item-wrapper flex justify-around flex-wrap">
        <div
          v-for="(item, index) in platformList"
          :key="index"
          class="platform-item"
          :class="{ active: activePlatformIndex === index }"
        >
          <div>{{ item.label }}</div>
          <div
            class="platform-item-count cursor-pointer"
            @click="getRealTimePo('platform', item.label)"
          >
            {{ item.value }}
          </div>
        </div>
      </div>
      <div class="platform-logo">
        <img :src="platformList[activePlatformIndex].logo" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { getDict, useDict } from "@/utils/dict.js";
import {
  eventOpinionGetInfo,
  eventOpinionTypeNum,
  eventOpinionPlatformNum,
  getOpinionReportByCategory,
} from "@/api/eventOpinion/index";
import OverflowTooltip from "@/components/OverflowTooltip/index.vue";
import weibo from "@/assets/screen/weibo.svg";
import zhihu from "@/assets/screen/zhihu.svg";
import weixin from "@/assets/screen/weixin.svg";
import douyin from "@/assets/screen/douyin.svg";
import qita from "@/assets/screen/qita.png";
import useUserStore from "@/store/modules/user";
import { useRouter } from "vue-router";

defineProps({
  titleOpacity: {
    type: Boolean,
    default: true,
  },
});

const { opinion_type } = useDict("opinion_type");
const router = useRouter();
const { deptName } = useUserStore();

const infoList = ref([]);
const typeList = ref([]);
const platformList = ref([
  { label: "微博", value: 0, logo: weibo, key: "weiBoNum" },
  { label: "知乎", value: 0, logo: zhihu, key: "zhiHuNum" },
  { label: "微信", value: 0, logo: weixin, key: "weiXinNum" },
  { label: "抖音", value: 0, logo: douyin, key: "douYinNum" },
  { label: "其他网站", value: 0, logo: qita, key: "otherNum" },
]);

const activePlatformIndex = ref(0);
const platformTimer = ref(null);

const timeRangeList = ref([
  { title: "周", type: "week", active: true },
  { title: "月", type: "month", active: false },
]);

const activeTimeRangeType = ref(timeRangeList.value[0].type);

onMounted(() => {
  platformTimer.value = setInterval(() => {
    if (activePlatformIndex.value === 4) {
      activePlatformIndex.value = 0;
    } else {
      activePlatformIndex.value += 1;
    }
  }, 3000);
});
onBeforeUnmount(() => {
  clearInterval(platformTimer.value);
});

function handleCount(count) {
  // console.log('handleCount',count);
  getInfo(count + 1);
}

function handleTimeRangeClick(item) {
  timeRangeList.value.forEach((item) => {
    item.active = false;
  });
  item.active = true;
  activeTimeRangeType.value = item.type;

  getInfo();
}

function getInfo(pageNum = 1) {
  const params = {
    pageSize: 10,
    pageNum,
    type: activeTimeRangeType.value,
  };
  eventOpinionGetInfo(params).then((res) => {
    infoList.value = res.rows;
  });
}
function getTypeNum() {
  eventOpinionTypeNum().then((res) => {
    typeList.value = res.data;
  });
}
function getPlatformNum() {
  eventOpinionPlatformNum().then((res) => {
    platformList.value.forEach((item) => {
      item.value = res.data[item.key];
    });
  });
}

async function getRealTimePo(key, value) {
  infoList.value = [];
  const params = {};
  if (key === "platform" && value === "其他网站") {
    params.excludePlatform = platformList.value
      .filter((i) => i.label !== "其他网站")
      .map((i) => i.label)
      .join(",");
  } else {
    params[key] = value;
  }
  const res = await getOpinionReportByCategory(params);
  if (res.code === 200) {
    infoList.value = res.data;
  }
}

function goToDetail(type, categoryId) {
  router.push({
    path: "/opinionScreen/detail",
    state: { type, categoryId },
  });
}

getInfo();
getTypeNum();
getPlatformNum();
</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;

.leftInfo-container {
  width: 100%;
  height: 100%;
  padding: px2vw(46) px2vw(45) 0 px2vw(50);

  display: flex;
  flex-direction: column;

  .po-show-header {
    display: flex;
    gap: px2vw(37);
    align-items: center;

    margin-bottom: px2vw(16);
    .show-header-left {
      margin-left: px2vw(20);
      width: px2vw(120);
      height: px2vw(19);
      background: url(@/assets/screen/show-header-left.png) no-repeat;
      background-size: contain;
      background-position: 0 px2vw(8);

      position: relative;

      // display: flex;
      // justify-content: center;

      .header-left-content {
        width: 100%;
        position: absolute;
        // left: px2vw(12);
        bottom: px2vw(2);

        display: flex;
        gap: px2vw(7);
        justify-content: center;
        align-items: center;

        .left-content-icon {
          width: px2vw(12);
          height: px2vw(12);
        }

        .left-content-text {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: px2vw(12);
          color: #feffff;
          line-height: px2vw(17);
          text-align: center;
          font-style: normal;

          max-width: px2vw(84);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .show-header-right {
      display: flex;
      align-items: center;

      .header-right-label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: px2vw(12);
        color: #feffff;
        line-height: px2vw(17);
        text-align: left;
        font-style: normal;
      }

      .header-right-tab {
        display: flex;
        // gap: px2vw(15);
        align-items: center;

        .right-tab-item {
          width: px2vw(67);
          height: px2vw(20);
          text-align: center;
          font-family: PangMenZhengDao;
          font-size: px2vw(16);
          color: #feffff;
          z-index: 20;
          cursor: pointer !important;
          &.active {
            background: url(@/assets/screen/time-range.svg) no-repeat;
            background-size: contain;
            background-position: 0 px2vw(8);
          }
        }
      }
    }
  }

  .screen-title {
    width: 100%;
    line-height: px2vw(18);
    color: #feffff;
    text-align: center;
    font-size: px2vw(19);
    font-family: PangMenZhengDao;
    position: relative;
    &:before {
      content: "";
      width: px2vw(160);
      height: px2vw(18);
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      transition: opacity 1s ease;
      opacity: 1;
    }
    &:after {
      content: "";
      width: px2vw(160);
      height: px2vw(18);
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      right: 0;
      transform: rotate(180deg);
      transition: opacity 1s ease;
      opacity: 1;
    }
    &.title-opacity {
      &:before {
        background: url(@/assets/screen/title-border.png) no-repeat;
        background-size: 100% 100%;
      }
      &:after {
        background: url(@/assets/screen/title-border.png) no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  // 实时舆情展示
  .po-show-title {
    margin-bottom: px2vw(20);
  }
  .info-item {
    margin-bottom: px2vw(12);
    padding: px2vw(16);
    background: url(@/assets/screen/info-back.png) no-repeat;
    background-size: 100% 100%;
    color: #a3c1d1;
    font-size: px2vw(13);
    position: relative;
    .info-item-title {
      margin-bottom: px2vw(8);
      font-size: px2vw(14);
      color: #feffff;
    }

    .info-item-address {
      display: flex;
      align-items: center;
      gap: px2vw(6);
      .item-address-icon {
        width: px2vw(10);
        height: px2vw(11);
      }
      .item-address-text {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: px2vw(12);
        color: #feffff;
        line-height: px2vw(17);
        text-align: left;
        font-style: normal;

        width: px2vw(90);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .info-item-tag {
      width: px2vw(62);
      height: px2vw(25);
      line-height: px2vw(25);
      text-align: center;
      font-size: px2vw(15);
      color: #feffff;
      position: absolute;
      right: 0;
      top: 0;
      background: url(@/assets/screen/info-tag.png) no-repeat;
      background-size: 100% 100%;
    }
  }

  // 舆情类型分布
  .po-type-title {
    margin-top: px2vw(21);
    margin-bottom: px2vw(24);
  }
  .po-type-wrapper {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    align-items: center;

    .type-item {
      height: px2vw(80);
      width: 25%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &.has-border {
        position: relative;
        &:after {
          content: "";
          width: px2vw(2);
          height: 100%;
          background: url(@/assets/screen/type-border.svg) no-repeat;
          background-size: 100% 100%;
          position: absolute;
          right: 0;
        }
      }
      .type-item-title {
        margin-bottom: px2vw(8);
        font-size: px2vw(15);
        color: #feffff;
      }
      .type-item-num {
        font-family: PangMenZhengDao;
        font-size: px2vw(29);
        color: #ffffff;
        text-shadow: 0 0 11px rgba(87, 198, 255, 0.76);
      }
    }
  }

  // 平台贴文分布
  .platform-title {
    margin-top: px2vw(21);
    margin-bottom: px2vw(36);
  }
  .platform-wrapper {
    height: px2vw(180);
    display: flex;
    justify-content: space-between;
    gap: px2vw(10);

    .platform-item-wrapper {
      width: px2vw(330);
      .platform-item {
        width: 33%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: px2vw(15);
        color: #feffff;
        position: relative;

        &:nth-child(4) {
          margin-top: px2vw(10);
          margin-left: px2vw(40);
        }
        &:nth-child(5) {
          margin-top: px2vw(10);
          margin-right: px2vw(40);
        }
        &.active {
          .platform-item-count {
            color: #02e8ff;
          }
          &:after {
            content: "";
            width: px2vw(8);
            height: px2vw(10);
            background: url(@/assets/screen/platform-active.png) no-repeat;
            background-size: 100% 100%;
            position: absolute;
            bottom: 0;
          }
        }
        .platform-item-count {
          margin-top: px2vw(8);
          font-size: px2vw(29);
          text-shadow: 0 0 px2vw(11) rgba(87, 198, 255, 0.76);
          font-family: PangMenZhengDao;
        }
      }
    }

    .platform-logo {
      width: px2vw(105);
      height: 100%;
      margin-top: px2vw(4);
      display: flex;
      justify-content: center;
      align-items: center;
      background: url(@/assets/screen/platform-back.svg) no-repeat;
      background-size: 100% 100%;

      img {
        margin-bottom: px2vw(50);
        animation: logo-move 3s infinite ease;
      }
      @keyframes logo-move {
        0% {
          transform: translate(0, px2vw(-10));
        }
        50% {
          transform: translate(0, px2vw(10));
        }
        100% {
          transform: translate(0, px2vw(-10));
        }
      }
    }
  }
}
</style>
