<template>
  <div class="summary-of-events w-full h-full flex flex-col">
    <item-title class="w-full" :title="title" />
    <div class="num-box" style="text-align: right">
      <span style="color: #cce3f9">
        波峰值:
        <span style="color: #21e1e7">
          <span class="font-DIN num">{{ maxNum }}条</span></span
        >
      </span>
    </div>
    <div class="w-full charts">
      <ECharts :options="options" />
    </div>
  </div>
</template>

<script setup>
import ItemTitle from "@/views/eventManage/screen/component/ItemTitle.vue";
import ECharts from "@/components/ECharts/index.vue";
import * as echarts from "echarts";

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
  publicOpinionHeatTrendData: {
    type: Object,
    default: () => ({}),
  },
});

const options = ref({});
const maxNum = ref(0);

watch(
  () => props.publicOpinionHeatTrendData,
  (newV) => {
    if (newV?.maxNum) {
      initTimeRangeChart(newV);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

function initTimeRangeChart(res) {
  maxNum.value = res?.maxNum || 0;
  options.value = {
    grid: {
      right: 20,
      top: 10,
      bottom: 30,
    },
    xAxis: {
      type: "category",
      axisTick: {
        show: false,
      },
      axisLabel: {
        margin: 12,
        color: "#C1EAFE",
      },
      data: res.dataInfos?.map((item) => {
        return item.opinionDate;
      }),
      splitLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "rgba(47,56,67,0.3)",
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          type: "dashed",
          color: "rgba(47,56,67,0.3)",
        },
      },
      axisLabel: {
        color: "#61798B",
        fontFamily: "DIN",
        fontSize: 14,
      },
    },
    series: [
      {
        data: res.dataInfos.map((item) => {
          return item.opinionNum;
        }),
        type: "line",
        lineStyle: {
          color: "#21E1E7",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#21E1E7", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#012A32", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        symbol:
          "image://data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDYzICg5MjQ0NSkgLSBodHRwczovL3NrZXRjaC5jb20gLS0+CiAgICA8dGl0bGU+57yW57uEIDc8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0i5aSn5bGPIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0i5p+Q5LqL5Lu25aSn5bGPIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtODg1LjAwMDAwMCwgLTUwMS4wMDAwMDApIiBmaWxsPSIjMjFFMUU3Ij4KICAgICAgICAgICAgPGcgaWQ9Iue8lue7hC03IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg4ODUuMDAwMDAwLCA1MDEuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICA8Y2lyY2xlIGlkPSLmpK3lnIblvaIiIG9wYWNpdHk9IjAuMjA5MDMwODc4IiBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiPjwvY2lyY2xlPgogICAgICAgICAgICAgICAgPGNpcmNsZSBpZD0i5qSt5ZyG5b2iIiBjeD0iMTAiIGN5PSIxMCIgcj0iNCI+PC9jaXJjbGU+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==",
        symbolSize: 20,
      },
    ],
  };
}
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.summary-of-events {
  gap: px2vw(10);
  .num-box {
    font-size: px2vw(12);
  }
  .num {
    font-size: px2vw(15);
  }
  .charts {
    height: 100%;
  }
}
</style>
