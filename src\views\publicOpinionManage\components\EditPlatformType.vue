<template>
  <div>
    <el-select
      v-model="platformTypeF"
      :placeholder="placeholder"
      filterable
      clearable
      ref="platformTypeRef"
    >
      <!-- 后端确保label唯一 -->
      <el-option
        v-for="item in platFormList"
        :key="item.label"
        :label="item.label"
        :value="item.label"
      />
      <template #footer>
        <!-- 添加按钮 -->
        <el-button type="primary" link icon="Plus" @click="addPlatformType"
          >添加平台</el-button
        >
      </template>
    </el-select>

    <!-- 舆情平台/类型新增弹窗 -->
    <PlatFormOrPoTypeDialog
      v-model="PlatFormOrPoTypeVisible"
      add-type="platform"
      @refreshSelectList="getPlatformTypes"
    />
  </div>
</template>

<script setup>
import { getPlatformList } from "@/api/poManage/poInfo";
import PlatFormOrPoTypeDialog from "./PlatFormOrPoTypeDialog.vue";

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  placeholder: {
    type: String,
    default: "请选择发布平台",
  },
});

const emit = defineEmits(["update:modelValue"]);

const platformTypeF = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit("update:modelValue", val);
  },
});

const platformTypeRef = ref(null);
const platFormList = ref([]);
const PlatFormOrPoTypeVisible = ref(false);

/**
 * 获取舆情类型数据
 */
const getPlatformTypes = () => {
  getPlatformList().then((response) => {
    platFormList.value = response.data.map((label) => ({
      value: label,
      label: label,
    }));
  });
};

/**
 * 平台类型添加按钮
 */
function addPlatformType() {
  platformTypeRef.value.blur();
  PlatFormOrPoTypeVisible.value = true;
}

getPlatformTypes();
</script>

<style lang="scss" scoped></style>
