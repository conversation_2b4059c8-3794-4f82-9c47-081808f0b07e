<?xml version="1.0" encoding="UTF-8"?>
<svg width="59px" height="48px" viewBox="0 0 59 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63 (92445) - https://sketch.com -->
    <title>待办</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="27.4679886%" y1="26.9293219%" x2="70.2116379%" y2="71.1889543%" id="linearGradient-1">
            <stop stop-color="#3183FF" offset="0%"></stop>
            <stop stop-color="#2D7BF8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#0773FD" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#006FFE" stop-opacity="0.638139205" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="27.4679886%" y1="26.9293219%" x2="69.7321707%" y2="70.692482%" id="linearGradient-3">
            <stop stop-color="#4A92FF" offset="0%"></stop>
            <stop stop-color="#3180FF" offset="100%"></stop>
        </linearGradient>
        <filter x="-7.0%" y="-7.0%" width="113.9%" height="114.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#E6F1FF" offset="0%"></stop>
            <stop stop-color="#C9DAFF" offset="100%"></stop>
        </linearGradient>
        <path d="M19,0.715682072 L19,0.636513701 C19,0.556553646 18.9208316,0.477385276 18.9208316,0.397425221 C18.9208316,0.397425221 18.9208316,0.31825685 18.8416633,0.31825685 C18.8416633,0.23908848 18.7624949,0.23908848 18.7624949,0.159128425 L18.6833265,0.159128425 C18.6833265,0.159128425 18.6041581,0.159128425 18.6041581,0.0799600545 L18.366653,0.0799600545 C18.289068,-3.45945494e-14 18.2098997,-3.45945494e-14 18.2098997,-3.45945494e-14 L18.0515629,-3.45945494e-14 C17.9723945,-3.45945494e-14 17.9723945,-3.45945494e-14 17.8932262,0.0791683708 L17.8140578,0.0791683708 L0.416016646,8.82410661 C0.099343163,8.98323503 -0.0589935785,9.30149188 0.0201747923,9.61974873 C0.099343163,9.9372139 0.336848275,10.2554707 0.653521758,10.2554707 L5.39808222,11.0503212 L5.55641896,11.0503212 C5.7147557,11.0503212 5.87309244,10.9711528 6.03142918,10.8919845 L12.9111606,5.72387321 L8.08743177,11.368578 C8.0082634,11.4477464 8.0082634,11.5269148 7.92909503,11.6068748 L7.92909503,18.2055585 C7.92909503,18.6029838 8.16660014,18.9212406 8.56165031,19.000409 L8.71998706,19.000409 C9.03666054,19.000409 9.27337397,18.8420722 9.43171071,18.6029838 L11.4085449,15.0253651 L16.1538971,18.8412806 C16.3122338,18.9204489 16.4705706,19.000409 16.6289073,19.000409 C16.7080757,19.000409 16.8656207,19.000409 16.9447891,18.9212406 C17.1822942,18.8420722 17.4197993,18.5238154 17.4197993,18.2847269 L19,0.794850443 L19,0.715682072 Z" id="path-6"></path>
        <filter x="-31.6%" y="-31.6%" width="163.2%" height="163.2%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0392156863   0 0 0 0 0.439215686   0 0 0 0 0.980392157  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="统一工作平台" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-353.000000, -63.000000)">
            <g id="待办" transform="translate(349.000000, 60.000000)">
                <path d="M27.8045042,26.7901513 L51.3603894,17.1968494 C52.3833765,16.7802307 53.5504076,17.2717893 53.9670264,18.2947764 C54.1642282,18.7789958 54.1639744,19.3211856 53.9663193,19.8052202 L44.3249541,43.4158214 C43.2051834,46.158012 41.0268349,48.3327282 38.2827794,49.4479211 L14.7737053,59.0020838 C13.7504132,59.417953 12.5837426,58.9255394 12.1678734,57.9022474 C11.9714614,57.4189538 11.9716595,56.8780442 12.1684256,56.3948947 L21.7659059,32.8287496 C22.8834019,30.0847926 25.0605472,27.9076474 27.8045042,26.7901513 Z" id="矩形" fill="url(#linearGradient-1)" opacity="0.657366071" transform="translate(33.071522, 38.095768) rotate(-315.000000) translate(-33.071522, -38.095768) "></path>
                <polygon id="路径-5" fill="url(#linearGradient-2)" points="18.5977437 31.6331242 5 3 60.2873563 3 48.5617695 31.6331242"></polygon>
                <path d="M27.8418605,20.8865956 L51.3977457,11.2932937 C52.4207328,10.8766749 53.5877639,11.3682335 54.0043827,12.3912206 C54.2015845,12.8754401 54.2013307,13.4176298 54.0036757,13.9016644 L44.3623105,37.5122656 C43.2425397,40.2544562 41.0641912,42.4291724 38.3201357,43.5443653 L14.8110616,53.0985281 C13.7877696,53.5143972 12.6210989,53.0219837 12.2052297,51.9986916 C12.0088177,51.5153981 12.0090158,50.9744884 12.2057819,50.4913389 L21.8032622,26.9251938 C22.9207583,24.1812368 25.0979035,22.0040916 27.8418605,20.8865956 Z" id="矩形" fill="url(#linearGradient-3)" opacity="0.674339658" transform="translate(33.108879, 32.192212) rotate(-315.000000) translate(-33.108879, -32.192212) "></path>
                <path d="M33.2715554,26.2801911 L38.3808209,24.1994064 C39.403808,23.7827876 40.5708392,24.2743462 40.9874579,25.2973333 C41.1846597,25.7815528 41.184406,26.3237425 40.9867509,26.8077771 L38.8897641,31.9430581 C37.7699933,34.6852487 35.5916448,36.8599649 32.8475893,37.9751578 L27.7532737,40.0455039 C26.7299817,40.4613731 25.563311,39.9689595 25.1474418,38.9456675 C24.9510298,38.4623739 24.951228,37.9214643 25.147994,37.4383147 L27.2329572,32.3187894 C28.3504532,29.5748324 30.5275984,27.3976871 33.2715554,26.2801911 Z" id="矩形" fill="#026FFF" opacity="0.767694382" filter="url(#filter-4)" transform="translate(33.071522, 32.118756) rotate(-315.000000) translate(-33.071522, -32.118756) "></path>
                <g id="send" transform="translate(22.666667, 12.750000)" fill-rule="nonzero">
                    <g id="路径">
                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        <use fill="url(#linearGradient-5)" xlink:href="#path-6"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>