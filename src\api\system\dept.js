import request from "@/utils/request";
import portalRequest from "@/utils/portalRequest.js";

// 查询部门列表
export function listDept(query) {
  return portalRequest({
    url: "/system/dept/list",
    method: "get",    
    params: query
  });
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return portalRequest({
    url: "/system/dept/list/exclude/" + deptId,   
    method: "get"
  });
}

// 查询部门详细
export function getDept(deptId) {
  return portalRequest({
    url: "/system/dept/" + deptId,    
    method: "get"
  });
}

// 新增部门
export function addDept(data) {
  return portalRequest({
    url: "/system/dept",
    method: "post",   
    data: data
  });
}

// 修改部门
export function updateDept(data) {
  return portalRequest({
    url: "/system/dept",
    method: "put",    
    data: data
  });
}

// 删除部门
export function delDept(deptId) {
  return portalRequest({
    url: "/system/dept/" + deptId,    
    method: "delete"
  });
}

// 查询部门印章
export function getDeptSeal(deptId) {
  return portalRequest({
    url: "/system/dept/get-dept-seal",
    method: "post",    
    data: { deptId }
  });
}

// 保存部门印章
export function savePostDeptSeal(data) {
  return portalRequest({
    url: "/system/dept/save-dept-seal",
    method: "post",
    data    
  });
}

// 查询部门对应的微信号列表
export function getWeixinByDept(deptId) {
  return request({
    url: `/business/deptWechat/list/${deptId}`,
    method: "get"
  });
}

// 新增部门对应的微信号
export function addDeptWechat(data) {
  return request({
    url: "/business/deptWechat",
    method: "post",
    data
  });
}

// 修改部门对应的微信号
export function editDeptWechat(data) {
  return request({
    url: "/business/deptWechat",
    method: "put",
    data
  });
}

// 删除部门对应的微信号
export function deleteDeptWechat(ids) {
  return request({
    url: `/business/deptWechat/${ids}`,
    method: "delete"
  });
}

// 获取部门列表
export function getDeptListData(data) {
  return portalRequest({
    url: "/event/manage/dept/list",
    method: "get",
  });
}
