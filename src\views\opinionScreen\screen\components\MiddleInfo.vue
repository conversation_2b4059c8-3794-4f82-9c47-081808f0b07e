<template>
  <div class="middleInfo-container">
    <div class="number-wrapper">
      <div>
        <div class="sum-card-title">发现舆情总数</div>
        <div class="number-card" @click="goToDetail('all')">
          <div
            v-for="num in sumInfo.totalNum"
            :key="num"
            :class="{ card: num !== ',' && num !== '.' }"
            class="number-item"
          >
            {{ num }}
          </div>
        </div>
      </div>
      <div>
        <div class="sum-card-title">敏感舆情总数</div>
        <div class="number-card" @click="goToDetail('sensitive')">
          <div
            v-for="num in sumInfo.sensitiveNum"
            :key="num"
            :class="{ card: num !== ',' && num !== '.' }"
            class="number-item"
          >
            {{ num }}
          </div>
        </div>
      </div>
    </div>

    <div class="w-full flex flex-col">
      <div
        class="screen-title large"
        :class="{ 'title-opacity': titleOpacity }"
      >
        七日舆情走势
      </div>
      <!-- <div class="video-wrapper" style="overflow: hidden">
        <vue3-seamless-scroll
          v-if="videoList.length"
          :list="videoList"
          hover
          direction="left"
          style="height: 100%"
          :step="0.2"
        >
          <div class="flex w-full">
            <div
              class="video-item"
              v-for="(item, itemIndex) in videoList"
              :key="item.opinionId"
              @click="viewVideo(item.linkUrl)"
            >
              <el-image :src="getPhoto(item)" class="video-bg">
                <template #error>
                  <div class="image-slot">
                    <img :src="videoDefault" alt="" />
                  </div>
                </template>
              </el-image>
              <el-tooltip :content="item.title" placement="top">
                <div class="title-container ellipsis">{{ item.title }}</div>
              </el-tooltip>
            </div>
          </div>
        </vue3-seamless-scroll>
        <div v-else class="h-full flex items-center justify-center text-white">
          暂无数据
        </div>
      </div> -->
      <div class="po-trend-wrapper">
        <SevenPoTrendLineChart :line-data="lineData" />
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  eventOpinionSum,
  eventOpinionSensitive,
} from "@/api/eventOpinion/index";
import videoDefault from "@/assets/screen/video-default.png";
import { getUserSevenDayOpinionReport } from "@/api/home";
import SevenPoTrendLineChart from "@/views/home/<USER>/SevenPoTrendLineChart.vue";

defineProps({
  titleOpacity: {
    type: Boolean,
    default: true,
  },
});

const router = useRouter();

const lineData = ref([]);

const sumInfo = ref({
  sensitiveNum: "0",
  totalNum: "0",
});
const videoList = ref([]);
const baseApi = import.meta.env.VITE_APP_BASE_API;

function getSum() {
  eventOpinionSum().then((res) => {
    sumInfo.value.sensitiveNum = String(res.data.sensitiveNum).replace(
      /\B(?=(\d{3})+(?!\d))/g,
      ","
    );
    sumInfo.value.totalNum = String(res.data.totalNum).replace(
      /\B(?=(\d{3})+(?!\d))/g,
      ","
    );
  });
}

function getSensitive() {
  eventOpinionSensitive()
    .then((res) => {
      if (res.code === 200) {
        videoList.value = res.data;
        // if (res.data.length) {
        //   let count = Math.round(res.data.length / 4)
        //   for (let i = 0; i < count; i++) {
        //     let resArr = res.data.filter((item, index) => index >= i * 4  && index < i * 4 + 4)
        //     videoList.value.push(resArr)
        //   }
        // }
      }
    })
    .catch((err) => {});
}

function getPhoto(item) {
  return item.photoUrl
    ? `${baseApi}${item.photoUrl.split(",")[0]}`
    : videoDefault;
}

function viewVideo(url) {
  window.open(url, "_blank");
}

function goToDetail(type) {
  router.push({
    name: "opinionScreenDetail",
    state: { type },
  });
}

/**
 * 获取折线图数据
 */
async function getLineOption() {
  const res = await getUserSevenDayOpinionReport();
  if (res.code === 200) {
    lineData.value = res.data.map((i) => [i.date, i.count]);
    // console.log("lineData.value ", lineData.value);
  }
}

getSum();
getSensitive();
getLineOption();
</script>

<style lang="scss" scoped>
@use "@/assets/styles/func.scss" as *;

.middleInfo-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  // 舆情总数
  .number-wrapper {
    display: flex;
    justify-content: space-between;
    padding-left: px2vw(50);
    padding-right: px2vw(50);
    padding-top: px2vw(120);

    .sum-card-title {
      width: px2vw(120);
      margin: 0 auto px2vw(20);
      color: #fff;
      position: relative;
      font-size: px2vw(20);
      font-family: PangMenZhengDao;
      &:before {
        content: "";
        width: px2vw(8);
        height: px2vw(10);
        background: url(@/assets/screen/platform-active.svg) no-repeat;
        background-size: 100% 100%;
        transform: rotate(90deg);
        position: absolute;
        left: px2vw(-12);
        top: px2vw(8);
        z-index: 10;
      }
      &:after {
        content: "";
        width: px2vw(8);
        height: px2vw(10);
        background: url(@/assets/screen/platform-active.svg) no-repeat;
        background-size: 100% 100%;
        transform: rotate(-90deg);
        position: absolute;
        right: px2vw(-12);
        top: px2vw(8);
        z-index: 10;
      }
    }

    .number-card {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;

      .number-item {
        height: px2vw(85);
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 4px;
        font-family: PangMenZhengDao;
        font-size: 30px;
        color: #ffffff;
        text-shadow: 0 0 9px rgba(87, 242, 255, 0.4);
        &.card {
          width: 48px;
          background: url(@/assets/screen/number-back.png) no-repeat;
          background-size: cover;
        }
      }
    }
  }

  // 敏感舆情视频
  .screen-title {
    width: 100%;
    margin: px2vw(40) 0 px2vw(0);
    line-height: px2vw(18);
    color: #feffff;
    text-align: center;
    font-size: px2vw(19);
    font-family: PangMenZhengDao;
    position: relative;
    &:before {
      content: "";
      width: px2vw(160);
      height: px2vw(18);
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      transition: opacity 1s ease;
      opacity: 1;
    }
    &:after {
      content: "";
      width: px2vw(160);
      height: px2vw(18);
      background: url(@/assets/screen/title-border-active.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      right: 0;
      transform: rotate(180deg);
      transition: opacity 1s ease;
      opacity: 1;
    }
    &.large {
      &:before {
        width: px2vw(340);
        background: url(@/assets/screen/title-border-active.png) no-repeat;
        background-size: 100% 100%;
      }
      &:after {
        width: px2vw(340);
        background: url(@/assets/screen/title-border-active.png) no-repeat;
        background-size: 100% 100%;
      }
    }
    &.title-opacity {
      &:before {
        background: url(@/assets/screen/title-border.png) no-repeat;
        background-size: 100% 100%;
      }
      &:after {
        background: url(@/assets/screen/title-border.png) no-repeat;
        background-size: 100% 100%;
      }
    }
  }
  .po-trend-wrapper {
    width: 100%;
    height: px2vw(216);
  }
}
</style>
