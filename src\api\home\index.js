import request from "@/utils/request";

// 创建微信公众号二维码
export function createWeixinQrCode(data) {
  return request({
    url: "/wechat/createQrCode",
    method: "post",
    data,
  });
}

// 检测是否绑定微信
export function judgeBindWechat() {
  return request({
    url: "/wechat/checkBindWechat",
    method: "get",
  });
}

// 解绑微信账号
export function unbindWechat(data) {
  return request({
    url: "/wechat/unbindWechat",
    method: "post",
    data,
  });
}

// 获取对应角色菜单列表
export function getMenuList() {
  return request({
    url: "/system/menu/selectMenuListByPermission",
    method: "get",
  });
}

// 获取舆情统计信息
export function getPoStatisticsInfo() {
  return request({
    url: "/business/opinionReport/statistics",
    method: "get",
  });
}

// 获取用户所在部门七天舆情信息
export function getUserSevenDayOpinionReport() {
  return request({
    url: "/business/opinionReport/getUserSevenDayOpinionReport",
    method: "get",
  });
}
