<!-- 工作台主页面 -->
<template lang="html">
  <div class="home-cotainer">
    <Header></Header>
    <el-row class="home-body">
      <el-col :span="2">
        <SiderNav ref="sideRef" v-model="currentMenu" />
      </el-col>
      <el-col :span="22">
        <transition name="router-fade" mode="out-in">
          <keep-alive :include="inclds">
            <component
              :is="currentComponent"
              v-if="currentComponent?.name"
              :key="currentComponent.name + ' ' + currentComponent.id"
            />
            <div v-else>正在加载...</div>
          </keep-alive>
        </transition>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
import Header from "./components/header.vue";
import SiderNav from "./components/siderBar.vue";
// 组件菜单
import dashboard from "./dashboard/index.vue";
import { menus } from "./components/menuList.js";
const route = useRoute();
const router = useRouter();
const modules = import.meta.glob("./*/index.vue");
const currentMenu = ref({});
const currentComponent = ref(null);
const sideRef = ref(null);
const inclds = ref([]);
const setUrlParameter = (key, value) => {
  const query = { ...route.query };
  query[key] = value;
  router.replace({ query });
};

const loadMenu = (menu) => {
  let link = modules[`./${menu.component}`];
  if (link) {
    currentComponent.value = markRaw(defineAsyncComponent(link));
    currentComponent.value.name =
      currentMenu.value.icon + "" + currentMenu.value.id;
    // 热更新 导致报错
    // 去重
    inclds.value = [...new Set(inclds.value)];
    inclds.value.push(currentComponent.value.name);
  } else {
    console.warn("没有找到组件!");
  }
};
// 观察route query 的tab值
watch(
  () => route.query.tab,
  (n, o) => {
    currentMenu.value = menus.find((item) => item.tab == n);
    if (currentMenu.value) {
      setUrlParameter("tab", currentMenu.value.tab);
      loadMenu(currentMenu.value);
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
// watch(
//   () => currentMenu.value,
//   (n, o) => {
//     let link = modules[`./${n.component}`];
//     if (link) {
//       currentComponent.value = markRaw(defineAsyncComponent(link));
//       currentComponent.value.name = currentMenu.value.icon + '' + currentMenu.value.id;
//       // 热更新 导致报错
//       // 去重
//       inclds.value = [...new Set(inclds.value)]
//       inclds.value.push(currentComponent.value.name)
//     } else {
//       console.warn("没有找到组件!");
//     }
//   }
// );
</script>
<style lang="scss">
.home-cotainer {
  background-color: #050627;
  min-height: 100vh;
  min-width: 1300px;
  color: #fff;

  .home-body {
  }
}
</style>
