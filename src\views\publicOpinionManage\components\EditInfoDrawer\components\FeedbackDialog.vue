<template>
  <div>
    <el-dialog
      v-model="feedbackVisible"
      title="舆情反馈"
      :width="'700'"
      @close="onCancel"
    >
      <el-form
        ref="feedbackFormRef"
        label-position="top"
        :model="feedbackForm"
        :rules="FEEDBACK_RULES()"
        status-icon
        class="custom-form !block"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="原处置单位" prop="originHandleUnit">
              <el-input v-model="feedbackForm.originHandleUnit" disabled />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="新处置单位" prop="newHandleUnit">
              <el-tree-select
                v-model="feedbackForm.newHandleUnit"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择新处置单位"
                check-strictly
              />
            </el-form-item>
          </el-col>

          <el-col :span="24" v-if="feedbackForm.newHandleUnit">
            <el-form-item label="审核人" prop="auditPerson">
              <el-tree-select
                v-if="showAuditPersonTree"
                placeholder="请选择审核人"
                ref="auditPersonTreeRef"
                v-model="feedbackForm.auditPerson"
                lazy
                :load="loadTreeData"
                :props="{
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  isLeaf: 'isLeaf',
                }"
                node-key="value"
                :render-after-expand="false"
                show-checkbox
                searchable
              >
              </el-tree-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onHandle" :loading="submitLoading">
          提交
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { FEEDBACK_RULES } from "@/views/publicOpinionManage/config/formConfig.js";
// import { HANDLE_RESULT_CODE } from "../config/constant.js";
import {
  AllDeptTree,
  opinionFeedback,
  getDeptParentUserList,
} from "@/api/poManage/poInfo.js";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
});

const deptOptions = ref([]);

const showAuditPersonTree = ref(true);

const auditPersonTreeRef = ref(null);

const feedbackVisible = ref(null);

const feedbackFormRef = ref(null);
const feedbackForm = ref({
  originHandleUnit: "",
  newHandleUnit: "",
  // auditUnit: "",
  auditPerson: "",
});

const { proxy } = getCurrentInstance();

const originOpinionId = ref("");
const taskId = ref("");

const emit = defineEmits(["refreshData"]);

const submitLoading = ref(false);

/**
 * 查询部门下拉树结构
 */
async function getDeptTree() {
  const res = await AllDeptTree();
  if (res.code === 200) {
    deptOptions.value = res.data;
  }
}

const onHandle = () => {
  feedbackFormRef.value.validate(async (val) => {
    if (val) {
      submitLoading.value = true;
      const params = {
        opinionId: originOpinionId.value,
        opinionTaskId: taskId.value,
        newDeptId: feedbackForm.value.newHandleUnit,
        auditDeptId: "100", // 定值，后端去处理
        auditorId: feedbackForm.value.auditPerson,
      };
      const res = await opinionFeedback(params);
      if (res.code === 200) {
        proxy.$message.success("处理成功");
        submitLoading.value = false;
        emit("refreshData");
        onCancel();
      }
    }
  });
};

watch(
  () => feedbackForm.value.newHandleUnit,
  async (val) => {
    if (val) {
      showAuditPersonTree.value = false; // 隐藏审核人树结构
      setTimeout(() => {
        showAuditPersonTree.value = true; // 隐藏审核人树结构
      }, 0);

      feedbackForm.auditPerson = ""; // 清空审核人
    }
  }
);

/**
 * 取消
 */
const onCancel = () => {
  feedbackFormRef.value.resetFields(); // 重置表单
  feedbackVisible.value = false;
};

/**
 * 加载部门人员树结构数据
 */
async function loadTreeData(node, resolve) {
  try {
    const parentId =
      JSON.stringify(node.data) !== "[]" ? node.data.value : null; // Root node has deptId as '0'
    const isDept = JSON.stringify(node.data) !== "[]" ? node.data.isDept : true;

    if (feedbackForm.value.newHandleUnit) {
      const response = await getDeptParentUserList({
        deptId: parentId,
        isDept: isDept,
        childrenDeptId: feedbackForm.value.newHandleUnit,
      });

      const { data } = response;

      const nodes = data.map((user) => ({
        value: user.id,
        label: user.name,
        isDept: user.isDept,
        isLeaf: !user.isDept,
        // disabled: user.isDept,
        // deptId: parentId,
        children: [],
      }));

      resolve(nodes);
    }
  } catch (error) {
    console.error("Failed to load data:", error);
    resolve([]);
  }
}

/**
 * 打开弹框
 */
const openDialog = async (detailInfo) => {
  originOpinionId.value = detailInfo?.id;
  feedbackForm.value.originHandleUnit = detailInfo?.deptName;
  taskId.value = detailInfo?.taskId;
  feedbackVisible.value = true;
};

getDeptTree();

defineExpose({
  openDialog,
  feedbackFormRef,
});
</script>

<style lang="scss" scoped>
:deep(.custom-form) {
  .el-form-item--default {
    margin-bottom: 12px;
  }

  .el-form-item__label,
  .el-radio__label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1f2329;
  }
}

:deep(.el-dialog) {
  padding: 24px;
  .el-dialog__header {
    padding-left: 0;
  }
  .el-dialog__title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
  }
}
</style>
