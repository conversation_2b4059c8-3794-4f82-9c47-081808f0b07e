import Cookies from "js-cookie";
import { getConfigKey } from "@/api/system/config";

const TokenKey = "ZX-Token";

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}

export async function toMainSystem(
  toLogin = false,
  params = "",
  isOpen = false
) {
  console.log("isOpen: ", isOpen);
  let payload = "sys.workbench.url";
  let base = "";
  let { msg } = await getConfigKey(payload);
  base = msg;
  console.log("base: ", base);
  let data = `&otherSystem=true`;
  let url = `${base}${toLogin ? `/login?${data}` : ""}${params ? params : ""}`;
  console.log(url);
  if (isOpen) {
    const mainWindow = window.open("", "mainSystemWindow");
    console.log("mainWindow.location.href : ", mainWindow.location.href);

    if (
      mainWindow &&
      !mainWindow.closed &&
      mainWindow.location.href === `${base}`
    ) {
      // 如果窗口已经存在且未关闭，则聚焦
      mainWindow.focus();
    } else {
      // 如果窗口不存在或已关闭，则重新打开父系统首页
      window.open(url, "mainSystemWindow");
    }
    return false;
  }
  location.href = url;
}
