import {throttle} from "lodash";

let isScrolling = false; // 标志位，防止多次滚动

// 缓动函数 (easeOut)
const easeOut = (t) => {
    return t * (2 - t); // 经典的缓出函数
};

const easeInOut = (t) => {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
};

// 处理鼠标滚动事件
export const handleWheel = (event, scrollContainer, step) => {
    event.preventDefault(); // 阻止默认滚动行为

    // 获取滚动容器
    const containerElement = scrollContainer.value;

    // 控制滚动距离
    const scrollAmount = event.deltaY > 0 ? step : -step; // 向下滚动50px，向上滚动-50px

    // 修改容器的 scrollTop 属性
    containerElement.scrollTop += scrollAmount;
};
