<template>
  <div class="poProcess-container">
    <!-- 标题 -->
    <div class="flex justify-start items-start">
      <div class="text-[14px] font-bold mb-[12px]">
        <span class="poProcessTitle">舆情流程</span>
      </div>
      <!-- <el-icon class="cursor-pointer hover:text-#0056F9" @click="handleClose"
        ><Close
      /></el-icon> -->
    </div>

    <!-- 内容 -->
    <el-scrollbar ref="scrollbarRef" class="h-[calc(100%-31px)]">
      <!-- 流程卡片信息 -->
      <div
        class="processItem flex flex-col items-start"
        v-for="(item, index) in processInfo"
        :key="index"
      >
        <section class="handleType w-full flex justify-between items-center">
          <div class="flex items-center">
            <img
              :src="iconTransfer(item.commandCategory)"
              alt="event"
              class="w-[15px] h-[15px] mr-[8px]"
            />
            <span>
              {{
                processTypeTransfer(item.commandCategory, item.command) || "-"
              }}
            </span>
          </div>

          <!-- 流程状态 -->
          <div
            v-if="item?.optStatusTag"
            class="processStatus"
            :style="{
              backgroundColor: processStatusTransfer(item?.optStatusTag)
                ?.bgColor,
              color: processStatusTransfer(item?.optStatusTag)?.color,
            }"
          >
            {{ processStatusTransfer(item?.optStatusTag)?.label }}
          </div>
        </section>

        <section class="mb-[12px]">
          <div class="handleInfoLabel mt-[13px] mb-[6px]">截止时间</div>
          <div class="handleInfoValue">{{ item.optTime || "-" }}</div>
        </section>

        <section class="mb-[12px]">
          <div class="handleInfoLabel mb-[6px]">处理人</div>
          <div class="handleInfoValue">
            {{ item.chargeListStr || "-" }}
          </div>
        </section>

        <section class="mb-[12px]">
          <div class="handleInfoLabel mb-[6px]">处理结果</div>
          <div class="handleInfoValue">
            {{ processResultTransfer(item?.command)?.label || "-" }}
          </div>
        </section>

        <section class="mb-[12px]">
          <div class="handleInfoLabel mb-[6px]">处理措施</div>
          <div class="handleInfoValue">
            {{ processMeasuresTransfer(item?.commandOpt)?.label || "-" }}
          </div>
        </section>

        <section class="mb-[12px]">
          <div class="handleInfoLabel mb-[6px]">处置说明</div>
          <div class="handleInfoValue break-all">
            {{ item?.commandOptDesc || "-" }}
          </div>
        </section>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import {
  PROCESS_STATUS_OPTIONS,
  PROCESS_RESULT_OPTIONS,
} from "@/views/publicOpinionCollaboration/config/index.js";
import {
  getHandleMeasuresOptions,
  getCommandCategoryOptions,
} from "@/api/publicOpinionCollaboration/index.js";
import { HANDLE_RESULT_OPTIONS } from "@/views/publicOpinionCollaboration/config/index.js";
import { PROCESS_TITLE_ICON } from "@/views/publicOpinionCollaboration/config/index.js";

const props = defineProps({
  processInfo: {
    type: Object,
    default: () => {},
    required: true,
  },
});

const emit = defineEmits(["closeDrawer"]);

const scrollbarRef = ref();

const processMeasuresOptions = ref([]); // 流程措施
const commandCategoryOptions = ref([]); // 流程类型

// 字段labe与code/id的转换

// 流程状态
const iconTransfer = (command) => {
  return PROCESS_TITLE_ICON.find((ele) => ele.code === command).icon;
};

// 流程状态
const processStatusTransfer = (status) => {
  return PROCESS_STATUS_OPTIONS.find((ele) => ele.value === status);
};

// 处置结果
const processResultTransfer = (code) => {
  return PROCESS_RESULT_OPTIONS.find((ele) => ele.code === code);
};

// 处置措施
const processMeasuresTransfer = computed(() => (code) => {
  return processMeasuresOptions.value.find((ele) => ele.code === code);
});

// 流程类型
const processTypeTransfer = computed(() => (category, code) => {
  const handleCodes = HANDLE_RESULT_OPTIONS.map((ele) => ele.code);
  const typeName = commandCategoryOptions.value.find(
    (ele) => ele.code === category
  )?.label;
  return handleCodes.includes(code) ? typeName + "反馈" : typeName;
});

/*
 * 下拉框数据
 */
const getMeasuresOptions = async () => {
  const res = await getHandleMeasuresOptions();
  processMeasuresOptions.value = res.data || [];
};
const getCategoryOptions = async () => {
  const res = await getCommandCategoryOptions();
  commandCategoryOptions.value = res.data || [];
};

const handleClose = () => {
  emit("closeDrawer");
};

defineExpose({
  scrollbarRef,
});

getMeasuresOptions();
getCategoryOptions();
</script>

<style lang="scss" scoped>
.poProcess-container {
  width: 100%;
  height: 100%;
  padding: 24px;
  background: #fcfcfd;

  .poProcessTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 12px;
    color: #8592a6;
  }

  .processItem {
    padding: 5px 12px 0;
    font-size: 12px;

    display: flex;
    flex-direction: column;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(220, 228, 228, 0.68);
    border-radius: 6px;
    border: 1px solid #dbe0e6;

    .handleType {
      height: 40px;

      border-bottom: 1px solid #ebedf0;

      span {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 12px;
        color: #1f2329;
      }
    }

    .handleInfoLabel {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #646a73;
    }

    .handleInfoValue {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #1f2329;
    }

    &:not(:last-child) {
      margin-bottom: 16px;
    }
  }

  .processStatus {
    padding: 0 7px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 3px;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
  }
}
</style>
