<template>
  <div class="pieChart-container" ref="pieChartRef"></div>
</template>

<script setup>
import * as echarts from "echarts";
import "echarts-gl";
import { getPie3D } from "@/utils/pie3D.js";

const props = defineProps({
  renderData: {
    type: Object,
    default: () => {},
  },
});

const { proxy } = getCurrentInstance();

const pieChart = ref(null);

const options = computed(() => {
  let option = getPie3D(
    [
      { name: "处置舆情", value: props.renderData.dealNum },
      { name: "敏感舆情", value: props.renderData.sensitiveNum },
      { name: "发现舆情", value: props.renderData.discoveryNum },
    ],
    0.89, // 可做为调整内环大小
  );
  let sum =
    props.renderData.dealNum +
    props.renderData.sensitiveNum +
    props.renderData.discoveryNum;
  option.title.text = `{a|${sum > 0 ? ((props.renderData.dealNum / sum) * 100).toFixed(3) : 0}}{b|%}\n{c|处置舆情}`;

  return option;
});

watch(
  () => options.value,
  () => {
    if (pieChart.value) {
      pieChart.value.clear();
      pieChart.value.setOption(options.value, true);
    }
  },
  { deep: true },
);

onMounted(() => {
  pieChart.value = markRaw(echarts.init(proxy.$refs.pieChartRef));
  pieChart.value.setOption(options.value, true);

  onresize();

  window.addEventListener("resize", onresize);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", onresize);
});

function onresize() {
  if (pieChart.value) {
    pieChart.value.resize();
  }
}
</script>

<style lang="scss" scoped>
.pieChart-container {
  width: 100%;
  height: 100%;
}
</style>
