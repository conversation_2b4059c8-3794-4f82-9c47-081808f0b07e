import portalRequest from "@/utils/portalRequest.js";
// 获取列表
export function getDataList(params) {
  return portalRequest({
    url: `/networkuser/manage/list`,
    method: "get",
    params,
  });
}

//添加网民
export function addNetPerson(data) {
  return portalRequest({
    url: "/networkuser/manage/add",
    method: "post",
    data: data,
  });
}

//编辑网民
export function editNetPerson(data) {
  return portalRequest({
    url: "/networkuser/manage/modify",
    method: "post",
    data: data,
  });
}

//删除网民
export function delNetPerson(data) {
  return portalRequest({
    url: "/networkuser/manage/delete",
    method: "post",
    data: data,
  });
}

//获取网名详情
export function getPersonDetailInfo(id) {
  return portalRequest({
    url: `/networkuser/manage/${id}`,
    method: "get",
  });
}

//获取历史
export function getHistoryData() {
  return portalRequest({
    url: "/networkuser/manage/history",
    method: "get",
  });
}

//搜索
export function getSearchData(keyword) {
  return portalRequest({
    url: `/networkuser/manage/keyword?keyword=${keyword}`,
    method: "get",
  });
}

//获取发帖列表
export function getPostList(body) {
  return portalRequest({
    url: `/networkuser/post/list`,
    method: "post",
    data: body,
  });
}

//获取帖子统计
export function getPostRecord(id) {
  return portalRequest({
    url: `/networkuser/post/count?userId=${id}`,
    method: "get",
  });
}

//删除网名
export function delHisPerson(body) {
  return portalRequest({
    url: `/networkuser/manage/history/remove`,
    method: "post",
    data: body,
  });
}

//解密身份证号
export function decryptIdCard(body) {
  return portalRequest({
    url: `/common/decryptIdCard`,
    method: "post",
    data: body,
  });
}

// 移除水军
export function delWaterArmy(data) {
  return portalRequest({
    url: `/waterarmy/delete`,
    method: "post",
    data,
  });
}

// 获取水军库
export function getWaterArmyList(data) {
  return portalRequest({
    url: `/waterarmy/list`,
    method: "post",
    data,
  });
}

// 获取水军历史文章
export function getWaterArmyHistoryArticle(params) {
  return portalRequest({
    url: "/waterarmy/getWaterArmyOpinion",
    method: "get",
    params,
  });
}

// 获取水军发文信息
export function getUserTrackData(params) {
  return portalRequest({
    url: `/waterarmy/getWaterArmy/usertrack`,
    method: "get",
    params,
  });
}

// 获取水军词云
export function getWaterArmyWordCloud(params) {
  // return portalRequest({
  //   url: `/waterarmy/wordCloud`,
  //   method: "get",
  //   params,
  // });
  return {
    code: 200,
    data: [
      { name: "雨伞", value: 8 },
      { name: "晴天", value: 28 },
      { name: "电话", value: 24 },
      { name: "手机", value: 23 },
      { name: "下雨", value: 22 },
      { name: "暴雨", value: 21 },
      { name: "多云", value: 20 },
      { name: "雨衣", value: 29 },
      { name: "屋檐", value: 28 },
      { name: "大风", value: 27 },
      { name: "台风", value: 26 },
      { name: "下雪", value: 25 },
    ],
  };
}

// 获取水军作者概览
export function getWaterArmyAuthorOverview(params) {
  return portalRequest({
    url: `/waterarmy/getWaterArmy/overview`,
    method: "get",
    params,
  });
}

// 获取媒体库
export function getMediaList(params){
  return portalRequest({
    url: `/media/list`,
    method: "get",
    params,
  });
}

// 删除媒体
export function delMedia(data){
  return portalRequest({
    url: `/media/delete`,
    method: "post",
    data,
  });
}

// 新增媒体
export function addMedia(data){
  return portalRequest({
    url: `/media/add`,
    method: "post",
    data,
  });
}

// 编辑媒体
export function editMedia(data){
  return portalRequest({
    url: `/media/modify`,
    method: "post",
    data,
  });
}

// 媒体导出
export function exportMedia(data){
  return portalRequest({
    url: `/media/export`,
    method: "post",
    data,
  });
}
