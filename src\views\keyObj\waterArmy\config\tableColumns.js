/*
 * @Author: 周杰 <EMAIL>
 * @Date: 2025-07-23 10:54:11
 * @LastEditors: 周杰 <EMAIL>
 * @LastEditTime: 2025-07-24 15:55:52
 * @FilePath: \patrol-intel-web\src\views\keyObj\waterArmy\config\tableColumns.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 疑似水军-表格列
 */
export const WATER_ARMY_TABLE_COLUMNS = [
  {
    prop: "accountName",
    label: "账号名称",
    align: "left",
    minWidth: 100,
  },
  {
    prop: "accountId",
    label: "账号ID",
    align: "left",
    minWidth: 80,
  },
  {
    prop: "platformName",
    label: "所属平台",
    align: "center",
    minWidth: 80,
  },
  {
    prop: "homePageLink",
    label: "主页链接",
    align: "left",
    slotName: "homePageLink",
    minWidth: 120,
  },
  {
    prop: "ipAddress",
    label: "IP归属",
    align: "left",
    minWidth: 120,
  },
  {
    prop: "judgeLabel",
    label: "判断标签",
    align: "left",
    slotName: "judgeLabel",
    minWidth: 100,
  },
  {
    prop: "eventInfoList",
    label: "参与历史事件",
    align: "left",
    slotName: "eventInfoList",
    minWidth: 180,
  },
];

/**
 * 历史发文-表格列
 */
export const HISTORY_ARTICLE_TABLE_COLUMNS = [
  {
    prop: "title",
    label: "标题",
    align: "left",
    slotName: "title",
  },
  {
    prop: "content",
    label: "发帖内容",
    align: "left",
    slotName: "content",
  },
  {
    prop: "photoUrl",
    label: "图片",
    align: "left",
    width: 120,
    slotName: "photoUrl",
  },
  {
    prop: "linkUrl",
    label: "链接",
    align: "left",

    slotName: "linkUrl",
  },
  {
    prop: "publishTime",
    label: "发帖时间",
    align: "left",
  },
];
