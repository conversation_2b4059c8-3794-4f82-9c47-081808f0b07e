import portalRequest from "@/utils/portalRequest.js";

// 获取实时舆情有关信息
export function eventOpinionGetInfo(params) {
  return portalRequest({
    url: `/event/opinion/get/info`,
    method: "get",
    params,
  });
}

// 获取舆情类型分布有关信息
export function eventOpinionTypeNum() {
  return portalRequest({
    url: `/event/opinion/type/num`,
    method: "get",
  });
}

// 获取平台贴文分布有关信息
export function eventOpinionPlatformNum() {
  return portalRequest({
    url: `/event/opinion/platform/num`,
    method: "get",
  });
}

// 获取进行中事件有关信息
export function eventOpinionIn() {
  return portalRequest({
    url: `/event/opinion/in`,
    method: "get",
  });
}

// 获取重点网民发帖有关信息
export function eventOpinionUserPost() {
  return portalRequest({
    url: `/event/opinion/user/post`,
    method: "get",
  });
}

// 获取舆情数据展示 1 周 2 月
export function eventOpinionTimeRange(timeRange) {
  return portalRequest({
    url: `/event/opinion/time/range?timeRange=${timeRange}`,
    method: "get",
  });
}

// 获取舆情数量
export function eventOpinionSum() {
  return portalRequest({
    url: `/event/opinion/get/sum`,
    method: "get",
  });
}

// 获取舆情敏感度视频列表
export function eventOpinionSensitive() {
  return portalRequest({
    url: `/event/opinion/sensitive`,
    method: "get",
  });
}

// 地区统计(仅长沙市)
export function getAreaPo() {
  return portalRequest({
    url: `/business/opinion/statistic/area`,
    method: "get",
  });
}

// 获取分类和平台下的舆情
export function getOpinionReportByCategory(query) {
  return portalRequest({
    url: `/business/opinionReport/getOpinionReportByCategory`,
    method: "get",
    params: query,
  });
}

// 查询不合并LinkUrl的舆情信息列表
export function getScreenOpinionAll(query) {
  return portalRequest({
    url: `/business/opinionReport/list/link/all`,
    method: "get",
    params: query,
  });
}
