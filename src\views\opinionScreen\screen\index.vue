<template>
  <div class="w-full h-full flex relative">
    <LeftInfo :title-opacity="titleOpacity" />
    <div class="map-wrapper absolute">
      <CityMapChart />
    </div>
    <MiddleInfo :title-opacity="titleOpacity" />
    <RightInfo :title-opacity="titleOpacity" />
    <div v-if="isWJLeader" class="right-header-info">
      <div class="header-info-left">
        <UserDropdown :font-style="fontStyle" />
      </div>
      <div
        class="header-info-right"
        @click="router.push('/publicOpinionManage/publicOpinionInfo')"
      >
        进入系统
        <el-icon><ArrowRight /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import LeftInfo from "./components/LeftInfo.vue";
import CityMapChart from "./components/CityMapChart.vue";
import MiddleInfo from "./components/MiddleInfo.vue";
import RightInfo from "./components/RightInfo.vue";
import UserDropdown from "@/layout/components/UserDropdown/index.vue";
import { pxToVw } from "@/utils/index.js";
import { useRouter } from "vue-router";
import useUserStore from "@/store/modules/user";
const router = useRouter();
const titleTimer = ref(null);
const titleOpacity = ref(true);
const userStore = useUserStore();

const { isWJLeader } = userStore;

const fontStyle = ref({
  color: "#FEFFFF",
  fontSize: pxToVw(12),
  lineHeight: pxToVw(17),
  fontFamily: "PingFangSC, PingFang SC",
});

onMounted(() => {
  titleTimer.value = setInterval(() => {
    titleOpacity.value = !titleOpacity.value;
  }, 1000);
});
onBeforeUnmount(() => {
  clearInterval(titleTimer.value);
});
</script>

<style scoped lang="scss">
@use "@/assets/styles/func.scss" as *;

.leftInfo-container {
  width: px2vw(550);
}
.map-wrapper {
  width: px2vw(900);
  height: px2vw(500);
  margin: 0 auto;
  top: 42%;
  left: 50%;
  z-index: 40;
  transform: translate(-50%, -40%);
}
.middleInfo-container {
  width: px2vw(820);
  position: relative;
  z-index: 50; // 置于地图之上
}
.rightInfo-container {
  width: px2vw(550);
}

.right-header-info {
  // color: #fff;
  position: absolute;
  top: px2vw(43);
  right: px2vw(80);

  display: flex;
  gap: px2vw(58);
  align-items: center;

  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: px2vw(12);
  color: #feffff;
  line-height: px2vw(17);
  text-align: left;
  font-style: normal;

  .header-info-left {
  }

  .header-info-right {
    display: flex;
    align-items: center;
    gap: px2vw(2);
    cursor: pointer;
  }
}
</style>
