<template>
  <div :id="`echarts${id || 1}`" ref="echartsRef" class="w-full h-full" />
</template>

<script setup>
import echarts from "./echarts.js";
import lodash from "lodash";
import "echarts/lib/component/graphic";
import { markRaw, nextTick, onBeforeUnmount, onMounted, ref } from "vue";

const props = defineProps({
  id: {
    type: String,
    default: () => "",
    required: false,
  },
  options: {
    type: Object,
    default: () => { },
    required: true,
  },
  renderer: {
    type: String,
    default: "canvas",
    required: false,
  },
  theme: {
    type: String,
    default: () => "",
    required: false,
  },
  resizeFunc: {
    type: Function,
    required: false,
  },
  isWatchOptions: {
    type: Boolean,
    default: () => true,
    required: false,
  },
});

const emits = defineEmits(["resizeEcharts"]);

// 创建echarts的dom对象和实例对象
const echartsRef = ref();
const echartsInstance = ref();

const options = ref({});

const { theme } = toRefs(props);

watch(
  () => props.options,
  async (newV) => {
    if (newV) {
      if (props.isWatchOptions) {
        options.value = newV;
        await init();
      }
    }
  }
);

function initEcharts() {
  if (!echartsRef.value) {
    return;
  }
  return new Promise((resolve, reject) => {
    echartsInstance.value = echarts.getInstanceByDom(echartsRef.value);
    if (!echartsInstance.value) {
      echartsInstance.value = markRaw(
        echarts.init(echartsRef.value, theme.value, {
          renderer: props.renderer,
        })
      );
    }
    resolve();
  });
}

function renderingEcharts() {
  if (echartsInstance.value) {
    // 给echarts赋值
    echartsInstance.value.setOption(options.value, { notMerge: true });
    resizeEcharts();
  }
}

async function init() {
  await initEcharts();
  renderingEcharts();
}

function resizeEcharts() {
  // console.log("resizeEcharts");
  echartsInstance.value.resize({ animation: { duration: 0 } });
  if (props.resizeFunc && typeof props.resizeFunc === "function") {
    props.resizeFunc(echartsInstance);
  }
  initEcharts();
}

const debouncedResizeEcharts = lodash.debounce(resizeEcharts, 0);

onMounted(async () => {
  if (props.options) {
    options.value = props.options;
    await init();
  }
  window.addEventListener("resize", debouncedResizeEcharts);
});

onBeforeUnmount(() => {
  echartsInstance.value?.dispose();
  window.removeEventListener("resize", debouncedResizeEcharts);
});

defineExpose({
  getRef: () => echartsRef.value,
  getInstance: () => echartsInstance.value,
  renderingEcharts,
  initEcharts,
  init,
  resizeEcharts
});
</script>

<style scoped lang="less"></style>
