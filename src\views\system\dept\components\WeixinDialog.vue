<template>
  <div class="dialog-container">
    <el-dialog :model-value="modelValue" width="800px" @close="onCancle">
      <template #header>
        <span class="dialogTitle">绑定微信昵称</span>
      </template>

      <CommonTable
        :columns="ADD_WEIXIN_COLUMNS"
        :data="tableDataF"
        :loading="tableLoading"
        :show-default-btn="false"
        :operation-column="160"
        no-padding
        :max-height="600"
        :highlight-current-row="false"
        :cellStyle="{ 'vertical-align': 'middle' }"
      >
        <template #weixinAccount="{ row }">
          <el-input
            v-if="row.isNew || row.isEdit"
            v-model="row.newWeixinAccount"
            placeholder="请填写微信昵称"
            clearable
          />
          <span v-else>{{ row.weixinAccount }}</span>
        </template>

        <template #operation="{ row }">
          <template v-if="row.isEdit">
            <el-button type="primary" link @click="onEdit(row)">确认</el-button>
            <el-button type="primary" link @click="onCancleEdit(row)">取消</el-button>
          </template>
          <template v-else>
            <template v-if="row.isNew">
              <el-button type="primary" link @click="onConfirm(row)">绑定</el-button>
              <el-button type="primary" link @click="onAdd(row)">添加</el-button>
            </template>
            <el-button v-else type="primary" link @click="row.isEdit = true">编辑</el-button>

            <el-popconfirm title="确定删除吗？" @confirm="onDel(row)">
              <template #reference>
                <el-button type="primary" link :disabled="row.isNew && addTableData.length === 1">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </template>
      </CommonTable>
    </el-dialog>
  </div>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
import { ADD_WEIXIN_COLUMNS } from "../config/tableColumns.js";
import { v4 as uuidv4 } from "uuid";
import { getWeixinByDept, addDeptWechat, editDeptWechat, deleteDeptWechat } from "@/api/system/dept";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  deptId: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(["update:modelValue"]);

const { proxy } = getCurrentInstance();

const tableData = ref([]); // 表格数据
const tableLoading = ref(false); // 表格加载状态
const addTableData = ref([]);

const tableDataF = computed(() => [...tableData.value, ...addTableData.value]);

// 展示弹窗时，获取已绑定的微信昵称。同时新增一行
watch(
  () => props.modelValue,
  val => {
    if (val) {
      getWeixinData();
      onAdd();
    }
  }
);

async function getWeixinData() {
  tableLoading.value = true;
  const res = await getWeixinByDept(props.deptId);
  if (res.code === 200) {
    tableData.value = res.data.map(ele => ({
      id: ele.id,
      weixinAccount: ele.wechatAccount || "", // 微信昵称
      newWeixinAccount: ele.wechatAccount || "",
      updateTime: ele.updateTime || "", // 更新时间
      isNew: false, // 是否是新增数据
      isEdit: false // 是否是编辑状态
    }));
  }
  tableLoading.value = false;
}

/**
 * 绑定(后端校验微信号是否重复)
 */
async function onConfirm({ id, newWeixinAccount }) {
  const res = await addDeptWechat({ deptId: props.deptId, wechatAccount: newWeixinAccount });
  if (res.code === 200) {
    proxy.$message.success("绑定成功");
    getWeixinData();
    addTableData.value = addTableData.value.filter(i => i.id !== id);
    if (!addTableData.value.length) {
      onAdd();
    }
  }
}

/**
 * 添加
 */
function onAdd() {
  addTableData.value.push({
    id: uuidv4(),
    weixinAccount: "",
    newWeixinAccount: "",
    updateTime: " ",
    isNew: true // 是否是新增数据
  });
}

/**
 * 编辑
 */
async function onEdit({ id, newWeixinAccount }) {
  const res = await editDeptWechat({ id, deptId: props.deptId, wechatAccount: newWeixinAccount });
  if (res.code === 200) {
    proxy.$message.success("编辑成功");
    getWeixinData();
  }
}

/**
 * 取消编辑
 */
function onCancleEdit(row) {
  row.isEdit = false;
  row.newWeixinAccount = row.weixinAccount;
}

/**
 * 删除
 */
async function onDel({ id, isNew }) {
  if (isNew) {
    addTableData.value = addTableData.value.filter(i => i.id !== id);
  } else {
    const res = await deleteDeptWechat(id);
    if (res.code === 200) {
      proxy.$message.success("删除成功");
      getWeixinData();
    }
  }
}

function onCancle() {
  reset();
  emit("update:modelValue", false);
}

/**
 * 重置数据
 */
function reset() {
  tableData.value = [];
  tableLoading.value = false;
  addTableData.value = [];
}
</script>

<style lang="scss" scoped>
.dialog-container {
  :deep(.el-dialog) {
    padding: 24px 0;
    .el-dialog__header {
      padding-left: 24px;
      .dialogTitle {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #1f2329;
      }
    }

    .el-dialog__body {
      padding: 0 24px;
    }
  }
}
</style>
