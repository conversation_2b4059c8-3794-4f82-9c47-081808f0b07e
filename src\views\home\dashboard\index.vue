<!-- 工作台组件页面 -->
<template lang="html">
    <div class="grid-container home-app-container">
        <div class="grid-item grid1 app-list">
            <titleBox title="我的收藏">
                <appItem
                    :applists="apps.addAppList"
                    @handleClick="addApp"
                ></appItem>
                <appItem
                    :applists="apps.appsCollect"
                    @handleClick="toApps"
                ></appItem>
            </titleBox>
            <titleBox title="自建应用">
                <appItem
                    :applists="apps.appsMy"
                    @handleClick="toApps"
                ></appItem>
            </titleBox>
            <titleBox title="公文管理">
                <appItem
                    :applists="apps.appsDoc"
                    @handleClick="toApps"
                ></appItem>
            </titleBox>
            <titleBox title="行政办公">
                <appItem
                    :applists="apps.appsHelp"
                    @handleClick="toApps"
                ></appItem>
            </titleBox>
        </div>
        <div
            class="grid-item on-duty"
            style="padding-bottom: 0px"
        >
            <div class="family-Y text-center">今日值班</div>
            <div class="duty-box grid2 grid2-rows-m">
                <div class="grid-box">
                    <div
                        class="dutyer poninter grid2-item"
                        v-for="i in dutyers"
                    >
                        <div class="duty-name">{{ i.name }}</div>
                        <div class="duty-job">{{ i.job }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div
            class="grid-item for-help"
            style="padding-top: 0px"
        >
            <div class="family-Y text-center">帮助文档</div>
            <div class="help-box grid2 grid2-rows-s">
                <div class="grid-box">
                    <div
                        class="doc poninter grid2-item"
                        v-for="i in docs"
                    >
                        <div class="doc-img">
                            <el-image
                                style="width: 20px; height: 20px"
                                :src="i.image"
                                fit="contain"
                            >
                                <template #error>
                                    <el-image
                                        style="width: 20px; height: 20px"
                                        :src="docImg"
                                        fit="contain"
                                    />
                                </template>
                            </el-image>
                        </div>
                        <div class="doc-job">{{ i.doc }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup name="homeDashboard">
    import titleBox from './components/titleBox.vue';
    import appItem from './components/appItem.vue';
    import docImg from '@/assets/home/<USER>';
    const router = useRouter();
    const apps = ref({
        addAppList: [{ img: 'tj.svg', title: '添加' }],
        appsMy: [
            { img: 'yqgl.png', title: '舆情管理', path: '/opinion/opinion-index' },
            { img: 'zllz.png', title: '指令流转', path: '/order/createorder' },
            { img: 'kqgl.png', title: '考勤管理', path: '/clocking/workbench' },
            { img: 'qwgl.png', title: '勤务管理', path: '/dutyon/dutyinfo' },
        ],
        appsDoc: [
            { img: 'sw.png', title: '收文管理', path: '/dispatcher/receive' },
            { img: 'fw.png', title: '发文管理', path: '/dispatcher/send' },
        ],
        appsCollect: [
            { img: 'kqgl.png', title: '系统管理', path: '/system/user' },
            { img: 'kqgl.png', title: '工作流', path: '/flyflow/flow/group' },
        ],
        appsHelp: [],
    });
    // 跳转其他app
    const toApps = (item) => {
        router.push(item.path);
    };
    // 添加app
    const addApp = () => {};
    const dutyers = ref([
        { name: '张三', job: '主任' },
        { name: '李四', job: '带班领导' },
        { name: '李五', job: '技术支持' },
        { name: '李五', job: '技术支持' },
    ]); // 值班人员列表
    const docs = ref([
        { doc: '使用指南', docurl: 'sad/dsa.doc', image: 'as' },
        { doc: '工作手册', docurl: 'sad/dsa.doc', image: 'as' },
        { doc: '保密准则', docurl: 'sad/dsa.doc', image: 'as' },
        { doc: '工作条例', docurl: 'sad/dsa.doc', image: 'as' },
    ]); // 公文列表
</script>
<style lang="scss" scoped>
    ::v-deep .el-avatar {
        --el-avatar-bg-color: transparent;
    }

    .grid-container {
        display: grid;
        grid-template-columns: 7fr 4fr;
        grid-template-rows: repeat(2, 1fr);
        gap: 10px;

        .grid-item {
            padding: 15px 30px 15px 0;
            min-height: 180px;
        }

        .grid1 {
            grid-area: 1 / 1 / 3 / 2;
        }

        //   应用
        .app-list {
        }

        //   值日
        .on-duty {
            height: 100%;
            p {
            }
        }

        //   帮助文档
        .for-help {
        }
    }

    .family-Y {
        font-family: 'YouSheBiaoTiHei';
        font-size: 18px;
        background-image: url('@/assets/home/<USER>');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 49px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    // 两列的grid布局
    .grid2-rows-s {
        grid-template-rows: repeat(auto-fit, 30px);
    }

    .grid2-rows-m {
        grid-template-rows: repeat(auto-fit, 60px);
    }

    .grid2 {
        padding: 10px 20px;
        height: calc(100% - 50px);
        box-sizing: border-box;
        max-height: 340px;
        .grid-box {
            max-height: 100%;
            overflow-y: auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px 20px;
            &::-webkit-scrollbar {
                width: 0px; /* 设置滚动条宽度 */
            }
            &::-webkit-scrollbar-thumb {
                width: 0px;
            }
        }
        background-image: url('@/assets/home/<USER>');
        background-repeat: no-repeat;
        background-size: 103% 100%;
        background-position: -7px 3px;
        // border: 1px solid #0c3b90;
        border-top: transparent;

        .grid2-item {
            height: 50px;
        }
    }

    .duty-box {
        .dutyer {
            padding: 15px 40px 4px 5px;
            line-height: 21px;
            text-align: right;
            background-image: url('@/assets/home/<USER>');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            height: 76px;

            .duty-name {
                color: #ffb685;
            }

            .duty-job {
                color: #fff;
                font-size: 14px;
            }
        }
    }

    .help-box {
        padding: 20px 14px 10px 30px;

        .doc {
            display: flex;
            gap: 10px;
            align-items: center;

            .doc-img {
                width: 35px;
                height: 35px;
                background-image: linear-gradient(
                    180deg,
                    rgba(40, 115, 255, 0) 0%,
                    rgba(40, 115, 255, 0.26) 100%
                );
                border: 1px solid #2651ae;
                border-radius: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
</style>
