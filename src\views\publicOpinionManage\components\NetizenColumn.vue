<template>
  <!-- 网安网端需要跳转到网名库详情，互联网端不需要 -->
  <span v-if="poManageStore.isWangAn && accountId" class="text-[#0052D9]" @click.stop="goNetPersonDetail">
    {{ nickname }}
  </span>
  <span v-else>{{ nickname }}</span>
</template>

<script setup>
import { usePoManageStore } from "@/store/modules/poManage.js";

const props = defineProps({
  // 网名昵称
  nickname: {
    type: String,
    default: ""
  },
  // 网名账号id
  accountId: {
    type: String,
    default: ""
  }
});

const router = useRouter();
const poManageStore = usePoManageStore();

/**
 * 跳转网名库
 */
function goNetPersonDetail() {
  router.push(`/keyObj/add/addNetPerson/${props.accountId}/${true}`);
}
</script>

<style lang="scss" scoped></style>
