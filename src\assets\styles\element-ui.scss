// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  // 居中显示
  margin-top: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .el-dialog__header {
    text-align: left;
    padding: 0 20px 20px 10px;
  }
  .el-dialog__headerbtn {
    top: 8px;
  }

  //  居中显示避免内容溢出
  .el-dialog__body {
    flex: 1;
    overflow: auto;
    padding-right: 5px;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}
.app-wrapper .el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background-color: #f2f3f6 !important;
}
.el-icon.el-breadcrumb__separator {
  color: grey;
}

// 表格默认最大高度
.el-table__body-wrapper .el-scrollbar__wrap--hidden-default {
  max-height: calc(100vh - 450px);
}
// 弹窗内部高度
.el-dialog__body {
  max-height: 75vh;
  overflow: auto;
  padding-top: 10px;
}
.el-button--primary.is-plain:focus {
  background-color: transparent;
  border-color: transparent;
  color: #409eff;
}
.el-message-box__message {
  padding-left: 15px !important;
}
.el-message-box__status {
  position: relative;
  transform: translateY(0px);
}

// .el-table .el-table__header-wrapper th{
//     color: #000000 !important;
// }

.el-table {
  color: #000000 !important;
}

.el-menu-item {
  color: #000000 !important;
}

.el-tree {
  color: #000000 !important;
}

.vxe-table .vxe-table--header-wrapper {
  color: #000000 !important;
}
.vxe-table--render-default {
  color: #000000 !important;
  font-family:
    Helvetica Neue,
    Helvetica,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    PingFang SC,
    sans-serif,
    sans-serif !important;
}

.vxe-select {
  color: #000000 !important;
}

.vxe-input--inner {
  color: #000000 !important;
}

.vxe-select--panel {
  color: #000000 !important;
}

// 文字提示框
.el-popper.is-dark {
  --el-text-color-primary: #1f232a;
  box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.14);
  border-radius: 10px;
  > span:first-child {
    display: block;
    max-width: 430px; /* 设置 Tooltip 的最大宽度 */
    max-height: 100px; /* 设置 Tooltip 的最大宽度 */
    overflow: auto;
    white-space: pre-wrap; /* 保留文本中的换行符 */
    word-break: break-all;
  }

  // 浏览器滚动条
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }
}

.el-popper[data-popper-placement^="bottom"] .el-popper__arrow:before {
  border-bottom-color: transparent !important;
  border-right-color: transparent !important;
}

.el-popper.is-light {
  border: none;
  .el-popper__arrow:before {
    background: transparent !important;
    border: 1px solid transparent;
  }
}
