<script setup>
import FilterSearch from "./components/FilterSearch.vue";
import MediaTable from "./components/MediaTable.vue";
import { getPlatformList } from "@/api/poManage/poInfo";
import { getMediaList, delMedia, editMedia, addMedia,exportMedia } from "@/api/networkuser/user";
import { MEDIA_TABLE_COLUMNS } from "./config/tableColumns";
import MediaDialog from "./components/MediaDialog.vue";
const { proxy } = getCurrentInstance();
// 搜索条件
const searchCondition = ref({
  mediaName: "",
  platformArr: "",
  mediaTypeArr: "",
});
// 平台数据
const mediaRankPlatform = ref([]);
const MediaTableRef = ref(); // 媒体表格ref
const virForm = ref({}); // 弹框数据
/**
 * 获取表格数据
 * @param data 表格数据
 */
const getDataMap = (data) => {
  return data.map((item) => ({
    id: item.id,
    mediaName: item.mediaName,
    platform: item.platform,
    mediaType: item.mediaType,
    createTime: item.createTime,
  }));
};

const interfaceInfo = computed(() => ({
  api: getMediaList,
  params: {
    mediaName: searchCondition.value.mediaName,
    platformArr: searchCondition.value.platformArr ? searchCondition.value.platformArr.join(',') : '',
    mediaTypeArr: searchCondition.value.mediaTypeArr ? searchCondition.value.mediaTypeArr.join(',') : '',
  },
  getDataMap,
}));

// 刷新数据
const refreshData = () => {
  // console.log(searchCondition.value,'searchCondition')
  const params = {
    mediaName: searchCondition.value.mediaName,
    platformArr: searchCondition.value.platformArr ? searchCondition.value.platformArr.join(',') : '',
    mediaTypeArr: searchCondition.value.mediaTypeArr ? searchCondition.value.mediaTypeArr.join(',') : '',
  };
  MediaTableRef.value.getTableData(params);
}

// 删除数据
const removeMedia = (row) => {
  // 二次确认
  proxy
    .$confirm("确认删除该媒体号？", "删除媒体号", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(async () => {
      const res = await delMedia([row.id]);
      if (res.code === 200) {
        proxy.$message.success("删除媒体号成功");
        refreshData();
      }
    });
}

// 确认编辑/新增
const submitForm = (value, type) => {
  // 提交前去除createTime属性
  const submitValue = { ...value };
  delete submitValue.createTime;
  if(type === 'add'){
    addMedia(submitValue).then(res => {
      if (res.code === 200) {
        proxy.$message.success("新增媒体号成功");
        refreshData();
      }
    })
  }else{
    editMedia(submitValue).then(res => {
      if (res.code === 200) {
        proxy.$message.success("编辑媒体号成功");
        refreshData();
      }
    })
  }
}

// 新增
const handleAdd = () => {
  proxy.$refs["virRef"].openDlg("add", {})

}

// 编辑
const handleEdit = (row) => {
  virForm.value = row;
  proxy.$refs["virRef"].openDlg("edit", virForm.value);
}

// 导出
const handleExport = async () => {
  const downloadLoadingInstance = ElLoading.service({
    text: "正在下载数据，请稍候",
    background: "rgba(0, 0, 0, 0.7)"
  })
  const res = await exportMedia({});
  if (res.code === 200) {
    const { filePath, fileName } = res.data;
    downloadZipFile(import.meta.env.VITE_APP_BASE_API + filePath, fileName);
  }
  downloadLoadingInstance.close();
}

/**
 * 下载文件
 */
function downloadZipFile(url, fileName) {
  const link = document.createElement("a");
  link.href = url;
  link.download = fileName || "媒体库信息.zip"; // 设置下载文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link); // 清理 DOM
}

/**
 * 获取舆情类型数据
 */
const getPlatformTypes = () => {
  getPlatformList().then((response) => {
    mediaRankPlatform.value = response.data.map((label) => ({
      value: label,
      label: label,
    }));
  });
};

// 选择字典数据
const { media_rank_type } = proxy.useDict(
  "media_rank_type",
);

getPlatformTypes()
</script>
<template>
  <div class="app-container">
    <!-- 标题 -->
    <div class="appTitle">媒体库</div>
    <!-- 搜索选项 -->
    <div class="flex justify-between flex-wrap">
      <div class="flex items-center mb-[10px]">
        <FilterSearch :search-condition="searchCondition" :platform-list="mediaRankPlatform"
          :rank-type-list="media_rank_type" @search="refreshData" />
      </div>
    </div>
    <!-- 新增|导出按钮 -->
    <div class="flex justify-between flex-wrap">
      <div class="flex items-center mb-[10px]">
        <el-button type="primary" @click="handleAdd">+新增媒体数据</el-button>
        <el-button plain @click="handleExport">导出</el-button>
      </div>
    </div>
    <!-- 媒体表格 -->
    <MediaTable ref="MediaTableRef" :interface-info="interfaceInfo" :table-columns="MEDIA_TABLE_COLUMNS"
      :rank-type-list="media_rank_type" @removeMedia="removeMedia" @editMedia="handleEdit" @refreshData="refreshData" />
    <!-- 弹框 -->
    <MediaDialog ref="virRef" @submitForm="submitForm" :platform-list="mediaRankPlatform"
      :rank-type-list="media_rank_type"></MediaDialog>
  </div>
</template>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: absolute; // 撑开高度
  padding: 10px 20px;

  .appTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1f2329;
    margin: 12px 0 20px;
  }
}

.MediaTable-container {
  height: calc(100% - 42px - 54px - 42px);
}
</style>