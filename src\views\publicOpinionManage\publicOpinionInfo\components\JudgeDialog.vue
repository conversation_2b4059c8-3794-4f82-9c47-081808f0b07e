<template>
  <div class="dialog-container">
    <el-dialog :model-value="modelValue" width="600px" @close="onCancle">
      <template #header>
        <span class="dialogTitle">{{ dialogInfo.title }}</span>
      </template>

      <div v-if="isFinishHandle" class="poInfoTip">贴文已进行处置，暂无法修改</div>
      <div class="poInfoLabel">{{ dialogInfo.label }}</div>
      <el-radio-group v-model="judgeResult" :disabled="isFinishHandle">
        <el-radio label="是" value="1" />
        <el-radio label="否" value="0" />
      </el-radio-group>

      <template #footer v-if="!isFinishHandle">
        <el-button class="cancelDialogBtn" @click="onCancle">取消</el-button>
        <el-button type="primary" class="addDialogBtn" @click="onOk">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { HANDLE_STATUS } from "../../config/constant.js";
import { changeRumorStatus, changeSourceStatus } from "@/api/poManage/poInfo";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  judgeType: {
    type: String,
    required: true
  },
  judgePoInfo: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(["update:modelValue", "refreshData"]);

const { proxy } = getCurrentInstance();

const judgeResult = ref("0");

const dialogInfo = computed(() =>
  props.judgeType === "rumor" ? { title: "谣言鉴定", label: "贴文是否为谣言" } : { title: "删帖判别", label: "贴文是否删除" }
);
const isFinishHandle = computed(() => props.judgePoInfo.handleStatus === HANDLE_STATUS.FINISH_HANDLE);

// 展开弹窗时，回填选中状态
watch(
  () => props.modelValue,
  val => {
    if (val) {
      judgeResult.value = props.judgeType === "rumor" ? props.judgePoInfo.rumorStatus : props.judgePoInfo.articleStatus;
    }
  }
);

function onCancle() {
  reset();
  emit("update:modelValue", false);
}

async function onOk() {
  if (props.judgeType === "rumor") {
    const res = await changeRumorStatus({ reportId: props.judgePoInfo.id, rumorStatus: judgeResult.value });
    if (res.code === 200) {
      proxy.$message.success("处理成功");
      emit("refreshData");
      onCancle();
    }
  } else {
    const res = await changeSourceStatus({ reportId: props.judgePoInfo.id, sourceStatus: judgeResult.value });
    if (res.code === 200) {
      proxy.$message.success("处理成功");
      emit("refreshData");
      onCancle();
    }
  }
}

/**
 * 重置数据
 */
function reset() {
  judgeResult.value = "0";
}
</script>

<style lang="scss" scoped>
.dialog-container {
  :deep(.el-dialog) {
    padding: 24px 0;
    .el-dialog__header {
      padding-left: 24px;
      .dialogTitle {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #1f2329;
      }
    }

    .el-dialog__body {
      padding: 0 24px;

      .poInfoTip {
        width: 100%;
        height: 44px;
        line-height: 44px;
        background: #f8f9fa;
        border-radius: 6px;
        padding-left: 16px;
        margin-bottom: 20px;

        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #787e85;
      }

      .poInfoLabel {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1f2329;
        margin-bottom: 10px;
      }

      .el-radio__input.is-disabled.is-checked .el-radio__inner {
        background: #ffffff;
        border: 5px solid rgba(0, 112, 255, 0.41);

        &::after {
          background: #ffffff;
        }
      }

      .el-radio__inner::after {
        width: 5px;
        height: 5px;
      }

      .el-radio__label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1f2329;
      }
    }

    .el-dialog__footer {
      background: #ffffff;
      border-radius: 0px 0px 10px 10px;
      padding: 0 24px 0 0;
      margin-top: 24px;

      .cancelDialogBtn,
      .addDialogBtn {
        width: 80px;
        height: 32px;
        line-height: 32px;
        border-radius: 5px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
      }
      .cancelDialogBtn {
        border: 1px solid #cfd2d6;
        color: #1f2329;
      }
    }
  }
}
</style>
