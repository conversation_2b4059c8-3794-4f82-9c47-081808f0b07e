import request from '@/utils/request'
import portalRequest from "@/utils/portalRequest.js";

// 查询登录日志列表
export function list(query) {
  return portalRequest({
    url: '/monitor/logininfor/list',
    method: 'get',
    params: query
  })
}

// 删除登录日志
export function delLogininfor(infoId) {
  return portalRequest({
    url: '/monitor/logininfor/' + infoId,
    method: 'delete'
  })
}

// 解锁用户登录状态
export function unlockLogininfor(userName) {
  return portalRequest({
    url: '/monitor/logininfor/unlock/' + userName,
    method: 'get'
  })
}

// 清空登录日志
export function cleanLogininfor() {
  return portalRequest({
    url: '/monitor/logininfor/clean',
    method: 'delete'
  })
}
