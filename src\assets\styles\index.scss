@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './ruoyi.scss';
@import './func.scss';

body {
    height: 100%;
    margin: 0;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    // font-family: PingFang SC, Helvetica Neue, Helvetica, Hiragino Sans GB, Microsoft YaHei, Arial,
    //     sans-serif;
    font-family: Helvetica Neue, Helvetica, Hiragino Sans GB, Microsoft YaHei, Arial,PingFang SC, sans-serif,
    sans-serif;
}

label {
    font-weight: 700;
}

html {
    height: 100%;
    box-sizing: border-box;
}

#app {
    height: 100%;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

p {
    margin: 0;
}

.no-padding {
    padding: 0px !important;
}

.padding-content {
    padding: 4px 0;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

div:focus {
    outline: none;
}

.fr {
    float: right;
}

.fl {
    float: left;
}

.pr-5 {
    padding-right: 5px;
}

.pl-5 {
    padding-left: 5px;
}

.block {
    display: block;
}

.pointer {
    cursor: pointer;
}

.inlineBlock {
    display: block;
}

.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.clearfix {
    &:after {
        visibility: hidden;
        display: block;
        font-size: 0;
        content: ' ';
        clear: both;
        height: 0;
    }
}

aside {
    background: #eef1f6;
    padding: 8px 24px;
    margin-bottom: 20px;
    border-radius: 2px;
    display: block;
    line-height: 32px;
    font-size: 16px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
        'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    color: #2c3e50;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    a {
        color: #337ab7;
        cursor: pointer;

        &:hover {
            color: rgb(32, 160, 255);
        }
    }
}

//main-container全局样式
.app-container {
    padding: 20px;
    // padding-top:0;
    background-color: #fff;
    border-radius: 4px;
    overflow-y: auto;
    min-width: 1000px;
    position: relative;
}
// 不规则container
.clocking-container {
    min-height: calc(100vh - 140px);
    max-height: calc(100vh - 110px);
    overflow-y: auto;
}
.components-container {
    margin: 30px 50px;
    position: relative;
}

.pagination-container {
    margin-top: 30px;
}

.text-center {
    text-align: center;
}

.sub-navbar {
    height: 50px;
    line-height: 50px;
    position: relative;
    width: 100%;
    text-align: right;
    padding-right: 20px;
    transition: 600ms ease position;
    background: linear-gradient(
        90deg,
        rgba(32, 182, 249, 1) 0%,
        rgba(32, 182, 249, 1) 0%,
        rgba(33, 120, 241, 1) 100%,
        rgba(33, 120, 241, 1) 100%
    );

    .subtitle {
        font-size: 20px;
        color: #fff;
    }

    &.draft {
        background: #d0d0d0;
    }

    &.deleted {
        background: #d0d0d0;
    }
}

.link-type,
.link-type:focus {
    color: #337ab7;
    cursor: pointer;

    &:hover {
        color: rgb(32, 160, 255);
    }
}

.filter-container {
    padding-bottom: 10px;

    .filter-item {
        display: inline-block;
        vertical-align: middle;
        margin-bottom: 10px;
    }
}

.text-none-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

@for $index from 1 through 10 {
    .text-ellipsis-#{$index} {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: $index;
        -webkit-box-orient: vertical;
    }
}

// 字体
@font-face {
    font-family: 'YouSheBiaoTiHei';
    src: url('@/assets/font/YouSheBiaoTiHei.ttf') format('truetype'); /* chrome、firefox、opera、Safari, Android, iOS 4.2+ */
}
@font-face {
    font-family: 'PingFang SC';
    src: url('@/assets/font/PingFang.ttf') format('truetype'); /* chrome、firefox、opera、Safari, Android, iOS 4.2+ */
}
@font-face {
    font-family: 'PangMenZhengDao';
    src: url('@/assets/font/PangMenZhengDao.ttf') format('truetype'); /* chrome、firefox、opera、Safari, Android, iOS 4.2+ */
}
@font-face {
    font-family: 'DINAlternate';
    src: url('@/assets/font/DINAlternate-bold.otf') format('opentype'); /* chrome、firefox、opera、Safari, Android, iOS 4.2+ */
}
@font-face {
    font-family: 'HuXiaoBo';
    src: url("@/assets/font/HuXiaoBoNanShenTi-2.otf") format("opentype");
}
@font-face {
    font-family: 'DIN';
    src: url("@/assets/font/DIN.ttf") format("opentype");
}
@font-face {
    font-family: 'YouSheBiaoTiYuan';
    src: url('@/assets/font/YouSheBiaoTiYuan.otf') format('opentype');
}
// 自定义
.text-center {
    text-align: center;
}
.poninter {
    cursor: pointer;
}
.pad-top-12 {
    padding-top: 12px;
}
.margin-top-12 {
    margin-top: 12px;
}
.margin-bottom-12 {
    margin-bottom: 12px;
}
.app-title {
    font-size: 18px;
    color: #161616;
    font-weight: 600;
    padding: 0px 0 14px 0;
}
label.home-form-title {
    font-size: 17px;
    margin: 10px 0;
    display: block;
    display: flex;
    align-items: center;
    &::before {
        content: '';
        display: inline-block;
        background: #0056f9;
        width: 3px;
        height: 19px;
        margin-right: 10px;
        vertical-align: middle;
    }
}
// tag
.el-tag.draft {
    background: #e2e2e2;
    border-radius: 4px;
    color: #4e4e4e;
    border: none;
}
.el-tag.processing {
    background: #e5ecff;
    border-radius: 4px;
    color: #2168ff;
    border: none;
}
// tag
.el-tag.finished {
    background: #f2fcf8;
    border-radius: 4px;
    color: #01b197;
    border: none;
}
.el-tag.returned {
    background: #ffeae8;
    border-radius: 4px;
    color: #f54b45;
    border: none;
}
.flexcenter-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.bet {
    justify-content: space-between;
}
.felx {
    display: flex;
}
.alcenter {
    align-items: center;
}
.felx-end {
    justify-content: flex-end;
}
.flex-gap10 {
    display: flex;
    align-items: center;
    gap: 10px;
}

.flexcloumn {
    flex-direction: column;
}
.ml8 {
    margin-left: 8px;
}
.mb18 {
    margin-bottom: 18px;
}
.mt20 {
    margin-top: 20px;
}
.padb15 {
    padding-bottom: 15px;
}
.padlr0{
    padding-left: 0;
    padding-right: 0;
}
// home----工作台样式
.home-app-container {
    padding: 20px;
    border-radius: 4px;
    min-height: calc(100vh - 50px);
    min-width: 1000px;
    position: relative;
}
.back0D1633 {
    background-color: #0d1633;
}

//
.table-incalss {
    :deep(.el-form-item) {
        margin: 5px 30px 5px 0px;
    }
    .el-form-item {
        margin: 5px 30px 5px 0px;
    }
}
.custom-date {
    .el-time-spinner__wrapper {
        width: 100% !important;
    }
    .el-date-range-picker__content {
        padding: 2px 10px;
    }
    .el-date-table td {
        padding: 0;
    }
}
.custom-drawer {
    .el-drawer__header {
        margin: 0;
        padding: 10px 20px;
    }
    .el-drawer__body {
        padding: 10px 20px;
    }
}

.input-algin-center {
    .el-input__inner {
        text-align: center;
    }
}
:focus {
    outline: 0;
}

// 消息模块
.el-popper.is-light .el-popper__arrow:before {
    background: transparent !important;
    border: 1px solid transparent;
}
.el-popper[data-popper-placement^='bottom'] .el-popper__arrow:before {
    border-bottom-color: transparent !important;
    border-right-color: transparent !important;
}
.el-popper.is-light{
    border:none
}
.costom-pop{
    .el-select-dropdown {
        background-color:#1E2F68 ;
        box-shadow:none !important;
        border:none;
    }
    .el-select-dropdown__item{
        color: #fff;
    }
    .el-select-dropdown__item.is-hovering {
        background-color: #546988;
    }
}

// 首页-绑定二维码弹窗
.el-popover.el-popper.bindQRcodeWrapper {
  width: px2vw(152)!important;
  min-width: px2vw(152);
  height: px2vw(152);
  background: rgba(22, 38, 100, 0.93);
  border: px2vw(1) solid rgba(63, 78, 167, 1);
  border-radius: px2vw(9);
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  top: px2vw(82)!important;

  .weixinQRImg {
    width: px2vw(130);
    height: px2vw(130);
  }
}

// 首页-右上方用户下拉
.el-popper.homeUserDropdown {
  top: px2vw(87)!important;
  .el-dropdown-menu {
    border-radius: px2vw(4);
    padding: px2vw(5) 0;
    .el-dropdown-menu__item {
      font-size: px2vw(14);
      line-height: px2vw(22);
      padding: px2vw(5) px2vw(16);
    }
  }
  .dropdownItem {
    width: px2vw(100);
  }
}

// 舆情管理大屏详情页-表格上报单位列下拉
.el-popper.detailWorkUnitDropdown {
  .workUnitDropdownWrapper {
    padding: px2vw(20);
    row-gap: px2vw(18);
    .dropdownItemWrapper {
      row-gap: px2vw(4);

      .subDeptName {
        width: 100%;
        height: px2vw(20);
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: px2vw(14);
        color: #1f2329;
        line-height: px2vw(20);
        text-align: left;
        font-style: normal;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .createTime {
        width: 100%;
        height: px2vw(20);
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: px2vw(12);
        color: #8d949e;
        line-height: px2vw(20);
        text-align: left;
        font-style: normal;
      }
    }
  }
}
