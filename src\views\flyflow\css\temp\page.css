* {
  margin: 0;
  padding: 0;
  list-style: none;
}
blockquote,
body,
button,
dd,
dl,
dt,
fieldset,
form,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
input,
legend,
li,
ol,
p,
pre,
td,
textarea,
th,
ul {
  margin: 0;
  padding: 0;
}
body,
button,
input,
select,
textarea {
  font: 12px/1.5 tahoma, arial, sans-serif;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
}
address,
cite,
dfn,
em,
var {
  font-style: normal;
}
code,
kbd,
pre,
samp {
  font-family: courier new, courier, monospace;
}
small {
  font-size: 12px;
}
ol,
ul {
  list-style: none;
}
a {
  text-decoration: none;
}
a:hover {
  text-decoration: underline;
}
legend {
  color: #000;
}
fieldset,
img {
  border: 0;
}
button,
input,
select,
textarea {
  font-size: 100%;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
.main {
  padding: 30px 100px;
}
.main h1 {
  font-size: 36px;
  color: #333;
  text-align: left;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
}
.helps {
  margin-top: 40px;
}
.helps pre {
  padding: 20px;
  margin: 10px 0;
  border: solid 1px #e7e1cd;
  background-color: #fffdef;
  overflow: auto;
}
.iconfont-list {
  overflow: hidden;
}
.iconfont-list li {
  float: left;
  width: 100px;
  height: 150px;
  text-align: center;
}
.iconfont-list .icon {
  font-size: 42px;
  line-height: 100px;
  margin: 10px 0;
  color: #333;
  font-style: normal;
  -webkit-transition: font-size 0.25s ease-out 0s;
  -moz-transition: font-size 0.25s ease-out 0s;
  transition: font-size 0.25s ease-out 0s;
}
.iconfont-list .icon:hover {
  font-size: 100px;
}
.iconfont-list .code {
  color: green;
  font-weight: bold;
}
