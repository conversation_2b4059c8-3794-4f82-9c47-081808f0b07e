<template>
  <div class="app-container">
    <!-- Tab -->
    <el-tabs v-model="tabType" @tab-change="tabChange">
      <el-tab-pane label="全部" name="all" />
      <el-tab-pane name="mine">
        <template #label>
          <span class="mr-[5px]">我的任务</span>
          <span class="todoNum flex-center">{{ mineNum }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 表单、表格 -->
    <div class="w-full h-[calc(100%-60px-32px)]" ref="tableContainerRef">
      <CommonTable
        ref="collaborationTableRef"
        :columns="tableColumns"
        :data="tableData"
        :loading="tableLoading"
        :show-default-btn="false"
        :show-operation-column="showOperation"
        :operation-column="160"
        no-padding
        :max-height="tableHeight"
        :highlight-current-row="false"
        @handleRowClick="handleRowClick"
      >
        <!-- 舆情id -->
        <template #poId="{ row }">
          <div class="whitespace-normal break-all">
            {{ row?.reportNumber || "-" }}
          </div>
        </template>

        <!-- 是否为首报 -->
        <template #isFirst="{ row }">
          <span>{{ row.isFirst === IS_FIRST_STATUS.YES ? "是" : "否" }}</span>
        </template>

        <!-- 标题 -->
        <template #poName="{ row }">
          <div class="contentSpaceControl">
            {{ row?.poName || "-" }}
          </div>
        </template>

        <!-- 内容可能为文本/图片 -->
        <template #poContent="{ row }">
          <ImagePreview
            v-if="contentIsImg(row.poContent)"
            :src="row.poContent"
            :width="100"
            :height="100"
            @click.stop
          />
          <div v-else class="contentSpaceControl whitespace-pre-wrap!">
            {{ poContentF(row.poContent) }}
          </div>
        </template>

        <!-- 贴文链接 -->
        <template #poLink="{ row }">
          <div v-if="row.poLink" class="flex">
            <div class="tagWrapper truncate">
              <img
                src="@/assets/images/poManage/link.png"
                alt="link"
                class="w-[10px] h-[10px] mr-[4px]"
              />
              <el-link
                class="linkText truncate"
                :underline="false"
                :href="row.poLink"
                target="blank"
                @click.stop
                >{{ row.poLink }}</el-link
              >
            </div>
          </div>
          <span v-else>-</span>
        </template>

        <!-- 事件 -->
        <template #poEvent="{ row }">
          <PoEvent
            :poEvent="row.poEvent"
            v-if="row.poEvent && Array(row.poEvent) && row.poEvent.length > 0"
          />
          <span v-else>-</span>
        </template>

        <!-- 所属单位 -->
        <template #workUnit="{ row }">
          <div @click.stop>
            <el-dropdown
              trigger="click"
              v-if="
                row.workUnit && row.linkUrlCount && Number(row.linkUrlCount) > 0
              "
              @visible-change="(visible) => handleClickDropdown(visible, row)"
            >
              <template #default>
                <div class="mr-[4px] text-[14px] flex items-center">
                  {{ row.workUnit }}<el-icon><CaretBottom /></el-icon>
                </div>
              </template>

              <template #dropdown>
                <el-scrollbar max-height="320px">
                  <div class="p-[20px] flex flex-col gap-y-[18px]">
                    <section
                      class="flex flex-col gap-y-[4px]"
                      v-for="(item, index) in linkReportRecords"
                      :key="index"
                    >
                      <div class="subDeptName">
                        {{ item.deptName }}
                      </div>
                      <div class="createTime">{{ item.reporterTime }}</div>
                    </section>
                  </div>
                </el-scrollbar>
              </template>
            </el-dropdown>
            <span v-else class="mr-[4px]">{{ row.workUnit || "-" }} </span>
          </div>
        </template>

        <!-- 来源 -->
        <template #reportSource="{ row }">
          <dict-tag
            v-if="row.reportSource"
            :options="opinion_source_type"
            :value="row.reportSource"
          />
          <span v-else>-</span>
        </template>

        <!-- 网安网端需要跳转到网名库详情，互联网端不需要 -->
        <template #netizenNickname="{ row }">
          <span
            v-if="poManageStore.isWangAn && row.netizenAccountId"
            class="text-[#0052D9]"
            @click.stop="goNetPersonDetail(row)"
          >
            {{ row.netizenNickname }}
          </span>
          <span v-else>{{ row.netizenNickname }}</span>
        </template>

        <!-- 图片 -->
        <template #poImg="{ row }">
          <ImagePreview
            v-if="row.poImg?.length > 0"
            :src="row.poImg[0]"
            :width="100"
            :height="100"
            @click.stop
          />
          <span v-else>-</span>
        </template>

        <!-- 贴文状态 -->
        <template #articleStatus="{ row }">
          <span class="truncate">{{
            articleStatusT(row.articleStatus).text
          }}</span>
        </template>

        <!-- <template #handleStatus="{ row }">
          <div
            class="handleStatusWrapper"
            :style="{
              backgroundColor: handleStatusT(row.handleStatus)?.bgColor,
            }"
          >
            <span
              class="truncate"
              :style="{ color: handleStatusT(row.handleStatus)?.color }"
              >{{ handleStatusT(row.handleStatus)?.label }}</span
            >
          </div>
        </template> -->

        <template #isSensitive="{ row }">
          {{ isSensitiveF(row.isSensitive) }}
        </template>

        <!-- 时间的处理，日期第一行,时分秒第二行 -->
        <template #publicTime="{ row }">
          <div class="whitespace-normal break-all">
            {{ row?.publicTime || "-" }}
          </div>
        </template>

        <template #createTime="{ row }">
          <div class="whitespace-normal break-all">
            {{ row?.createTime || "-" }}
          </div>
        </template>

        <template #deadlineTime="{ row }">
          <div class="whitespace-normal break-all">
            {{ row?.deadlineTime || "-" }}
          </div>
        </template>

        <template #handleStatus="{ row }">
          <div class="flex-center">
            <div
              class="truncate statusClass"
              :style="handleStatusF(row.handleStatus).style"
            >
              {{ handleStatusF(row.handleStatus).text }}
            </div>
          </div>
        </template>

        <!-- 处理状态 -->
        <template #handleStatus2="{ row }">
          <div class="flex-center">
            <div
              class="truncate statusClass"
              :style="handleStatus2F(row.handleStatus2).style"
            >
              {{ handleStatus2F(row.handleStatus2).text }}
            </div>
          </div>
        </template>

        <!-- 表单 -->
        <template #search>
          <el-form :model="queryParams" :inline="true" class="custom-form">
            <el-form-item label="" prop="reportNumber">
              <el-input
                prefix-icon="Search"
                v-model="queryParams.reportNumber"
                placeholder="请输入舆情编号"
                clearable
                style="width: 200px"
                @keyup.enter="getTableData"
                @clear="getTableData"
              />
            </el-form-item>

            <el-form-item label="" prop="event">
              <el-input
                prefix-icon="Search"
                v-model="queryParams.event"
                placeholder="请输入舆情事件"
                clearable
                style="width: 200px"
                @keyup.enter="getTableData"
                @clear="getTableData"
              />
            </el-form-item>

            <el-form-item label="" prop="targetGroup">
              <el-select
                v-model="queryParams.targetGroup"
                placeholder="请选择目标群"
                clearable
                style="width: 200px"
                @change="getTableData"
              >
                <template #prefix>目标群</template>
                <el-option label="全部" value="all" />
                <el-option
                  v-for="item in targetGroupList"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="" prop="reportSource">
              <el-select
                v-model="queryParams.reportSource"
                placeholder="请选择消息来源"
                clearable
                style="width: 200px"
                @change="getTableData"
                @clear="getTableData"
              >
                <template #prefix>来源</template>
                <el-option label="全部" value="all" />
                <el-option
                  v-for="dict in opinion_source_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

            <!-- <el-button type="primary" icon="Search" @click="getTableData"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button> -->
          </el-form>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button
            v-if="
              row.commandCategory !== PROCESS_TYPE.VIEW &&
              !HANDLE2_STATUS.FINISH_HANDLE.includes(row.handleStatus2)
            "
            link
            type="primary"
            @click.stop="onHandle('handle', row)"
            >处理</el-button
          >
          <el-button
            v-else
            link
            type="primary"
            @click.stop="onHandle('view', row)"
            >查看</el-button
          >

          <el-popconfirm
            v-if="row.handleStatus !== HANDLE_STATUS.FINISH_HANDLE"
            class="custom-popconfirm"
            confirm-button-text="确认"
            cancel-button-text="取消"
            :icon="InfoFilled"
            icon-color="#0052d9"
            :width="300"
            title="确定将条舆情报送至WA进行协同处理么?"
            @confirm.stop="onConfirmWA(row)"
          >
            <template #reference>
              <el-button link type="primary" @click.stop>报送WA处理</el-button>
            </template>
          </el-popconfirm>
        </template>
      </CommonTable>
    </div>

    <!-- 分页 -->
    <div class="h-[32px] pb-[15px] flex justify-end items-center">
      <pagination
        v-model:page="pageObj.pageNum"
        v-model:limit="pageObj.pageSize"
        :total="pageObj.total"
        @pagination="getTableData"
      />
    </div>

    <!-- 舆情处置弹框 -->
    <HandleDrawer ref="handleDrawerRef" @refresh-data="getTableData" />
  </div>
</template>

<script setup>
import CommonTable from "@/components/commonTable/index.vue";
import HandleDrawer from "./components/handleDrawer.vue";
import { publicOpinionInfoColumns } from "./config/tableColumns.js";
import {
  handleSendWA,
  getMineList,
  getMineCount,
} from "@/api/publicOpinionCollaboration/index.js";
import { getPoAllList } from "@/api/publicOpinionCollaboration/index.js";
import { useFetchTableData } from "@/views/publicOpinionCollaboration/hooks/useFetchTableData";
import { ARTICLE_STATUS_LIST } from "@/views/publicOpinionCollaboration/config/index.js";
import { InfoFilled } from "@element-plus/icons-vue";
import {} from "@/views/publicOpinionCollaboration/config/constant.js";
import { watchEffect } from "vue";
import { usePoManageStore } from "@/store/modules/poManage.js";
import {
  HANDLE_STATUS,
  IS_FIRST_STATUS,
} from "@/views/publicOpinionCollaboration/config/constant.js";
import PoEvent from "@/views/publicOpinionManage/components/PoEvent.vue";
import { getLinkReportRecords } from "@/api/poManage/poInfo";
import { getTargetGroup } from "@/api/poManage/poInfo.js";
import { useTableHeight } from "@/hooks/useTableHeight";
import { PUBLIC_OPINION_INFO_COLUMNS } from "@/views/publicOpinionManage/config/tableColumns";
import {
  HANDLE_STATUS_LIST,
  HANDLE2_STATUS_LIST,
  SENSITIVE_STATUS_LIST,
} from "@/views/publicOpinionManage/config/constant.js";
import { PROCESS_TYPE } from "@/views/publicOpinionManage/config/mapRel";
import { HANDLE2_STATUS } from "@/views/publicOpinionManage/config/constant";

const { proxy } = getCurrentInstance();

const { opinion_source_type, media_type } = proxy.useDict(
  "opinion_source_type",
  "media_type"
);

const queryParams = ref({
  reportNumber: "",
  event: "",
  reportSource: "all",
  targetGroup: "all",
  pageNum: history.state?.pageNum,
  pageSize: history.state?.pageSize,
});

const tabType = ref(history.state.tab || "all"); // 全部/我的任务

const tableContainerRef = ref(null); // 表格容器
const collaborationTableRef = ref(null); // 舆情处置表格
const handleDrawerRef = ref(null); // 舆情处置抽屉

const { tableHeight, getTableHeight } = useTableHeight(tableContainerRef, 42);

watch(tabType.value, async (val) => {
  if (val) {
    await nextTick(); // 等待元素渲染完成
    getTableHeight();
  }
});

const linkReportRecords = ref([]);

const contentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 内容是否为图片
const poContentF = computed(() => (val) => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter((i) => !contentIsImg.value(i));
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});

const showOperation = computed(() => {
  return tabType.value === "mine";
});

const tableApi = computed(() => {
  return tabType.value === "all" ? getPoAllList : getMineList;
});
const poManageStore = usePoManageStore();

const mineNum = ref(undefined); // 我的任务数量

// Tab为全部没有截止时间字段
const tableColumns = computed(() => {
  return tabType.value === "all"
    ? PUBLIC_OPINION_INFO_COLUMNS([
        "selection",
        "handleUnit",
        "handler",
        "handleStatus2",
        "score",
      ])
    : PUBLIC_OPINION_INFO_COLUMNS(["selection", "score"]);
});

const isSensitiveF = computed(
  () => (val) =>
    SENSITIVE_STATUS_LIST.find((i) => i.value === val)?.label || "-"
);

// 贴文、处置状态
const articleStatusT = computed(() => (val) => {
  const obj = ARTICLE_STATUS_LIST.find((i) => i.value === val);
  return { text: obj?.label || "-" };
});
// const handleStatusT = computed(() => (val) => {
//   return HANDLE_STATUS_LIST.find((i) => i.value === val);
// });
const handleStatusF = computed(() => (val) => {
  const obj = HANDLE_STATUS_LIST.find((i) => i.value === val);
  return {
    text: obj?.label || "-",
    style: { color: obj?.color || "#333333", background: obj?.bgColor },
  };
});

const handleStatus2F = computed(() => (val) => {
  const obj = HANDLE2_STATUS_LIST.find((i) => i.value.includes(val));
  return {
    text: obj?.label || "-",
    style: { color: obj?.color || "#333333", background: obj?.bgColor },
  };
});
// 表格数据
const { tableData, tableLoading, pageObj, getTableData } = useFetchTableData({
  api: tableApi,
  params: queryParams,
  getDataMap,
});

/**
 * 我的-待办个数
 */
const getMine = async () => {
  const res = await getMineCount();
  if (res.code === 200) {
    mineNum.value = res.data;
  }
};

const targetGroupList = ref([]);

/**
 * 获取目标群下拉项
 */
async function getTargetGroupList() {
  const res = await getTargetGroup();
  if (res.code === 200) {
    targetGroupList.value = res.data;
  }
}

getTargetGroupList();

/**
 * 舆情处置
 */
const onHandle = (type, row) => {
  handleDrawerRef.value.openDrawer(type, row);
};

/**
 * 报送单位下拉点击事件
 */
async function handleClickDropdown(visible, row) {
  if (visible) {
    const res = await getLinkReportRecords(row?.id);
    if (res?.code === 200) {
      linkReportRecords.value = res?.data;
    }
  }
}

/**
 * 重置
 */
const resetQuery = () => {
  queryParams.value = {
    reportNumber: "",
    event: "",
    reportSource: "",
    targetGroup: "",
  };
};

/**
 * tabChange事件
 */
const tabChange = () => {
  queryParams.value = {
    reportNumber: "",
    event: "",
    reportSource: "",
    targetGroup: "",
  };
};

/**
 * 报送WA处理
 */
const onConfirmWA = async (row) => {
  const params = {
    reportId: row.id,
    processStep: row.handleStatus,
    command: "VIEW_ASSIGN",
  };
  const res = await handleSendWA(params);
  if (res.code === 200) {
    proxy.$modal.msgSuccess(res.msg);
    getTableData(); // 刷新表格数据
  }
};

/**
 * 数据整理
 */
function getDataMap(data) {
  return data.map((ele) => {
    return {
      id: ele.id,
      workId: ele.taskId,
      reportNumber: ele.reportNumber,
      isFirst: ele.firstReportTag,
      netizenAccountId: ele.netizenAccountId,
      poName: ele.title,
      poContent: ele.content,
      poLink: ele.linkUrl,
      poEvent: ele.event,
      workUnit: ele.workUnit, // 上报单位
      mediaType:
        media_type.value.find((i) => Number(i.value) === ele.mediaType)
          ?.label || "-", // 媒体类型
      reportSource: ele.reportSource,
      platformType: ele.platformTag,
      wechatNickname: ele.wechatNickname, // 微信报送昵称
      netizenNickname: ele.netizenNickname || "-",
      netizenAccount: ele.netizenAccount, // 网名账号
      publicTime: ele.publishTime,
      poImg: ele.photoUrl && ele.photoUrl?.split(","),
      articleStatus: ele.sourceStatus,
      handleStatus: ele.reportProcess,
      createTime: ele.createTime,
      targetGroup: ele.targetGroup,
      deadlineTime: ele.deadlineTime,
      handleUnit: tabType.value === "all" ? ele.deptInChargeName : ele.deptName,
      handler: tabType.value === "all" ? ele.userInChargeName : ele.userName,
      rumorOpinion: ele.rumorOpinion,
      linkUrlCount: ele.linkUrlCount,
      handleStatus2: ele.processStep, // 当前任务所处阶段
      commandCategory: ele.commandCategory, // 当前任务的类别
    };
  });
}

/**
 * 舆情内容点击事件
 */
const handleRowClick = (row) => {
  const type =
    tabType.value === "mine" &&
    row.commandCategory !== PROCESS_TYPE.VIEW &&
    !HANDLE2_STATUS.FINISH_HANDLE.includes(row.handleStatus2)
      ? "handle"
      : "view";
  onHandle(type, row);
};

getMine();

const unwatch = watchEffect(() => {
  if (history.state.poId) {
    if (!handleDrawerRef.value?.openDrawer) return;
    if (tableData.value.length > 0) {
      const poInfoObj = tableData.value.find(
        (ele) => ele.workId === history.state.poId
      );
      handleDrawerRef.value.openDrawer("handle", poInfoObj);
      unwatch(); // 只执行一次
    }
  }
});

/**
 * 跳转网名库
 */
function goNetPersonDetail({ netizenAccountId }) {
  router.push({
    name: "addNetPerson",
    params: { id: netizenAccountId, type: true },
  });
}
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: absolute; // 撑开高度
  padding-top: 0px;
  // padding-bottom: 10px;

  .firstBtn {
    width: 38px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 3px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
  }

  .contentSpaceControl {
    white-space: normal;
    word-break: break-all;

    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4; /* 控制几行打点 */
    line-clamp: 4;
    overflow: hidden;
  }

  :deep(.custom-form.el-form) {
    display: flex;
    // justify-content: space-between;

    .el-form-item {
      margin-right: 8px;
      margin-bottom: 10px;

      .el-input__wrapper {
        border-radius: 6px;
      }
    }
  }

  .tagWrapper {
    height: 22px;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding-left: 8px;
    padding-right: 5px;
    background: #ffffff;
    box-shadow: 0px 1px 3px 0px rgba(222, 224, 232, 0.5);
    border-radius: 14px;
    border: 1px solid #e6e6e9;
  }

  // 舆情内容链接
  :deep(.linkText) {
    display: inline;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #0070ff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &:visited {
      color: #0070ff;
    }
    .el-link__inner {
      display: inline;
    }
  }

  .tagWrapper {
    height: 22px;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding-left: 8px;
    padding-right: 5px;
    background: #ffffff;
    box-shadow: 0px 1px 3px 0px rgba(222, 224, 232, 0.5);
    border-radius: 14px;
    border: 1px solid #e6e6e9;
  }

  .handleStatusWrapper {
    width: 50px;
    height: 19px;
    border-radius: 3px;

    display: flex;
    justify-content: center;
    align-items: center;

    span {
      width: 36px;
      // height: 19px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      line-height: 18px;
      text-align: left;
      font-style: normal;
    }
  }

  .statusClass {
    width: 50px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    border-radius: 3px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
  }

  :deep(.el-tabs) {
    .el-tabs__header {
      margin-bottom: 10px;
      .el-tabs__nav {
        .el-tabs__item {
          height: 51px;
          display: flex;
          justify-content: center;
          align-items: center;

          font-family:
            PingFangSC,
            PingFang SC;
          font-size: 16px;
          font-weight: 400;
          color: #8f959e;

          &.is-active {
            font-weight: 600;
            color: #1f2329;
          }
        }
      }
    }

    .todoNum {
      width: 25px;
      height: 16px;
      background: url("@/assets/images/home/<USER>") no-repeat;
      background-size: cover;
      background-position: center;

      font-family: DINAlternate;
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      letter-spacing: 0;
    }
  }
}
</style>

<style lang="scss">
.el-popconfirm__main {
  font-size: small !important;
}
</style>
