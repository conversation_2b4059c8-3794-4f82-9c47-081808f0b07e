<template>
  <div class="dialog-container">
    <el-dialog :model-value="modelValue" width="600px" @close="onCancle">
      <template #header>
        <span class="dialogTitle">选择事件</span>
      </template>

      <div class="poInfoLabel">事件名称</div>
      <el-select v-model="poEvent" multiple placeholder="请选择舆情事件" clearable>
        <el-option v-for="item in poManageStore.ingPoEventList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>

      <template #footer>
        <el-button class="cancelDialogBtn" @click="onCancle">取消</el-button>
        <el-button type="primary" class="addDialogBtn" @click="onOk">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { usePoManageStore } from "@/store/modules/poManage.js";
import { editPoInfo } from "@/api/poManage/poInfo";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  rowEvent: {
    type: [Array, String],
    required: true
  },
  rowId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(["update:modelValue", "refreshData"]);

const { proxy } = getCurrentInstance();
const poManageStore = usePoManageStore();
poManageStore.getAllPoEventList();

const poEvent = ref([]); // 舆情事件

watchEffect(() => {
  if (props.modelValue) {
    poEvent.value = Array.isArray(props.rowEvent) ? props.rowEvent.filter(i => i.status === "1").map(i => i.id) : []; // 仅回显进行中的事件
  }
});

function onCancle() {
  reset();
  emit("update:modelValue", false);
}

async function onOk() {
  const params = {
    id: props.rowId,
    eventIds: poEvent.value
  };
  const res = await editPoInfo(params);
  if (res.code === 200) {
    proxy.$message.success("提交成功");
    emit("refreshData");
    onCancle();
  }
}

/**
 * 重置数据
 */
function reset() {
  poEvent.value = [];
}
</script>

<style lang="scss" scoped>
.dialog-container {
  :deep(.el-dialog) {
    padding: 16px 0 0;
    .el-dialog__header {
      padding-left: 24px;
      .dialogTitle {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #1f2329;
      }
    }

    .el-dialog__body {
      padding: 10px 24px 0;

      .poInfoLabel {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #646a73;
        margin-bottom: 10px;
      }
    }

    .el-dialog__footer {
      height: 76px;
      line-height: 76px;
      background: #ffffff;
      border-radius: 0px 0px 10px 10px;
      padding: 0 24px 0 0;
      margin-top: 12px;

      .cancelDialogBtn,
      .addDialogBtn {
        width: 80px;
        height: 32px;
        line-height: 32px;
        border-radius: 5px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
      }
      .cancelDialogBtn {
        border: 1px solid #cfd2d6;
        color: #1f2329;
      }
    }
  }
}
</style>
