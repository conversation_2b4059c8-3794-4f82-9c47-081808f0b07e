<template>
  <div class="poInfo-container">
    <el-scrollbar ref="scrollbarRef" class="h-[100%] pr-[14px]">
      <!-- 舆情标题 -->
      <div class="poTitle flex items-center">
        <el-input
          v-if="isEdit"
          v-model="formData.poName"
          placeholder="请输入标题名称（必填）"
          :maxlength="200"
          show-word-limit
          clearable
          class="poTitleInput"
        >
          <template #prefix>
            <div v-if="isFirstF" class="firstTag mr-[11px]">首报</div>
            <div v-if="isSensitiveF" class="sensitiveTag mr-[11px]">敏感</div>
          </template>
        </el-input>
        <template v-else>
          <div v-if="isFirstF" class="firstTag mr-[11px]">首报</div>
          <div v-if="isSensitiveF" class="sensitiveTag mr-[11px]">敏感</div>
          <span class="flex-1 text-[14px] text-[#202D41]">{{
            poInfo.poName || "-"
          }}</span>
        </template>
      </div>

      <!-- 舆情内容 -->
      <div class="mt-[20px]">
        <div class="labelTitle" :class="{ requiredTag: isEdit }">舆情内容</div>
        <el-input
          v-if="isEdit"
          v-model="formData.poContent"
          type="textarea"
          placeholder="请输入舆情内容"
          :rows="6"
          :maxlength="5000"
          show-word-limit
          clearable
        />
        <template v-else>
          <ImagePreview
            v-if="contentIsImg(poInfo.poContent)"
            :src="poInfo.poContent"
            :width="200"
            :height="120"
          />
          <div v-else class="labelValue break-all whitespace-pre-wrap!">
            {{ poContentF(poInfo.poContent) }}
          </div>
        </template>
      </div>

      <!-- 贴文链接 -->
      <div class="mt-[20px]">
        <div class="labelTitle">贴文链接</div>
        <el-input
          v-if="isEdit"
          v-model="formData.poLink"
          placeholder="请输入贴文链接"
          clearable
          class="linkInput"
        >
          <template #prefix>
            <div class="linkPrefix">
              <img
                class="w-[15px] h-[15px]"
                src="@/assets/images/poManage/link2.png"
                alt=""
              />
            </div>
          </template>
        </el-input>
        <template v-else>
          <el-link
            v-if="poInfo.poLink"
            class="linkText break-all"
            :underline="false"
            :href="poInfo.poLink"
            target="blank"
          >
            {{ poInfo.poLink }}
          </el-link>
          <span v-else class="labelValue">-</span>
        </template>
      </div>

      <!-- 图片 -->
      <div class="uploadImgWrapper mt-[20px]">
        <div class="labelTitle">图片</div>
        <div class="flex flex-wrap gap-[10px]" v-if="Array.isArray(curImg)">
          <section
            v-for="(item, index) in curImg"
            :key="index"
            class="mt-[10px]"
          >
            <ImagePreview :src="item" :width="200" :height="120" />
            <div
              v-if="isEdit"
              class="text-[14px] text-[#d9001b] text-right cursor-pointer"
              @click="delImg(item)"
            >
              删除
            </div>
          </section>
        </div>
        <span
          v-if="!Array.isArray(curImg) && !isEdit"
          class="labelValue mt-[10px]"
          >-</span
        >
        <FileUpload
          v-if="isEdit"
          v-model="formData.imgList"
          :limit="imgLimit"
          :is-show-tip="false"
          :file-type="['jpg', 'png']"
          :file-size="10"
          file-list-type="Array"
        >
          <el-button type="default" class="uploadBtn mt-[10px]">
            <img
              src="@/assets/images/poManage/upload.png"
              alt="upload"
              class="w-[12px] h-[12px] mr-[4px]"
            />
            点击上传
          </el-button>
        </FileUpload>
      </div>

      <!-- 舆情事件 -->
      <div class="text-[#333333] mt-[20px] poEvent-container">
        <div class="labelTitle">舆情事件</div>
        <el-select
          v-if="isEdit"
          v-model="formData.poEvent"
          multiple
          placeholder="请选择舆情事件"
          clearable
        >
          <el-option
            v-for="item in poManageStore.ingPoEventList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <PoEvent v-else :poEvent="poInfo.poEvent" visible-key="visible3" />
      </div>

      <!-- <div
        class="text-[#333333] mt-[20px]"
        v-for="(item, index) in infoList"
        :key="index"
      >
        <div class="labelTitle">{{ item.label }}</div>
        <span class="labelValue">{{ item.value }}</span>
      </div> -->

      <!-- 上报单位 -->
      <div class="text-[#333333] mt-[20px] poEvent-container">
        <div class="labelTitle">上报单位</div>
        <EditWorkUnit v-if="isEdit" v-model="formData.workUnit" />
        <span v-else class="labelValue">{{ poInfo.workUnit }}</span>
      </div>

      <!-- 发布平台 -->
      <div class="text-[#333333] mt-[20px] poEvent-container">
        <div class="labelTitle">发布平台</div>
        <EditPlatformType v-if="isEdit" v-model="formData.platformType" />
        <span v-else class="labelValue">{{ poInfo.platformType }}</span>
      </div>

      <!-- 网民昵称 -->
      <div class="mt-[20px]">
        <div class="labelTitle">网民昵称</div>
        <el-input
          v-if="isEdit"
          v-model="formData.netizenNickname"
          placeholder="请输入网民昵称"
          clearable
        />
        <span v-else class="labelValue">{{ poInfo.netizenNickname }}</span>
      </div>

      <!-- 网民账号 -->
      <div class="mt-[20px]">
        <div class="labelTitle">网民账号</div>
        <el-input
          v-if="isEdit"
          v-model="formData.netizenAccount"
          placeholder="请输入网民账号"
          clearable
        />
        <span v-else class="labelValue">{{ poInfo.netizenAccount }}</span>
      </div>

      <!-- 报送时间 -->
      <div class="mt-[20px]">
        <div class="labelTitle">报送时间</div>
        <el-date-picker
          v-if="isEdit"
          v-model="formData.createTime"
          type="datetime"
          placeholder="请选择报送时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 380px"
        />
        <span v-else class="labelValue">{{ poInfo.createTime }}</span>
      </div>

      <!-- 发布时间 -->
      <div class="mt-[20px]">
        <div class="labelTitle">发布时间</div>
        <el-date-picker
          v-if="isEdit"
          v-model="formData.publicTime"
          type="datetime"
          placeholder="请选择发布时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 380px"
        />
        <span v-else class="labelValue">{{ poInfo.publicTime }}</span>
      </div>

      <!-- 舆情属地 -->
      <div class="text-[#333333] mt-[20px] poEvent-container">
        <div class="labelTitle">舆情属地</div>
        <el-cascader
          clearable
          v-if="isEdit"
          v-model="formData.happenLocation"
          :options="pcaTextArr"
          placeholder="请选择舆情属地"
          style="width: 100%"
        />
        <span v-else class="labelValue">{{ poInfo.happenLocation }}</span>
      </div>

      <!-- 舆情类型 -->
      <div class="text-[#333333] mt-[20px] poEvent-container">
        <div class="labelTitle">舆情类型</div>
        <EditPoType v-if="isEdit" v-model="formData.poType" />
        <span v-else class="labelValue">{{ poInfo.poType }}</span>
      </div>

      <!-- 媒体类型 -->
      <div class="text-[#333333] mt-[20px] poEvent-container">
        <div class="labelTitle">媒体类型</div>
        <el-select
          v-if="isEdit"
          v-model="formData.poMediaType"
          placeholder="请选择媒体类型"
        >
          <el-option
            v-for="item in mediaTypeOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <span v-else class="labelValue">{{
          getDict(poInfo.poMediaType, mediaTypeOption)
        }}</span>
      </div>

      <!-- 是否为敏感舆情 -->
      <div class="text-[#333333] mt-[20px] poEvent-container" v-if="isEdit">
        <div class="labelTitle">是否为敏感舆情</div>
        <el-radio-group v-model="formData.isSensitive">
          <el-radio label="是" value="1" />
          <el-radio label="否" value="0" />
        </el-radio-group>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import PoEvent from "../../PoEvent.vue";
import EditWorkUnit from "../../EditWorkUnit.vue";
import EditPlatformType from "../../EditPlatformType.vue";
import EditPoType from "../../EditPoType.vue";
import { IS_FIRST_STATUS, SENSITIVE_STATUS } from "../../../config/constant";
import { usePoManageStore } from "@/store/modules/poManage.js";
import useDictStore from "@/store/modules/dict";
import { pcaTextArr } from "element-china-area-data";
import { getDict } from "@/utils/dict.js";

const poManageStore = usePoManageStore();
poManageStore.getAllPoEventList();

const dictStore = useDictStore();
const mediaTypeOption = computed(
  () => dictStore.dict.find((ele) => ele.key === "media_type").value
);

const props = defineProps({
  isEdit: {
    type: Boolean,
    required: true,
  },
  poInfo: {
    type: Object,
    required: true,
  },
  formData: {
    type: Object,
    required: true,
  },
  modelValue: {
    type: Boolean,
    required: true,
  },
});

const scrollbarRef = ref();
const curImg = ref();
const { proxy } = getCurrentInstance();

// 抽屉打开时，获取已上传的图片
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      curImg.value = props.poInfo.poImg;
    }
  },
  {
    immediate: true,
  }
);

const isFirstF = computed(() => props.poInfo.isFirst === IS_FIRST_STATUS.YES);
const isSensitiveF = computed(
  () => props.poInfo.isSensitive === SENSITIVE_STATUS.YES
);
const imgLimit = computed(() => 5 - (curImg.value?.length ?? 0));
const infoList = computed(() => [
  { label: "上报单位", value: props.poInfo.workUnit },
  { label: "来源", value: props.poInfo.poFrom },
  { label: "发布平台", value: props.poInfo.platformType },
  { label: "网民昵称", value: props.poInfo.netizenNickname },
  { label: "网民账号", value: props.poInfo.netizenAccount },
  { label: "报送时间", value: props.poInfo.createTime },
  { label: "目标群", value: props.poInfo.targetGroup },
  { label: "发布时间", value: props.poInfo.publicTime },
  { label: "舆情属地", value: props.poInfo.happenLocation },
  { label: "舆情类型", value: props.poInfo.poType },
]);
const singleContentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 单个内容是否为图片

const contentIsImg = computed(() => (val) => {
  const contentArr = val.split("<br>") || [];
  return contentArr.every((i) => singleContentIsImg.value(i));
}); // 整个内容单独就是个图片或全是图片
const poContentF = computed(() => (val) => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter(
      (i) => !singleContentIsImg.value(i)
    );
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});

/**
 * 删除图片
 */
function delImg(item) {
  curImg.value = curImg.value.filter((ele) => ele !== item);
}

defineExpose({
  scrollbarRef,
  curImg,
});
</script>

<style lang="scss" scoped>
.poInfo-container {
  width: 100%;

  .poTitle {
    padding-bottom: 12px;
    border-bottom: 1px solid #f5f5f5;

    .firstTag,
    .sensitiveTag {
      width: 47px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 3px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 13px;
    }

    .firstTag {
      background: #e6ecff;
      color: #1749e0;
    }

    .sensitiveTag {
      background-color: #ffe8ef;
      color: #f82626;
    }

    .poTitleInput {
      .firstTag,
      .sensitiveTag {
        height: 18px;
        font-size: 12px;
        font-weight: 400;
      }
    }

    .line {
      display: inline-block;
      width: 1px;
      height: 100px;
    }
  }

  .labelTitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #1f2125;
    margin-bottom: 10px;
  }

  .requiredTag::before {
    color: var(--el-color-danger);
    content: "*";
    margin-right: 4px;
    font-weight: 400;
  }

  .poEvent-container :deep(.noData),
  .labelValue {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #3f434b;
  }

  // 舆情内容链接
  :deep(.linkText) {
    display: inline;
    color: #0052d9;

    &:visited {
      color: #0052d9;
    }

    .el-link__inner {
      display: inline;
    }
  }

  :deep(.linkInput) {
    .el-input__wrapper {
      padding-left: 1px;

      .linkPrefix {
        width: 38px;
        height: 32px;
        background: #eff0f1;
        border-radius: 3px 0px 0px 3px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .uploadImgWrapper {
    .labelTitle {
      margin-bottom: 0;
    }

    .uploadBtn {
      width: 94px;
      height: 30px;
      background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
      border-radius: 3px;
      border: 1px solid #d8d8d8;

      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
    }
  }
}
</style>
