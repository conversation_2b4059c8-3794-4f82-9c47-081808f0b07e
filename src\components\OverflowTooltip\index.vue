<template>
  <el-tooltip
    :content="content"
    :placement="placement"
    :show-after="300"
    :visible="visible"
  >
    <div
      @mouseover="
        visible = $event.target.scrollWidth > $event.target.clientWidth
      "
      @mouseleave="visible = false"
    >
      <slot></slot>
    </div>
  </el-tooltip>
</template>

<script setup>
defineProps({
  content: {
    type: String,
    default: '',
  },
  placement: {
    type: String,
    default: 'top',
  },
})

const visible = ref(false)
</script>

<style lang="scss" scoped></style>
