<?xml version="1.0" encoding="UTF-8"?>
<svg width="59px" height="49px" viewBox="0 0 59 49" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63 (92445) - https://sketch.com -->
    <title>编组 10</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FAFEFF" offset="0%"></stop>
            <stop stop-color="#88E9FF" offset="100%"></stop>
        </linearGradient>
        <filter x="-23.3%" y="-28.4%" width="146.7%" height="156.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.447058824   0 0 0 0 0.682352941   0 0 0 0 0.984313725  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="某事件大屏" transform="translate(-438.000000, -900.000000)" fill="url(#linearGradient-1)" fill-rule="nonzero">
            <g id="编组-10" transform="translate(438.000000, 900.000000)">
                <path d="M30.2560226,11.0157204 C30.7686916,11.0157204 31.2760292,11.0524261 31.7790339,11.1087316 C30.4102643,4.73537082 23.5965795,4.26325641e-14 15.8185566,4.26325641e-14 C7.12266514,4.26325641e-14 -1.17239551e-13,5.92605177 -1.17239551e-13,13.4521725 C-1.17239551e-13,17.7979766 2.36972309,21.3637031 6.32982814,24.1313787 L4.74770996,28.8895934 L10.277603,26.1170382 C12.2562097,26.5080462 13.843926,26.9113391 15.8184708,26.9113391 C16.3148835,26.9113391 16.806918,26.8865524 17.2952476,26.848306 C16.9863124,25.7914084 16.8069632,24.6838935 16.8069632,23.5337542 C16.807049,16.6232313 22.7416492,11.0157204 30.2560226,11.0157204 Z M21.6391044,6.40569291 C22.9897696,6.40569291 24.084638,7.500611 24.084638,8.85130791 C24.084638,10.2020048 22.9897651,11.2968777 21.6391044,11.2968777 C20.2883487,11.2968777 19.1934803,10.2019596 19.1934803,8.85130791 C19.1934803,7.50065618 20.2884391,6.40569291 21.6391044,6.40569291 Z M10.5714114,11.3461847 C9.22074609,11.3461847 8.12578732,10.2512666 8.12578732,8.90061486 C8.12578732,7.54996314 9.22075059,6.45504505 10.5714114,6.45504505 C11.922167,6.45504505 13.0170806,7.54996314 13.0170806,8.90061486 C13.0170806,10.2512214 11.922167,11.3461847 10.5714114,11.3461847 Z M44.5818669,23.3951906 C44.5818669,17.0710419 38.2536201,11.91595 31.145761,11.91595 C23.6192427,11.91595 17.691596,17.0710418 17.691596,23.3951906 C17.691596,29.7302644 23.6192428,34.874115 31.145761,34.874115 C32.7208354,34.874115 34.309907,34.476515 35.8920252,34.0799497 L40.2310972,36.4564817 L39.0411346,32.5034024 C42.216436,30.1213627 44.5818669,26.9626747 44.5818669,23.3951906 Z M27.0123611,21.6223284 C26.0505125,21.6223284 25.270855,20.8425218 25.270855,19.8807545 C25.270855,18.9188969 26.0505577,18.139271 27.0123611,18.139271 C27.9741646,18.139271 28.7538672,18.918942 28.7538672,19.8807545 C28.7539621,20.842567 27.9741691,21.6223284 27.0123611,21.6223284 Z M35.708343,21.7211184 C34.746404,21.7211184 33.9667918,20.941357 33.9667918,19.9796349 C33.9667918,19.0177321 34.7464492,18.2381514 35.708343,18.2381514 C36.6701465,18.2381514 37.4498491,19.0177772 37.4498491,19.9796349 C37.4498988,20.941357 36.670151,21.7211184 35.708343,21.7211184 Z" id="形状" opacity="0.0773577009"></path>
                <g id="微信" filter="url(#filter-2)" transform="translate(8.956882, 7.989958)">
                    <path d="M30.2991407,11.0257626 C30.8118098,11.0257626 31.3191474,11.0624683 31.8221521,11.1187739 C30.4533825,4.74541305 23.6396977,0.0100422327 15.8616748,0.0100422327 C7.16578328,0.0100422327 0.0431181475,5.93609401 0.0431181475,13.4622147 C0.0431181475,17.8080189 2.41284124,21.3737454 6.37294628,24.1414209 L4.7908281,28.8996356 L10.3207212,26.1270805 C12.2993279,26.5180884 13.8870441,26.9213813 15.861589,26.9213813 C16.3580016,26.9213813 16.8500361,26.8965946 17.3383657,26.8583482 C17.0294306,25.8014506 16.8500813,24.6939358 16.8500813,23.5437965 C16.8501671,16.6332735 22.7847673,11.0257626 30.2991407,11.0257626 Z M21.6822225,6.41573514 C23.0328878,6.41573514 24.1277562,7.51065323 24.1277562,8.86135014 C24.1277562,10.2120471 23.0328833,11.30692 21.6822225,11.30692 C20.3314669,11.30692 19.2365985,10.2120019 19.2365985,8.86135014 C19.2365985,7.51069842 20.3315572,6.41573514 21.6822225,6.41573514 Z M10.6145295,11.3562269 C9.26386424,11.3562269 8.16890547,10.2613088 8.16890547,8.9106571 C8.16890547,7.56000537 9.26386874,6.46508728 10.6145295,6.46508728 C11.9652851,6.46508728 13.0601987,7.56000537 13.0601987,8.9106571 C13.0601987,10.2612636 11.9652851,11.3562269 10.6145295,11.3562269 Z M44.624985,23.4052329 C44.624985,17.0810841 38.2967382,11.9259922 31.1888792,11.9259922 C23.6623609,11.9259922 17.7347142,17.0810841 17.7347142,23.4052329 C17.7347142,29.7403066 23.6623609,34.8841572 31.1888792,34.8841572 C32.7639535,34.8841572 34.3530252,34.4865572 35.9351433,34.0899919 L40.2742154,36.4665239 L39.0842528,32.5134446 C42.2595542,30.131405 44.624985,26.9727169 44.624985,23.4052329 Z M27.0554793,21.6323706 C26.0936306,21.6323706 25.3139732,20.8525641 25.3139732,19.8907967 C25.3139732,18.9289391 26.0936758,18.1493132 27.0554793,18.1493132 C28.0172827,18.1493132 28.7969854,18.9289842 28.7969854,19.8907967 C28.7970802,20.8526092 28.0172872,21.6323706 27.0554793,21.6323706 Z M35.7514612,21.7311606 C34.7895222,21.7311606 34.0099099,20.9513993 34.0099099,19.9896771 C34.0099099,19.0277743 34.7895674,18.2481936 35.7514612,18.2481936 C36.7132646,18.2481936 37.4929673,19.0278195 37.4929673,19.9896771 C37.493017,20.9513993 36.7132691,21.7311606 35.7514612,21.7311606 Z" id="形状"></path>
                </g>
            </g>
        </g>
    </g>
</svg>