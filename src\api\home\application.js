import portalRequest from '@/utils/portalRequest.js';

/**
 * @description 获取应用列表
 * @param {*} data
 * @returns {Promise<*>}
 * */
export function getAppList(data) {
  return portalRequest({
    url: '/backend/app_info/list',
    method: 'get',
    params: data,
  });
}

/**
 * @description 修改应用是否为常用应用
 * @method post url: /backend/app_info/edit
 * @param {*} data
 * @returns {Promise<*>}
 * */
export function editApp(data) {
  return portalRequest({
    url: '/backend/app_info/edit',
    method: 'post',
    data,
  });
}

/** 一键添加应用 */
export function addAllApp(params) {
  return portalRequest({
    url: '/backend/app_info/switchAll',
    method: 'post',
    params,
  });
}
