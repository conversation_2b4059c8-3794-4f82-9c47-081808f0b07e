<template>
  <div class="errPage-container">
    <div class="flex flex-col items-center">
      <img
        class="w-[319px] h-[218px] mb-[48px]"
        src="@/assets/low_browser_images/upgrades-img.png"
        alt=""
      />
      <span class="text-[40px] text-[#262626] leading-[56px] mb-[27px] font-600">
        请升级您的浏览器，以便我们更好的为您提供服务！
      </span>
      <span class="text-[29px] text-[#B8B8B8] leading-[41px] font-600 mb-[70px]">
        <!-- 请根据您的 Windows 系统版本下载浏览器 -->
        下载通用版浏览器安装包，适用于 Windows 7、10 和 11 系统
      </span>
      <div class="flex gap-[86px]">
        <div
          v-for="(item, index) in browserData"
          :key="index"
          class="flex flex-col items-center"
        >
          <img
            class="w-[80px] h-[91px] mb-[3px]"
            src="@/assets/low_browser_images/google-img.png"
            alt="谷歌Logo"
          />
          <!-- <span class="text-[20px] text-[#333] font-600 mb-[16px]">{{ item.title }}</span> -->
          <span
            class="text-[16px] text-[#1676FC] cursor-pointer font-600"
            @click="handleDownload(item.url)"
          >
            下载
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  const browserData = [
    {
      title: 'chrome',
      url: '/low-browser/chrome.exe',
    },
    // {
    //   title: 'Windows 7（64位）',
    //   url: '/low-browser/windows7_64.exe',
    // },
    // {
    //   title: 'Windows 10/11（32位）',
    //   url: '/low-browser/windows10_32.exe',
    // },
    // {
    //   title: 'Windows 10/11（64位）',
    //   url: '/low-browser/windows10_64.exe',
    // },
  ];
  function handleDownload(url) {
    window.location.href = url;
  }
</script>

<style lang="scss" scoped>
  .errPage-container {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    padding-top: 100px;
  }
</style>
