<template>
  <div class="publicOpinionTable-container" ref="tableContainerRef">
    <CommonTable
      :columns="tableColumns"
      :data="tableData"
      :loading="tableLoading"
      :show-default-btn="false"
      :show-operation-column="true"
      :operation-column="operationColumnWidth"
      no-padding
      :max-height="tableHeightF"
      :highlight-current-row="false"
      :row-class-name="rowClassName"
      :row-key="rowKey"
      @handle-selection-change="handleSelectionChange"
      @handle-row-click="handleRowClick"
      @updateColumns="updateColumns"
      :isStripe="true"
      :is-column-drag-sort="isColumnDragSort"
    >
      <template #poId="{ row }">
        <img
          v-if="articleIsInvalid(row.isInvalid)"
          src="@/assets/images/poManage/invalid.svg"
          alt=""
          class="invalidItemWrapper"
        />
        <img
          v-if="
            articleIsRumor(row.rumorStatus) && !articleIsInvalid(row.isInvalid)
          "
          src="@/assets/images/rumor-bg.svg"
          alt=""
          class="invalidItemWrapper"
        />
        <div class="whitespace-normal break-all">{{ row.poId }}</div>
      </template>

      <template #isFirst="{ row }">
        <div>{{ firstLabel(row.isFirst) }}</div>
      </template>

      <template #poName="{ row }">
        <div class="poContentWrapper">{{ row.poName || "-" }}</div>
      </template>

      <!-- 内容可能为 文本/图片/带换行聚合体 -->
      <template #poContent="{ row }">
        <ImagePreview
          v-if="contentIsImg(row.poContent)"
          :src="row.poContent"
          :width="100"
          :height="100"
          @click.stop
        />
        <div v-else class="poContentWrapper whitespace-pre-wrap!">
          {{ poContentF(row.poContent) }}
        </div>
      </template>

      <template #poLink="{ row }">
        <div v-if="row.poLink" class="flex">
          <div class="tagWrapper truncate">
            <img
              src="@/assets/images/poManage/link.png"
              alt="link"
              class="w-[10px] h-[10px] mr-[4px]"
            />
            <el-link
              class="linkText truncate"
              :underline="false"
              :href="row.poLink"
              target="blank"
              @click.stop
              >{{ row.poLink }}</el-link
            >
          </div>
        </div>
        <span v-else>-</span>
      </template>

      <template #poEvent="{ row }">
        <PoEvent
          :poEvent="row.poEvent"
          :isShowAddEvent="showAddEventF(row)"
          @showEvent="showEvent(row)"
        />
      </template>

      <template #workUnit="{ row }">
        <div @click.stop>
          <el-dropdown
            trigger="click"
            v-if="
              row.workUnit !== '-' &&
              row.linkUrlCount &&
              Number(row.linkUrlCount) > 0
            "
            @visible-change="(visible) => handleClickDropdown(visible, row)"
          >
            <template #default>
              <div class="mr-[4px] text-[14px] flex items-center">
                {{ row.workUnit }}<el-icon><CaretBottom /></el-icon>
              </div>
            </template>

            <template #dropdown>
              <el-scrollbar max-height="320px">
                <div class="p-[20px] flex flex-col gap-y-[18px]">
                  <section
                    class="flex flex-col gap-y-[4px]"
                    v-for="(item, index) in linkReportRecords"
                    :key="index"
                  >
                    <div class="subDeptName">
                      {{ item.deptName }}
                    </div>
                    <div class="createTime">{{ item.reporterTime }}</div>
                  </section>
                </div>
              </el-scrollbar>
            </template>
          </el-dropdown>
          <span v-else class="mr-[4px]">{{ row.workUnit }} </span>
        </div>
      </template>

      <template #isSensitive="{ row }">
        {{ isSensitiveF(row.isSensitive) }}
      </template>

      <template #netizenNickname="{ row }">
        <NetizenColumn
          :nickname="row.netizenNickname"
          :account-id="row.netizenAccountId"
        />
      </template>

      <template #publicTime="{ row }">
        <div class="whitespace-normal break-all">{{ row.publicTime }}</div>
      </template>

      <template #poImg="{ row }">
        <ImagePreview
          v-if="Array.isArray(row.poImg)"
          :src="row.poImg[0]"
          :width="100"
          :height="100"
          @click.stop
        />
        <span v-else>-</span>
      </template>

      <template #articleStatus="{ row }">
        <span
          class="truncate"
          :style="articleStatusF(row.articleStatus).style"
          >{{ articleStatusF(row.articleStatus).text }}</span
        >
      </template>
      <template #handleStatus="{ row }">
        <div class="flex-center">
          <div
            class="truncate statusClass"
            :style="handleStatusF(row.handleStatus).style"
          >
            {{ handleStatusF(row.handleStatus).text }}
          </div>
        </div>
      </template>

      <template #createTime="{ row }">
        <div class="whitespace-normal break-all">{{ row.createTime }}</div>
      </template>

      <!-- 赋分 -->
      <template #score="{ row }">
        <span class="text-[#03B615] truncate">{{ row.score }}</span>
      </template>

      <!-- 媒体类型 -->
      <template #poMediaType="{ row }">
        <span>{{ getDict(row.poMediaType, mediaTypeOption) }}</span>
      </template>

      <!-- 处理状态 -->
      <template #handleStatus2="{ row }">
        <div class="flex-center">
          <div
            class="truncate statusClass"
            :style="handleStatus2F(row.handleStatus2).style"
          >
            {{ handleStatus2F(row.handleStatus2).text }}
          </div>
        </div>
      </template>

      <template #operation="{ row }">
        <slot name="operation" :row="row">
          <div
            class="operationWrapper"
            v-if="
              !articleIsDel(row.articleStatus) &&
              !articleIsInvalid(row.isInvalid) &&
              !articleIsRumor(row.rumorStatus)
            "
          >
            <el-button
              link
              style="color: #0070ff"
              @click.stop="$emit('showDialog', '研判', row)"
              >研判</el-button
            >
            <el-button
              v-if="row.handleStatus === HANDLE_STATUS.WAIT_HANDLE"
              link
              style="color: #0070ff"
              @click.stop="onRumorOrDel('rumor', row)"
              >谣言鉴定</el-button
            >
            <el-button
              v-if="row.handleStatus === HANDLE_STATUS.WAIT_HANDLE"
              style="color: #0070ff"
              link
              @click.stop="disableData(row.id)"
              >无效</el-button
            >

            <el-dropdown trigger="click">
              <el-button style="color: #0070ff" link @click.stop
                >更多</el-button
              >
              <template #dropdown>
                <el-dropdown-menu>
                  <template v-if="!articleIsRumor(row.rumorStatus)">
                    <!-- 落查、核报、处置、转阅 -->
                    <el-dropdown-item>
                      <el-button
                        style="color: #0070ff"
                        link
                        @click="$emit('showDialog', TRANSFER_TYPE.CHECK, row)"
                      >
                        {{ TRANSFER_TYPE.CHECK }}
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        style="color: #0070ff"
                        link
                        @click="$emit('showDialog', TRANSFER_TYPE.RESOLVE, row)"
                      >
                        {{ TRANSFER_TYPE.RESOLVE }}
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        style="color: #0070ff"
                        link
                        @click="$emit('showDialog', TRANSFER_TYPE.REPORT, row)"
                      >
                        {{ TRANSFER_TYPE.REPORT }}
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        style="color: #0070ff"
                        link
                        @click="$emit('showDialog', TRANSFER_TYPE.VIEW, row)"
                      >
                        {{ TRANSFER_TYPE.VIEW }}
                      </el-button>
                    </el-dropdown-item>

                    <el-dropdown-item
                      v-if="row.handleStatus === HANDLE_STATUS.WAIT_HANDLE"
                    >
                      <el-button
                        link
                        style="color: #0070ff"
                        @click="onRumorOrDel('del', row)"
                        >删帖判定</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        v-if="poManageStore.isWangAn"
                        v-hasPermi="['business:opinionReport:score']"
                        style="color: #0070ff"
                        link
                        @click="$emit('showDialog', TRANSFER_TYPE.SCORE, row)"
                      >
                        {{ TRANSFER_TYPE.SCORE }}
                      </el-button>
                    </el-dropdown-item>
                  </template>
                  <el-dropdown-item>
                    <el-button
                      style="color: #0070ff"
                      link
                      @click="$emit('showDrawer', 'edit', row)"
                      >编辑</el-button
                    >
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="row.handleStatus === HANDLE_STATUS.WAIT_HANDLE"
                  >
                    <el-button
                      style="color: #0070ff"
                      link
                      @click="handleDelete(row.id)"
                      >删除</el-button
                    >
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button
                      style="color: #0070ff"
                      link
                      @click="handleWaterArmy(row)"
                      >水军标记</el-button
                    >
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div v-else>
            <el-button
              link
              style="color: #0070ff"
              @click.stop="handleRestore(row.id)"
              >恢复</el-button
            >
          </div>
        </slot>
      </template>
    </CommonTable>

    <div class="flex justify-between items-center">
      <!-- 批量操作 -->
      <div class="flex items-center text-[14px]">
        <span class="mr-[30px]" v-if="showMulSelect"
          >已选择{{ selectedMemberCount }}项</span
        >
      </div>
      <!-- 分页 -->
      <pagination
        v-model:page="pageObj.pageNum"
        v-model:limit="pageObj.pageSize"
        :total="pageObj.total"
        @pagination="$emit('refreshData')"
      />
    </div>

    <!-- 添加事件弹窗 -->
    <AddEventDialog
      v-model="showEventDialog"
      :row-id="rowId"
      :row-event="rowEvent"
      @refreshData="$emit('refreshData')"
    />
  </div>
</template>

<script setup>
import { CaretBottom } from "@element-plus/icons-vue";

import CommonTable from "@/components/commonTable/index.vue";
import PoEvent from "./PoEvent.vue";
import NetizenColumn from "./NetizenColumn.vue";
import { useFetchTableData } from "@/hooks/useFetchTableData";
import { useTableHeight } from "@/hooks/useTableHeight";
import {
  IS_FIRST_STATUS_LIST,
  SENSITIVE_STATUS_LIST,
  INVALID_STATUS,
  RUMOR_STATUS,
  ARCTICLE_STATUS,
  ARCTICLE_STATUS_LIST,
  HANDLE_STATUS_LIST,
  HANDLE2_STATUS_LIST,
} from "../config/constant.js";
import { TRANSFER_TYPE } from "../config/mapRel";
import {
  disablePoInfo,
  getLinkReportRecords,
  opinionDelete,
} from "@/api/poManage/poInfo";
import { usePoManageStore } from "@/store/modules/poManage.js";
import AddEventDialog from "./AddEventDialog.vue";
import {
  changeRumorStatus,
  changeSourceStatus,
  changeWaterArmy,
} from "@/api/poManage/poInfo";
import { HANDLE_STATUS } from "../config/constant.js";
import { getDict, useDict } from "@/utils/dict.js";

const props = defineProps({
  // 获取数据的接口相关信息
  interfaceInfo: {
    type: Object,
    default: () => ({}),
  },
  // 表格列配置
  tableColumns: {
    type: Array,
    default: () => [],
  },
  // 操作栏宽度
  operationColumnWidth: {
    type: Number,
    default: 220,
  },
  // 是否展示多选列
  showMulSelect: {
    type: Boolean,
    default: true,
  },
  // 表格最大高度
  tableMaxHeight: {
    type: Number,
    default: 0,
  },
  rowKey: {
    type: [String, Function],
    default: "id",
  },
  // 是否展示事件添加按钮
  showAddEvent: {
    type: Boolean,
    default: false,
  },
  isColumnDragSort: {
    type: Boolean,
    default: false,
  },
  mediaTypeOption: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits([
  "mulSelect",
  "showDrawer",
  "showDialog",
  "refreshData",
  "updateColumns",
]);

// 如果存在tab切换，重新计算表格高度
const activeTabName = inject("activeTabName", ref(""));
watch(activeTabName, async (val) => {
  if (val) {
    await nextTick(); // 等待元素渲染完成
    getTableHeight();
  }
});

const { proxy } = getCurrentInstance();
const { tableData, tableLoading, pageObj, getTableData } = useFetchTableData(
  props.interfaceInfo
);
const poManageStore = usePoManageStore();

const linkReportRecords = ref([]);

const isClear = ref(false);

const tableContainerRef = ref();
const { tableHeight, getTableHeight } = useTableHeight(
  tableContainerRef,
  32 + 15
);
const tableHeightF = computed(() =>
  props.tableMaxHeight ? props.tableMaxHeight : tableHeight.value
); // 兼容固定高度和自适应高度
const selectedMemberList = ref([]); // 已选人员信息
const selectedMemberCount = computed(() => selectedMemberList.value.length); // 已选个数

const firstLabel = computed(
  () => (val) =>
    IS_FIRST_STATUS_LIST.find((ele) => ele.value === val)?.label || "-"
);
const isSensitiveF = computed(
  () => (val) =>
    SENSITIVE_STATUS_LIST.find((i) => i.value === val)?.label || "-"
);
const articleStatusF = computed(() => (val) => {
  const obj = ARCTICLE_STATUS_LIST.find((i) => i.value === val);
  return { text: obj?.label || "-", style: { color: obj?.color || "#333333" } };
});
const handleStatusF = computed(() => (val) => {
  const obj = HANDLE_STATUS_LIST.find((i) => i.value === val);
  return {
    text: obj?.label || "-",
    style: { color: obj?.color || "#333333", background: obj?.bgColor },
  };
});
const articleIsDel = computed(
  () => (val) => val === ARCTICLE_STATUS.FINISH_DEL
); // 当前帖子是否已删除
const articleIsInvalid = computed(() => (val) => val === INVALID_STATUS.YES); // 当前帖子是否无效
const articleIsRumor = computed(() => (val) => val === RUMOR_STATUS.YES); // 当前帖子是否是谣言
const showAddEventF = computed(
  () => (row) =>
    props.showAddEvent &&
    !articleIsDel.value(row.articleStatus) &&
    !articleIsRumor.value(row.rumorStatus) &&
    !articleIsInvalid.value(row.isInvalid)
);
const singleContentIsImg = computed(
  () => (val) =>
    val?.startsWith("/profile/upload") && /\.(png|jpe?g)$/i.test(val)
); // 单个内容是否为图片

const contentIsImg = computed(() => (val) => {
  const contentArr = val.split("<br>") || [];
  return contentArr.every((i) => singleContentIsImg.value(i));
}); // 整个内容单独就是个图片或全是图片
const poContentF = computed(() => (val) => {
  if (val) {
    const contentArr = val.split("<br>") || [];
    const contentWithoutImg = contentArr.filter(
      (i) => !singleContentIsImg.value(i)
    );
    return contentWithoutImg.join("\n");
  } else {
    return "-";
  }
});
const handleStatus2F = computed(() => (val) => {
  const obj = HANDLE2_STATUS_LIST.find((i) => i.value.includes(val));
  return {
    text: obj?.label || "-",
    style: { color: obj?.color || "#333333", background: obj?.bgColor },
  };
});

const handleDelete = async (id) => {
  proxy.$modal
    .confirm(`是否确认删除该舆情?`)
    .then(async () => {
      const res = await opinionDelete(id);
      if (res.code == 200) {
        proxy.$message.success("操作成功");
        emit("refreshData");
      }
    })
    .catch(() => {});
};

const handleRestore = async (id) => {
  // 将 删帖判定、谣言鉴定和无效 都恢复正常
  await Promise.all([
    changeRumorStatus({
      reportId: id,
      rumorStatus: "0",
    }),
    changeSourceStatus({
      reportId: id,
      sourceStatus: "0",
    }),
    disablePoInfo({
      ids: [id],
      status: "0",
    }),
  ]);

  // if (res.code === 200) {
  proxy.$message.success("操作成功");
  emit("refreshData");
  // }
};

/**
 * 报送单位下拉点击事件
 */
async function handleClickDropdown(visible, row) {
  if (visible) {
    const res = await getLinkReportRecords(row?.id);
    if (res?.code === 200) {
      linkReportRecords.value = res?.data;
    }
  }
}

const updateColumns = (newColumns) => {
  emit("updateColumns", newColumns);
};

/**
 * 给特定表格行加 class
 */
function rowClassName({ row }) {
  if (articleIsDel.value(row.articleStatus)) {
    return "delItemWrapper";
  }
}

function handleWaterArmy(row) {
  // console.log("id", id);
  // 点击后，弹出二次确认框，是否将该舆情信息的发帖网民标记为网络水军。若账号及所属平台已经在水军库中，则提示该账号已自动标记为疑似水军，无需重复标记，不进入水军库。
  proxy.$modal
    .confirm(`是否确认将该舆情信息的发帖网民标记为网络水军？`)
    .then(async () => {
      // 调用接口，将该舆情信息的发帖网民标记为网络水军。
      const params = {};
      const res = await changeWaterArmy(params);
      if (res.code === 200) {
        proxy.$message.success("操作成功");
        emit("refreshData");
      }
    });
}

/**
 * 表格多选
 */
function handleSelectionChange(selectedList) {
  selectedMemberList.value = selectedList;
  emit("mulSelect", selectedList);
}

async function onRumorOrDel(judgeType, row) {
  if (judgeType === "rumor") {
    const res = await changeRumorStatus({ reportId: row.id, rumorStatus: "1" });
    if (res.code === 200) {
      proxy.$message.success("处理成功");
      emit("refreshData");
      isClear.value = true;
    }
  } else {
    const res = await changeSourceStatus({
      reportId: row.id,
      sourceStatus: "1",
    });
    if (res.code === 200) {
      proxy.$message.success("处理成功");
      emit("refreshData");
      isClear.value = true;
    }
  }
}

/**
 * 点击表格行
 */
function handleRowClick(row) {
  // if (
  //   articleIsDel.value(row.articleStatus) ||
  //   articleIsInvalid.value(row.isInvalid)
  // ) 已删除、无效 的帖子不可点击
  // if (articleIsInvalid.value(row.isInvalid)) return; // 帖子已删除的帖子不可点击
  emit("showDrawer", "view", row);
}

/**
 * 无效数据
 */
async function disableData(id) {
  const res = await disablePoInfo({ ids: [id], status: "1" });
  if (res.code === 200) {
    proxy.$message.success("操作成功");
    emit("refreshData");
    isClear.value = true;
  }
}

const showEventDialog = ref(false);
const rowId = ref("");
const rowEvent = ref([]);
/**
 * 展示添加事件弹窗
 */
function showEvent({ id, poEvent }) {
  showEventDialog.value = true;
  rowId.value = id;
  rowEvent.value = poEvent;
}

defineExpose({
  tableData,
  getTableData,
});
</script>

<style lang="scss" scoped>
.publicOpinionTable-container {
  width: 100%;
  height: 100%;
}

.poContentWrapper {
  white-space: normal;
  word-break: break-all;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4; /* 控制几行打点 */
  line-clamp: 4;
  overflow: hidden;
}

.tagWrapper {
  height: 22px;
  line-height: 22px;
  display: flex;
  align-items: center;
  padding-left: 8px;
  padding-right: 8px;
  background: #ffffff;
  box-shadow: 0px 1px 3px 0px rgba(222, 224, 232, 0.5);
  border-radius: 14px;
  border: 1px solid #e6e6e9;
}

.subDeptName {
  width: 100%;
  height: 20px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1f2329;
  line-height: 20px;
  text-align: left;
  font-style: normal;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.createTime {
  width: 100%;
  height: 20px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #8d949e;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}

// 舆情内容链接
:deep(.linkText) {
  display: inline;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #0070ff;

  &:visited {
    color: #0070ff;
  }
  .el-link__inner {
    display: inline;
  }
}

.statusClass {
  width: 50px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  border-radius: 3px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
}

// 无效的表格行
:deep(.invalidItemWrapper) {
  position: absolute;
  top: 10px;
  right: 0;
  width: 57px;
}

// 已删除的表格行
:deep(.delItemWrapper) {
  opacity: 0.4;
}

.operationWrapper {
  display: flex;
  align-items: center;
  :deep(.el-dropdown) {
    margin-left: 12px;
  }
}

.pagination-container {
  margin-top: 0;
}
</style>
